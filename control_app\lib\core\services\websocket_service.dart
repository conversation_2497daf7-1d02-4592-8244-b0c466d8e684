import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../models/websocket_message.dart';
import '../models/device_model.dart';
import 'remote_command_manager.dart';

/// WebSocket连接状态
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error
}

/// WebSocket服务类
/// 负责与网关服务建立WebSocket连接，处理消息收发
class WebSocketService {
  static final WebSocketService _instance = WebSocketService._internal();
  static WebSocketService get instance => _instance;
  factory WebSocketService() => _instance;
  WebSocketService._internal();

  // 原生WebSocket连接实例
  WebSocketChannel? _channel;
  
  // 连接状态
  WebSocketConnectionState _connectionState = WebSocketConnectionState.disconnected;
  
  // 流控制器
  final StreamController<WebSocketConnectionState> _connectionStateController = 
      StreamController<WebSocketConnectionState>.broadcast();
  final StreamController<WebSocketMessage> _messageController = 
      StreamController<WebSocketMessage>.broadcast();
  final StreamController<List<Device>> _deviceListController = 
      StreamController<List<Device>>.broadcast();
  final StreamController<Device> _deviceUpdateController = 
      StreamController<Device>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();

  // 配置参数
  String? _gatewayUrl;
  String? _authToken;
  Map<String, dynamic>? _clientInfo;
  
  // 重连参数
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  final int _maxReconnectAttempts = 5;
  final Duration _reconnectDelay = const Duration(seconds: 3);
  
  // 心跳参数
  Timer? _heartbeatTimer;
  final Duration _heartbeatInterval = const Duration(seconds: 30);
  
  // Getters
  WebSocketConnectionState get connectionState => _connectionState;
  bool get isConnected => _connectionState == WebSocketConnectionState.connected;
  Stream<WebSocketConnectionState> get connectionStateStream => _connectionStateController.stream;
  Stream<WebSocketMessage> get messageStream => _messageController.stream;
  Stream<List<Device>> get deviceListStream => _deviceListController.stream;
  Stream<Device> get deviceUpdateStream => _deviceUpdateController.stream;
  Stream<String> get errorStream => _errorController.stream;

  /// 初始化WebSocket连接
  Future<void> initialize({
    required String gatewayUrl,
    String? authToken,
    Map<String, dynamic>? clientInfo,
  }) async {
    _gatewayUrl = gatewayUrl;
    _authToken = authToken;
    _clientInfo = clientInfo ?? {
      'type': 'control',
      'platform': Platform.operatingSystem,
      'version': '1.0.0',
      'name': 'Control App',
    };
    
    // 初始化远程命令管理器
    _commandManager = RemoteCommandManager.instance;
    
    await connect();
  }



  /// 建立WebSocket连接
  Future<void> connect() async {
    if (_connectionState == WebSocketConnectionState.connecting ||
        _connectionState == WebSocketConnectionState.connected) {
      return;
    }

    _updateConnectionState(WebSocketConnectionState.connecting);
    
    try {
       // 构建WebSocket URL
       if (_gatewayUrl == null) {
         throw Exception('Gateway URL not set');
       }
       String wsUrl = _gatewayUrl!.replaceFirst('http://', 'ws://').replaceFirst('https://', 'wss://');
       if (!wsUrl.endsWith('/ws')) {
         wsUrl = wsUrl.endsWith('/') ? '${wsUrl}ws' : '$wsUrl/ws';
       }
      
      // 添加查询参数
      final uri = Uri.parse(wsUrl);
      final queryParams = <String, String>{
        'clientType': 'control',
        if (_authToken != null) 'token': _authToken!,
        if (_clientInfo != null) ..._clientInfo!.map((k, v) => MapEntry(k, v.toString())),
      };
      
      final wsUri = uri.replace(queryParameters: queryParams);
      
      debugPrint('Connecting to WebSocket: $wsUri');
      
      _channel = WebSocketChannel.connect(wsUri);
      _setupEventHandlers();
      
      debugPrint('WebSocket connection initiated');
      
    } catch (e) {
      debugPrint('WebSocket connection error: $e');
      _updateConnectionState(WebSocketConnectionState.error);
      _errorController.add('Connection failed: $e');
      _scheduleReconnect();
    }
  }

  /// 设置事件处理器
  void _setupEventHandlers() {
    if (_channel == null) return;

    // 监听WebSocket消息流
    _channel!.stream.listen(
      (data) {
        debugPrint('WebSocket message received: $data');
        
        // 首次收到消息表示连接成功
        if (_connectionState != WebSocketConnectionState.connected) {
          debugPrint('WebSocket connected to gateway');
          _updateConnectionState(WebSocketConnectionState.connected);
          _reconnectAttempts = 0;
          _startHeartbeat();
          
          // 发送客户端注册消息
          _sendClientRegistration();
        }
        
        _handleMessage(data);
      },
      onError: (error) {
        debugPrint('WebSocket error: $error');
        _updateConnectionState(WebSocketConnectionState.error);
        _errorController.add('Connection error: $error');
        _scheduleReconnect();
      },
      onDone: () {
        debugPrint('WebSocket disconnected from gateway');
        _updateConnectionState(WebSocketConnectionState.disconnected);
        _stopHeartbeat();
        _scheduleReconnect();
      },
    );
  }

  /// 发送客户端注册消息
  void _sendClientRegistration() {
    // 发送设备注册消息，兼容网关期望的格式
    final registrationData = {
      'type': 'device_register',
      'payload': _clientInfo,
      'senderId': _clientInfo?['deviceId'] ?? 'control_app',
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    _sendRawMessage(registrationData);
  }

  /// 处理接收到的消息
  void _handleMessage(dynamic data) {
    try {
      // 解析JSON消息
      Map<String, dynamic> jsonData;
      if (data is String) {
        jsonData = json.decode(data);
      } else {
        jsonData = data;
      }
      
      final message = WebSocketMessage.fromJson(jsonData);
      _messageController.add(message);
      
      // 根据消息类型进行特殊处理
      switch (message.type) {
        case WebSocketMessageType.welcome:
          _handleWelcomeMessage(message);
          break;
        case WebSocketMessageType.device_registered:
          _handleDeviceRegisteredMessage(message);
          break;
        case WebSocketMessageType.heartbeat:
          _handleHeartbeat(message);
          break;
        case WebSocketMessageType.heartbeat_ack:
          _handleHeartbeatAck(message);
          break;
        case WebSocketMessageType.pong:
          _handlePongMessage(message);
          break;
        case WebSocketMessageType.device:
          _handleDeviceMessage(message);
          // 如果是设备列表消息，也更新设备列表流
          if (message.action == WebSocketMessageAction.list && message.data != null) {
            _handleDeviceList(message.data!);
          }
          break;
        case WebSocketMessageType.capture:
          _handleCaptureMessage(message);
          break;
        case WebSocketMessageType.discovery:
          _handleDiscoveryMessage(message);
          break;
        case WebSocketMessageType.error:
          _handleErrorMessage(message);
          break;
        default:
          debugPrint('未处理的消息类型: ${message.type}');
          break;
      }
    } catch (e) {
      debugPrint('Error parsing message: $e');
      _errorController.add('Message parsing error: $e');
    }
  }

  /// 处理设备列表
  void _handleDeviceList(dynamic data) {
    try {
      final List<dynamic> deviceList = data['devices'] ?? [];
      final devices = deviceList.map((deviceData) => Device.fromJson(deviceData)).toList();
      _deviceListController.add(devices);
    } catch (e) {
      debugPrint('Error parsing device list: $e');
      _errorController.add('Device list parsing error: $e');
    }
  }

  /// 处理设备状态更新
  void _handleDeviceUpdate(dynamic data) {
    try {
      final device = Device.fromJson(data);
      _deviceUpdateController.add(device);
    } catch (e) {
      debugPrint('Error parsing device update: $e');
      _errorController.add('Device update parsing error: $e');
    }
  }

  /// 处理截图结果
  void _handleCaptureResult(dynamic data) {
    final message = WebSocketMessage(
      type: WebSocketMessageType.capture,
      action: WebSocketMessageAction.result,
      data: data,
      timestamp: DateTime.now(),
    );
    _messageController.add(message);
  }

  /// 处理错误消息
  void _handleError(dynamic data) {
    final errorMessage = data['message'] ?? 'Unknown error';
    _errorController.add(errorMessage);
  }

  /// 处理心跳消息
  void _handleHeartbeat(WebSocketMessage message) {
    if (message.action == WebSocketMessageAction.ping) {
      // 响应心跳
      final pongMessage = WebSocketMessage(
        type: WebSocketMessageType.heartbeat,
        action: WebSocketMessageAction.pong,
        data: {'timestamp': DateTime.now().millisecondsSinceEpoch},
        timestamp: DateTime.now(),
      );
      _sendMessage(pongMessage);
    }
  }

  /// 处理设备消息
  void _handleDeviceMessage(WebSocketMessage message) {
    // 设备相关消息的特殊处理
    debugPrint('Device message received: ${message.action}');
    
    switch (message.action) {
      case WebSocketMessageAction.list:
        if (message.data != null) {
          _handleDeviceList(message.data!);
        }
        break;
      case WebSocketMessageAction.update:
        if (message.data != null) {
          _handleDeviceUpdate(message.data!);
        }
        break;
      case WebSocketMessageAction.powerOn:
      case WebSocketMessageAction.powerOff:
      case WebSocketMessageAction.restart:
        _handleDeviceControlResponse(message.data);
        break;
      case WebSocketMessageAction.executeCommand:
        _handleRemoteCommandResponse(message.data);
        break;
      case WebSocketMessageAction.batchOperation:
        _handleBatchOperationResponse(message.data);
        break;
      default:
        debugPrint('Unknown device action: ${message.action}');
    }
  }

  /// 处理设备控制响应
  void _handleDeviceControlResponse(dynamic data) {
    debugPrint('Device control response: $data');
    // 可以在这里处理设备控制操作的响应
  }

  /// 处理远程命令响应
  void _handleRemoteCommandResponse(dynamic data) {
    debugPrint('Remote command response: $data');
    
    // 通过远程命令管理器处理响应
    if (data is Map<String, dynamic>) {
      _commandManager.handleCommandResponse(data);
    }
    
    // 可以在这里处理远程命令执行的响应
  }

  /// 处理批量操作响应
  void _handleBatchOperationResponse(dynamic data) {
    debugPrint('Batch operation response: $data');
    // 可以在这里处理批量操作的响应
  }

  /// 处理截图消息
  void _handleCaptureMessage(WebSocketMessage message) {
    // 截图相关消息的特殊处理
    debugPrint('Capture message received: ${message.action}');
  }

  /// 处理错误消息
  void _handleErrorMessage(WebSocketMessage message) {
    final errorText = message.data?['message'] ?? 'Unknown error';
    _errorController.add(errorText);
  }

  /// 处理欢迎消息
  void _handleWelcomeMessage(WebSocketMessage message) {
    debugPrint('✅ 收到欢迎消息: ${message.data?['message']}');
    debugPrint('🆔 客户端ID: ${message.data?['clientId']}');
  }

  /// 处理设备注册成功消息
  void _handleDeviceRegisteredMessage(WebSocketMessage message) {
    debugPrint('✅ 设备注册成功: ${message.data}');
    // 可以在这里触发UI更新或其他后续操作
  }

  /// 处理心跳确认消息
  void _handleHeartbeatAck(WebSocketMessage message) {
    debugPrint('💓 收到心跳确认: ${message.data}');
    // 心跳确认处理逻辑
  }

  /// 处理pong消息
  void _handlePongMessage(WebSocketMessage message) {
    debugPrint('💓 收到心跳响应: ${message.data}');
    // 心跳响应处理逻辑
  }

  /// 处理发现消息
  void _handleDiscoveryMessage(WebSocketMessage message) {
    debugPrint('🔍 收到发现消息: ${message.action}');
    if (message.action == WebSocketMessageAction.response && message.data != null) {
      // 处理设备发现响应
      final devices = message.data?['devices'] as List?;
      if (devices != null) {
        final deviceList = devices.map((deviceData) => Device.fromJson(deviceData)).toList();
        _deviceListController.add(deviceList);
      }
    }
  }

  /// 发送消息（私有方法）
  void _sendMessage(WebSocketMessage message) {
    if (_channel == null || !isConnected) {
      debugPrint('Cannot send message: not connected');
      return;
    }

    try {
      final jsonMessage = json.encode(message.toJson());
      _channel!.sink.add(jsonMessage);
    } catch (e) {
      debugPrint('Error sending message: $e');
      _errorController.add('Send message error: $e');
    }
  }

  /// 发送原始JSON消息（私有方法）
  void _sendRawMessage(Map<String, dynamic> data) {
    if (_channel == null || !isConnected) {
      debugPrint('Cannot send message: not connected');
      return;
    }

    try {
      final jsonMessage = json.encode(data);
      _channel!.sink.add(jsonMessage);
      debugPrint('Sent raw message: $jsonMessage');
    } catch (e) {
      debugPrint('Error sending raw message: $e');
      _errorController.add('Send raw message error: $e');
    }
  }

  /// 发送消息（公共方法）
  Future<void> sendMessage(WebSocketMessage message) async {
    _sendMessage(message);
  }

  /// 请求设备列表
  void requestDeviceList() {
    final message = WebSocketMessage(
      type: WebSocketMessageType.device,
      action: WebSocketMessageAction.list,
      data: {},
      timestamp: DateTime.now(),
    );
    _sendMessage(message);
  }

  /// 请求设备截图
  void requestCapture(String deviceId, {Map<String, dynamic>? options}) {
    final message = WebSocketMessage(
      type: WebSocketMessageType.capture,
      action: WebSocketMessageAction.request,
      data: {
        'deviceId': deviceId,
        'options': options ?? {},
      },
      timestamp: DateTime.now(),
    );
    _sendMessage(message);
  }

  /// 控制设备显示
  Future<void> controlDisplay({
    required String deviceId,
    required String imagePath,
    Map<String, dynamic>? options,
  }) async {
    final message = WebSocketMessage.displayControl(
      deviceId: deviceId,
      imagePath: imagePath,
      options: options,
      messageId: _generateMessageId(),
    );
    await sendMessage(message);
  }

  /// 开始显示图片
  Future<bool> startDisplay(String deviceId, String imageId, {Map<String, dynamic>? config}) async {
    try {
      final message = WebSocketMessage.displayStart(
        deviceId: deviceId,
        imageId: imageId,
        config: config,
        messageId: _generateMessageId(),
      );
      
      _sendMessage(message);
      return true;
    } catch (e) {
      print('开始显示失败: $e');
      return false;
    }
  }

  /// 停止显示
  Future<bool> stopDisplay(String deviceId) async {
    try {
      final message = WebSocketMessage.displayStop(
        deviceId: deviceId,
        messageId: _generateMessageId(),
      );
      
      _sendMessage(message);
      return true;
    } catch (e) {
      print('停止显示失败: $e');
      return false;
    }
  }

  /// 切换到下一张图片
  Future<bool> nextImage(String deviceId, {String? imageId, int? index}) async {
    try {
      final message = WebSocketMessage.displayNext(
        deviceId: deviceId,
        imageId: imageId,
        index: index,
        messageId: _generateMessageId(),
      );
      
      _sendMessage(message);
      return true;
    } catch (e) {
      print('切换下一张失败: $e');
      return false;
    }
  }

  /// 切换到上一张图片
  Future<bool> previousImage(String deviceId, {String? imageId, int? index}) async {
    try {
      final message = WebSocketMessage.displayPrevious(
        deviceId: deviceId,
        imageId: imageId,
        index: index,
        messageId: _generateMessageId(),
      );
      
      _sendMessage(message);
      return true;
    } catch (e) {
      print('切换上一张失败: $e');
      return false;
    }
  }

  /// 跳转到指定图片
  Future<bool> jumpToImage(String deviceId, String imageId, int index) async {
    try {
      final message = WebSocketMessage.displayJump(
        deviceId: deviceId,
        imageId: imageId,
        index: index,
        messageId: _generateMessageId(),
      );
      
      _sendMessage(message);
      return true;
    } catch (e) {
      print('跳转图片失败: $e');
      return false;
    }
  }

  /// 开始轮播显示
  Future<bool> startSlideshow(String deviceId, List<String> imageIds, {Map<String, dynamic>? config}) async {
    try {
      final message = WebSocketMessage.displaySlideshow(
        deviceId: deviceId,
        imageIds: imageIds,
        config: config,
        messageId: _generateMessageId(),
      );
      
      _sendMessage(message);
      return true;
    } catch (e) {
      print('开始轮播失败: $e');
      return false;
    }
  }

  /// 开始同步显示
  Future<bool> startSyncDisplay(String deviceId, String groupId, List<String> imageIds, {Map<String, dynamic>? config}) async {
    try {
      final message = WebSocketMessage.displaySync(
        deviceId: deviceId,
        groupId: groupId,
        imageIds: imageIds,
        config: config,
        messageId: _generateMessageId(),
      );
      
      await _sendMessage(message);
      return true;
    } catch (e) {
      print('开始同步显示失败: $e');
      return false;
    }
  }

  /// 设备开机
  Future<void> powerOnDevice(String deviceId) async {
    final message = WebSocketMessage.devicePowerOn(
      deviceId: deviceId,
      messageId: _generateMessageId(),
      correlationId: _generateCorrelationId(),
    );
    await sendMessage(message);
  }

  /// 设备关机
  Future<void> powerOffDevice(String deviceId) async {
    final message = WebSocketMessage.devicePowerOff(
      deviceId: deviceId,
      messageId: _generateMessageId(),
      correlationId: _generateCorrelationId(),
    );
    await sendMessage(message);
  }

  /// 设备重启
  Future<void> restartDevice(String deviceId) async {
    final message = WebSocketMessage.deviceRestart(
      deviceId: deviceId,
      messageId: _generateMessageId(),
      correlationId: _generateCorrelationId(),
    );
    await sendMessage(message);
  }

  /// 执行远程命令
  Future<void> executeRemoteCommand({
    required String deviceId,
    required String command,
    Map<String, dynamic>? parameters,
    String? commandId,
  }) async {
    final message = WebSocketMessage.executeCommand(
      deviceId: deviceId,
      command: command,
      parameters: parameters,
      messageId: commandId ?? _generateMessageId(),
    );
    await sendMessage(message);
  }

  /// 批量设备操作
  Future<void> batchDeviceOperation({
    required List<String> deviceIds,
    required WebSocketMessageAction operation,
    Map<String, dynamic>? parameters,
  }) async {
    final message = WebSocketMessage.batchOperation(
      deviceIds: deviceIds,
      operation: operation,
      parameters: parameters,
      messageId: _generateMessageId(),
      correlationId: _generateCorrelationId(),
    );
    await sendMessage(message);
  }

  /// 发送设备发现请求
  void requestDeviceDiscovery() {
    final message = WebSocketMessage(
      type: WebSocketMessageType.discovery,
      action: WebSocketMessageAction.request,
      data: {},
      timestamp: DateTime.now(),
    );
    _sendMessage(message);
  }

  /// 开始心跳
  void _startHeartbeat() {
    _stopHeartbeat();
    _heartbeatTimer = Timer.periodic(_heartbeatInterval, (_) {
      final message = WebSocketMessage(
        type: WebSocketMessageType.heartbeat,
        action: WebSocketMessageAction.ping,
        data: {'timestamp': DateTime.now().millisecondsSinceEpoch},
        timestamp: DateTime.now(),
      );
      _sendMessage(message);
    });
  }

  /// 停止心跳
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// 更新连接状态
  void _updateConnectionState(WebSocketConnectionState state) {
    if (_connectionState != state) {
      _connectionState = state;
      _connectionStateController.add(state);
    }
  }

  /// 安排重连
  void _scheduleReconnect() {
    if (_reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint('Max reconnect attempts reached');
      _updateConnectionState(WebSocketConnectionState.error);
      return;
    }

    _reconnectTimer?.cancel();
    _updateConnectionState(WebSocketConnectionState.reconnecting);
    
    _reconnectTimer = Timer(_reconnectDelay, () {
      _reconnectAttempts++;
      debugPrint('Reconnecting... attempt $_reconnectAttempts');
      connect();
    });
  }

  /// 断开连接
  Future<void> disconnect() async {
    _reconnectTimer?.cancel();
    _stopHeartbeat();
    
    if (_channel != null) {
      await _channel!.sink.close();
      _channel = null;
    }
    
    _updateConnectionState(WebSocketConnectionState.disconnected);
  }

  /// 重置连接
  Future<void> reconnect() async {
    await disconnect();
    _reconnectAttempts = 0;
    await connect();
  }

  /// 更新认证令牌
  void updateAuthToken(String? token) {
    _authToken = token;
    if (isConnected) {
      // 重新连接以使用新的令牌
      reconnect();
    }
  }

  /// 获取连接信息
  Map<String, dynamic> getConnectionInfo() {
    return {
      'state': _connectionState.toString(),
      'gatewayUrl': _gatewayUrl,
      'reconnectAttempts': _reconnectAttempts,
      'hasAuthToken': _authToken != null,
      'clientInfo': _clientInfo,
    };
  }

  /// 生成唯一的消息ID
  String _generateMessageId() {
    return 'msg_${DateTime.now().millisecondsSinceEpoch}_${_messageCounter++}';
  }

  /// 生成关联ID
  String _generateCorrelationId() {
    return 'corr_${DateTime.now().millisecondsSinceEpoch}_${_messageCounter++}';
  }

  /// 消息计数器
  int _messageCounter = 0;

  /// 远程命令管理器（用于处理命令响应）
  late final RemoteCommandManager _commandManager;

  /// 释放资源
  void dispose() {
    disconnect();
    _connectionStateController.close();
    _messageController.close();
    _deviceListController.close();
    _deviceUpdateController.close();
    _errorController.close();
  }
}