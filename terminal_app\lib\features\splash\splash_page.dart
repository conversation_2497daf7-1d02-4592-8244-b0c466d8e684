import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:io'
    if (dart.library.html) '../../core/utils/web_platform_stub.dart' as io;
import 'package:go_router/go_router.dart';
import '../../core/providers/app_state_provider.dart';
import '../../core/providers/device_provider.dart';
import '../../core/services/logger_service.dart';
import '../../core/services/device_registration_service.dart';
import '../../core/services/websocket_service.dart';
import '../../core/models/device_info.dart';
import '../../core/models/device_model.dart';
import '../../core/config/app_config.dart';

/// 启动页面
class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeApp();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _animationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      final appStateNotifier = ref.read(appStateProvider.notifier);
      final deviceNotifier = ref.read(deviceProvider.notifier);

      // 更新连接状态为连接中
      appStateNotifier.setConnectionStatus(ConnectionStatus.connecting);

      // 初始化日志服务
      await LoggerService.instance.initialize();
      LoggerService.info('应用启动中...');

      // 等待动画完成
      await Future.delayed(const Duration(seconds: 2));

      // 获取设备信息
      LoggerService.info('正在获取设备信息...');
      debugPrint('🔍 开始获取设备信息...');

      try {
        final deviceInfo = await DeviceInfo.getCurrentDevice();
        deviceNotifier.setDeviceInfo(deviceInfo);
        LoggerService.info('设备信息获取成功: ${deviceInfo.name} (${deviceInfo.id})');
        debugPrint('✅ 设备信息获取成功: ${deviceInfo.name} (${deviceInfo.id})');
        debugPrint('📱 设备类型: ${deviceInfo.type}');
        debugPrint('🖥️ 平台: ${deviceInfo.platform}');

        // 强制触发UI更新
        await Future.delayed(const Duration(milliseconds: 100));
      } catch (e) {
        LoggerService.error('设备信息获取失败: $e');
        debugPrint('❌ 设备信息获取失败: $e');

        // 即使获取失败，也设置一个有意义的默认设备信息
        final hostname = _getSafeHostname();
        final platformName = _getSafeOperatingSystem();
        final defaultDeviceId = '${platformName}_${hostname}_terminal';

        final defaultDeviceInfo = DeviceInfo(
          id: defaultDeviceId,
          name: hostname,
          type: DeviceType.terminal,
          platform: platformName,
          platformVersion: _getSafeOperatingSystemVersion(),
          model: '$platformName PC',
          manufacturer: kIsWeb ? 'Browser' : 'Unknown',
          ipAddress: '127.0.0.1',
          macAddress: null,
          networkName: null,
          screenResolution: null,
          capabilities: DeviceCapabilities.getDefault(),
          status: DeviceStatus.offline,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        deviceNotifier.setDeviceInfo(defaultDeviceInfo);
        debugPrint(
            '🔧 使用默认设备信息: ${defaultDeviceInfo.name} (${defaultDeviceInfo.id})');
      }

      // 初始化设备注册服务
      final deviceService = DeviceRegistrationService();
      await deviceService.initialize();

      // 尝试连接到网关
      LoggerService.info('正在连接到网关...');
      try {
        // 从配置获取网关URL
        final gatewayUrl = AppConstants.defaultGatewayWsUrl;

        // 初始化WebSocket服务
        final webSocketService = WebSocketService.instance;
        await webSocketService.initialize();

        // 监听WebSocket连接状态变化
        webSocketService.statusStream.listen((status) {
          switch (status) {
            case WebSocketConnectionState.connected:
              LoggerService.info('🎉 WebSocket连接状态更新: 已连接');
              deviceNotifier.setConnected(true, gatewayUrl: gatewayUrl);
              break;
            case WebSocketConnectionState.disconnected:
            case WebSocketConnectionState.error:
              LoggerService.info('💔 WebSocket连接状态更新: 已断开');
              deviceNotifier.setConnected(false);
              break;
            default:
              break;
          }
        });

        // 检查当前状态（防止监听器设置时WebSocket已经连接成功）
        if (webSocketService.isConnected) {
          LoggerService.info('🔄 WebSocket已在连接状态，同步设备状态');
          deviceNotifier.setConnected(true, gatewayUrl: gatewayUrl);
        }

        // 连接到网关
        final connected = await webSocketService.connect(gatewayUrl);
        if (!connected) {
          LoggerService.warning('网关连接失败');
          // 不在这里设置setConnected(false)，让statusStream监听器处理
        }
        // 注意：连接成功和失败的状态更新都通过statusStream监听器处理
      } catch (e) {
        LoggerService.error('网关连接错误: $e');
        // 不在这里设置setConnected(false)，让statusStream监听器处理
      }

      // 尝试自动注册设备
      try {
        await deviceService.registerDevice();
        deviceNotifier.updateStatus(DeviceStatus.registered);
        LoggerService.info('设备注册成功');
      } catch (e) {
        deviceNotifier.updateStatus(
          DeviceStatus.error,
          errorMessage: '设备注册失败: $e',
        );
        LoggerService.error('设备注册失败: $e');
      }

      // 更新连接状态
      final deviceState = ref.read(deviceProvider);
      if (deviceState.isConnected) {
        appStateNotifier.setConnectionStatus(ConnectionStatus.connected);
      } else {
        appStateNotifier.setConnectionStatus(ConnectionStatus.disconnected);
      }

      // 导航到主页面
      if (mounted) {
        context.go('/display');
      }
    } catch (e, stackTrace) {
      LoggerService.error('应用初始化失败: $e', stackTrace);
      ref.read(appStateProvider.notifier).setConnectionStatus(
            ConnectionStatus.error,
            error: '应用初始化失败: $e',
          );

      // 显示错误对话框
      if (mounted) {
        _showErrorDialog(e.toString());
      }
    }
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('初始化失败'),
        content: Text('应用初始化时发生错误:\n$error'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _retryInitialization();
            },
            child: const Text('重试'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/display');
            },
            child: const Text('跳过'),
          ),
        ],
      ),
    );
  }

  void _retryInitialization() {
    ref
        .read(appStateProvider.notifier)
        .setConnectionStatus(ConnectionStatus.connecting);
    _initializeApp();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final deviceState = ref.watch(deviceProvider);

    return Scaffold(
      backgroundColor: const Color(0xFF1A1A1A),
      body: Center(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 应用图标
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        color: const Color(0xFF2196F3),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color:
                                const Color(0xFF2196F3).withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.display_settings,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 32),

                    // 应用标题
                    const Text(
                      '图片切换终端',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),

                    // 应用副标题
                    Text(
                      'Terminal Display App',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white.withValues(alpha: 0.7),
                      ),
                    ),
                    const SizedBox(height: 48),

                    // 状态指示器
                    _buildStatusIndicator(
                        ref.read(appStateProvider.notifier), deviceState),
                    const SizedBox(height: 16),

                    // 状态文本
                    _buildStatusText(
                        ref.read(appStateProvider.notifier), deviceState),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatusIndicator(
      AppStateProvider appState, DeviceState deviceState) {
    if (appState.connectionStatus == ConnectionStatus.error ||
        deviceState.status == DeviceStatus.error) {
      return const Icon(
        Icons.error_outline,
        size: 32,
        color: Colors.red,
      );
    }

    return const SizedBox(
      width: 32,
      height: 32,
      child: CircularProgressIndicator(
        strokeWidth: 3,
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2196F3)),
      ),
    );
  }

  Widget _buildStatusText(AppStateProvider appState, DeviceState deviceState) {
    String statusText;
    Color textColor = Colors.white.withValues(alpha: 0.8);

    if (appState.connectionStatus == ConnectionStatus.error) {
      statusText = appState.connectionError ?? '应用初始化失败';
      textColor = Colors.red;
    } else if (deviceState.status == DeviceStatus.error) {
      statusText = deviceState.errorMessage ?? '设备注册失败';
      textColor = Colors.red;
    } else {
      switch (appState.connectionStatus) {
        case ConnectionStatus.connecting:
          statusText = '正在连接服务器...';
          break;
        case ConnectionStatus.connected:
          statusText = '已连接到服务器';
          break;
        case ConnectionStatus.disconnected:
          statusText = '与服务器断开连接';
          break;
        default:
          statusText = '准备就绪';
      }
    }

    return Text(
      statusText,
      style: TextStyle(
        fontSize: 14,
        color: textColor,
      ),
      textAlign: TextAlign.center,
    );
  }
}

/// 安全获取主机名（Web兼容）
String _getSafeHostname() {
  if (kIsWeb) {
    return 'Web-Terminal';
  }

  try {
    final hostname = io.Platform.localHostname;
    return hostname.isNotEmpty ? hostname : 'Unknown-Device';
  } catch (e) {
    return 'Unknown-Device';
  }
}

/// 安全获取操作系统信息（Web兼容）
String _getSafeOperatingSystem() {
  if (kIsWeb) {
    return 'web';
  }

  try {
    return io.Platform.operatingSystem;
  } catch (e) {
    return 'unknown';
  }
}

/// 安全获取操作系统版本（Web兼容）
String _getSafeOperatingSystemVersion() {
  if (kIsWeb) {
    return 'Web Browser';
  }

  try {
    return io.Platform.operatingSystemVersion;
  } catch (e) {
    return 'Unknown';
  }
}
