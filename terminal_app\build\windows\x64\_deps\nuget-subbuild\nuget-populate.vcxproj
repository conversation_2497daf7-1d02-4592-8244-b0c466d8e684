﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B65031F2-992F-398C-BAC0-DF2D6A437109}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>nuget-populate</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-mkdir.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Creating directories for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/tmp/nuget-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Performing download step (download and verify) for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/download-nuget-populate.cmake
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/verify-nuget-populate.cmake
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\download-nuget-populate.cmake;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\nuget-populate-urlinfo.txt;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-update.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No update step for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-update
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\nuget-populate-update-info.txt;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-patch.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No patch step for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\nuget-populate-patch-info.txt;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-copyfile.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Copying file to SOURCE_DIR</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_if_different E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget.exe E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-src
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-copyfile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-copyfile</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-configure.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No configure step for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\tmp\nuget-populate-cfgcmd.txt;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-patch;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-copyfile;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-build.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No build step for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-install.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No install step for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\30d0f54b5cf991fcd0df5e84963ad0cd\nuget-populate-test.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">No test step for 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\7f191f373c3a1f1be09cf6bd33745ad7\nuget-populate-complete.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Completed 'nuget-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug/nuget-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-install;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-mkdir;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-download;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-update;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-patch;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-configure;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-build;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-test;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\src\nuget-populate-stamp\Debug\nuget-populate-copyfile;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\Debug\nuget-populate-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\7302c58a2fe102e75e2a16b8c730c9d4\nuget-populate.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\Debug\nuget-populate-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild -BE:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild --check-stamp-file E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\3.29.5-msvc4\CMakeSystem.cmake;E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\nuget-populate-prefix\tmp\nuget-populate-mkdirs.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject\PatchInfo.txt.in;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject\RepositoryInfo.txt.in;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject\UpdateInfo.txt.in;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject\cfgcmd.txt.in;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject\download.cmake.in;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject\mkdirs.cmake.in;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\ExternalProject\shared_internal_commands.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-subbuild\ZERO_CHECK.vcxproj">
      <Project>{CCB1DAF9-FC00-38DF-AE77-F883AF828C65}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>