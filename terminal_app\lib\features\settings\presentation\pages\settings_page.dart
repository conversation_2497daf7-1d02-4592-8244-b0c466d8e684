import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';

import '../../../../core/services/logger_service.dart';
import '../../../../core/providers/service_providers.dart';
import '../../../../core/services/display_service.dart' as display_service;
import '../widgets/screenshot_folders_widget.dart';

/// 设置页面
class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  final _deviceNameController = TextEditingController();
  final _serverPortController = TextEditingController();
  final _customFolderController = TextEditingController();
  bool _autoStart = false;
  bool _enableCompression = true;
  bool _fullScreenOnStart = false;
  bool _showControlOverlay = true;
  bool _useCustomFolder = false;
  int _compressionQuality = 80;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _deviceNameController.dispose();
    _serverPortController.dispose();
    _customFolderController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    try {
      final configService = ref.read(appConfigServiceProvider);
      final config = await configService.getConfig();

      setState(() {
        _deviceNameController.text = config.deviceName;
        _serverPortController.text = config.serverPort.toString();
        _autoStart = config.autoStart;
        _enableCompression = config.enableCompression;
        _fullScreenOnStart = config.fullScreenOnStart;
        _showControlOverlay = config.showControlOverlay;
        _compressionQuality = config.compressionQuality;

        // 加载自定义文件夹设置
        _useCustomFolder = config.useCustomImageFolder ?? false;
        _customFolderController.text = config.customImageFolderPath ?? '';
      });
    } catch (error) {
      LoggerService.error('Failed to load settings', error);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      appBar: AppBar(
        title: const Text(
          '设置',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[800],
        elevation: 2,
        actions: [
          IconButton(
            onPressed: _saveSettings,
            icon: const Icon(Icons.save),
            tooltip: '保存设置',
          ),
          IconButton(
            onPressed: _resetSettings,
            icon: const Icon(Icons.restore),
            tooltip: '重置设置',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDeviceSettings(),
            const SizedBox(height: 16),
            _buildDisplaySettings(),
            const SizedBox(height: 16),
            _buildCompressionSettings(),
            const SizedBox(height: 16),
            // 添加截图文件夹管理
            const ScreenshotFoldersWidget(),
            const SizedBox(height: 16),
            _buildSystemSettings(),
            const SizedBox(height: 16),
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// 构建设备设置
  Widget _buildDeviceSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.device_hub, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  '设备设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 设备名称
            TextField(
              controller: _deviceNameController,
              decoration: const InputDecoration(
                labelText: '设备名称',
                hintText: '输入设备名称',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.computer),
              ),
            ),

            const SizedBox(height: 16),

            // 服务器端口
            TextField(
              controller: _serverPortController,
              decoration: const InputDecoration(
                labelText: '服务器端口',
                hintText: '输入服务器端口',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.settings_ethernet),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建显示设置
  Widget _buildDisplaySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.display_settings, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  '显示设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 启动时全屏
            SwitchListTile(
              title: const Text('启动时全屏'),
              subtitle: const Text('应用启动时自动进入全屏模式'),
              value: _fullScreenOnStart,
              onChanged: (value) {
                setState(() {
                  _fullScreenOnStart = value;
                });
              },
            ),

            // 显示控制覆盖层
            SwitchListTile(
              title: const Text('显示控制覆盖层'),
              subtitle: const Text('在非全屏模式下显示控制按钮'),
              value: _showControlOverlay,
              onChanged: (value) {
                setState(() {
                  _showControlOverlay = value;
                });
              },
            ),

            const Divider(),

            // 自定义图片文件夹
            SwitchListTile(
              title: const Text('使用自定义图片文件夹'),
              subtitle: const Text('指定一个自定义文件夹来读取图片'),
              value: _useCustomFolder,
              onChanged: (value) {
                setState(() {
                  _useCustomFolder = value;
                });
              },
            ),

            if (_useCustomFolder) ...[
              const SizedBox(height: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _customFolderController,
                          decoration: const InputDecoration(
                            labelText: '自定义文件夹路径',
                            hintText: '选择或输入文件夹路径',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.folder),
                          ),
                          readOnly: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: _selectCustomFolder,
                        icon: const Icon(Icons.folder_open),
                        tooltip: '选择文件夹',
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '如果不选择自定义文件夹，将使用默认的截图文件夹',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建压缩设置
  Widget _buildCompressionSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.compress, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  '压缩设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 启用压缩
            SwitchListTile(
              title: const Text('启用图片压缩'),
              subtitle: const Text('压缩图片以减少存储空间和传输时间'),
              value: _enableCompression,
              onChanged: (value) {
                setState(() {
                  _enableCompression = value;
                });
              },
            ),

            // 压缩质量
            if (_enableCompression) ...[
              const SizedBox(height: 16),
              Text(
                '压缩质量: $_compressionQuality%',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Slider(
                value: _compressionQuality.toDouble(),
                min: 10,
                max: 100,
                divisions: 18,
                label: '$_compressionQuality%',
                onChanged: (value) {
                  setState(() {
                    _compressionQuality = value.round();
                  });
                },
              ),
              Text(
                '较低的质量会减少文件大小，但可能影响图片清晰度',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建系统设置
  Widget _buildSystemSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.settings, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  '系统设置',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 开机自启动
            SwitchListTile(
              title: const Text('开机自启动'),
              subtitle: const Text('系统启动时自动运行应用'),
              value: _autoStart,
              onChanged: (value) {
                setState(() {
                  _autoStart = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.build, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  '操作',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testScreenshot,
                    icon: const Icon(Icons.screenshot),
                    label: const Text('测试截图'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testConnection,
                    icon: const Icon(Icons.wifi),
                    label: const Text('测试连接'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _clearCache,
                    icon: const Icon(Icons.clear_all),
                    label: const Text('清除缓存'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _viewLogs,
                    icon: const Icon(Icons.list_alt),
                    label: const Text('查看日志'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 选择自定义文件夹
  Future<void> _selectCustomFolder() async {
    try {
      final result = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择图片文件夹',
      );

      if (result != null) {
        setState(() {
          _customFolderController.text = result;
        });
      }
    } catch (error) {
      LoggerService.error('Failed to select custom folder', error);
      _showErrorSnackBar('选择文件夹失败');
    }
  }

  /// 保存设置
  Future<void> _saveSettings() async {
    try {
      final configService = ref.read(appConfigServiceProvider);
      final currentConfig = await configService.getConfig();

      final newConfig = currentConfig.copyWith(
        deviceName: _deviceNameController.text.trim(),
        serverPort: int.tryParse(_serverPortController.text) ?? 8080,
        autoStart: _autoStart,
        enableCompression: _enableCompression,
        fullScreenOnStart: _fullScreenOnStart,
        showControlOverlay: _showControlOverlay,
        compressionQuality: _compressionQuality,
        useCustomImageFolder: _useCustomFolder,
        customImageFolderPath:
            _useCustomFolder ? _customFolderController.text.trim() : null,
      );

      await configService.updateConfig(newConfig);

      // 重新扫描图片（如果更改了文件夹设置）
      if (_useCustomFolder ||
          currentConfig.useCustomImageFolder != _useCustomFolder) {
        final displayService = ref.read(display_service.displayServiceProvider);
        await displayService.scanLocalImages();
      }

      _showSuccessSnackBar('设置保存成功');
    } catch (error) {
      LoggerService.error('Failed to save settings', error);
      _showErrorSnackBar('设置保存失败');
    }
  }

  /// 重置设置
  Future<void> _resetSettings() async {
    final confirmed = await _showResetConfirmDialog();
    if (!confirmed) return;

    try {
      final configService = ref.read(appConfigServiceProvider);
      await configService.resetConfig();

      await _loadSettings();
      _showSuccessSnackBar('设置已重置');
    } catch (error) {
      LoggerService.error('Failed to reset settings', error);
      _showErrorSnackBar('设置重置失败');
    }
  }

  /// 测试截图
  Future<void> _testScreenshot() async {
    try {
      final screenshotService = ref.read(screenshotServiceProvider);
      final result = await screenshotService.takeScreenshot(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
      );

      if (result.success) {
        _showSuccessSnackBar('截图测试成功');
      } else {
        _showErrorSnackBar('截图测试失败: ${result.error}');
      }
    } catch (error) {
      LoggerService.error('Screenshot test failed', error);
      _showErrorSnackBar('截图测试失败');
    }
  }

  /// 测试连接
  Future<void> _testConnection() async {
    try {
      final deviceService = ref.read(deviceServiceProvider);
      final isConnected = await deviceService.testConnection();

      if (isConnected) {
        _showSuccessSnackBar('连接测试成功');
      } else {
        _showErrorSnackBar('连接测试失败');
      }
    } catch (error) {
      LoggerService.error('Connection test failed', error);
      _showErrorSnackBar('连接测试失败');
    }
  }

  /// 清除缓存
  Future<void> _clearCache() async {
    try {
      // TODO: 实现清除缓存逻辑
      _showSuccessSnackBar('缓存清除成功');
    } catch (error) {
      LoggerService.error('Failed to clear cache', error);
      _showErrorSnackBar('缓存清除失败');
    }
  }

  /// 查看日志
  void _viewLogs() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const _LogViewerPage(),
        settings: const RouteSettings(name: '/logs'),
      ),
    );
  }

  /// 显示重置确认对话框
  Future<bool> _showResetConfirmDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('重置设置'),
          content: const Text('确定要重置所有设置到默认值吗？此操作无法撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
              ),
              child: const Text('重置'),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// 日志查看器页面
class _LogViewerPage extends StatelessWidget {
  const _LogViewerPage();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('日志查看器'),
        backgroundColor: Colors.blue[800],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.list_alt,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '日志查看器',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '功能即将推出',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
