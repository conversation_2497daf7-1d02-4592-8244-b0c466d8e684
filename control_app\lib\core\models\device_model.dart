import 'package:flutter/foundation.dart';

/// 设备类型枚举
enum DeviceType {
  terminal,
  control,
  gateway,
}

/// 设备状态枚举
enum DeviceStatus {
  unregistered,
  registering,
  registered,
  offline,
  online,
  connecting,
  disconnecting,
  capturing,
  error,
  maintenance,
}

/// 设备能力模型
class DeviceCapabilities {
  final bool canCapture;
  final bool canDisplay;
  final bool canRecord;
  final bool canStream;
  final List<String> supportedFormats;
  final String maxResolution;
  final bool hasCamera;
  final bool hasMicrophone;
  final bool hasStorage;
  final bool hasNetwork;

  const DeviceCapabilities({
    required this.canCapture,
    required this.canDisplay,
    required this.canRecord,
    required this.canStream,
    required this.supportedFormats,
    required this.maxResolution,
    required this.hasCamera,
    required this.hasMicrophone,
    required this.hasStorage,
    required this.hasNetwork,
  });

  factory DeviceCapabilities.fromJson(Map<String, dynamic> json) {
    return DeviceCapabilities(
      canCapture: json['canCapture'] ?? false,
      canDisplay: json['canDisplay'] ?? false,
      canRecord: json['canRecord'] ?? false,
      canStream: json['canStream'] ?? false,
      supportedFormats: List<String>.from(json['supportedFormats'] ?? []),
      maxResolution: json['maxResolution'] ?? '1920x1080',
      hasCamera: json['hasCamera'] ?? false,
      hasMicrophone: json['hasMicrophone'] ?? false,
      hasStorage: json['hasStorage'] ?? true,
      hasNetwork: json['hasNetwork'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'canCapture': canCapture,
      'canDisplay': canDisplay,
      'canRecord': canRecord,
      'canStream': canStream,
      'supportedFormats': supportedFormats,
      'maxResolution': maxResolution,
      'hasCamera': hasCamera,
      'hasMicrophone': hasMicrophone,
      'hasStorage': hasStorage,
      'hasNetwork': hasNetwork,
    };
  }

  DeviceCapabilities copyWith({
    bool? canCapture,
    bool? canDisplay,
    bool? canRecord,
    bool? canStream,
    List<String>? supportedFormats,
    String? maxResolution,
    bool? hasCamera,
    bool? hasMicrophone,
    bool? hasStorage,
    bool? hasNetwork,
  }) {
    return DeviceCapabilities(
      canCapture: canCapture ?? this.canCapture,
      canDisplay: canDisplay ?? this.canDisplay,
      canRecord: canRecord ?? this.canRecord,
      canStream: canStream ?? this.canStream,
      supportedFormats: supportedFormats ?? this.supportedFormats,
      maxResolution: maxResolution ?? this.maxResolution,
      hasCamera: hasCamera ?? this.hasCamera,
      hasMicrophone: hasMicrophone ?? this.hasMicrophone,
      hasStorage: hasStorage ?? this.hasStorage,
      hasNetwork: hasNetwork ?? this.hasNetwork,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceCapabilities &&
        other.canCapture == canCapture &&
        other.canDisplay == canDisplay &&
        other.canRecord == canRecord &&
        other.canStream == canStream &&
        listEquals(other.supportedFormats, supportedFormats) &&
        other.maxResolution == maxResolution &&
        other.hasCamera == hasCamera &&
        other.hasMicrophone == hasMicrophone &&
        other.hasStorage == hasStorage &&
        other.hasNetwork == hasNetwork;
  }

  @override
  int get hashCode {
    return Object.hash(
      canCapture,
      canDisplay,
      canRecord,
      canStream,
      supportedFormats,
      maxResolution,
      hasCamera,
      hasMicrophone,
      hasStorage,
      hasNetwork,
    );
  }

  @override
  String toString() {
    return 'DeviceCapabilities(canCapture: $canCapture, canDisplay: $canDisplay, canRecord: $canRecord, canStream: $canStream, supportedFormats: $supportedFormats, maxResolution: $maxResolution, hasCamera: $hasCamera, hasMicrophone: $hasMicrophone, hasStorage: $hasStorage, hasNetwork: $hasNetwork)';
  }
}

/// 设备配置模型
class DeviceConfig {
  final bool autoCapture;
  final int captureInterval;
  final int captureQuality;
  final String captureFormat;
  final int displayTimeout;
  final bool enableNotifications;
  final bool enableSound;
  final bool enableVibration;
  final String theme;
  final String language;

  const DeviceConfig({
    required this.autoCapture,
    required this.captureInterval,
    required this.captureQuality,
    required this.captureFormat,
    required this.displayTimeout,
    required this.enableNotifications,
    required this.enableSound,
    required this.enableVibration,
    required this.theme,
    required this.language,
  });

  factory DeviceConfig.fromJson(Map<String, dynamic> json) {
    return DeviceConfig(
      autoCapture: json['autoCapture'] ?? false,
      captureInterval: json['captureInterval'] ?? 30,
      captureQuality: json['captureQuality'] ?? 80,
      captureFormat: json['captureFormat'] ?? 'png',
      displayTimeout: json['displayTimeout'] ?? 0,
      enableNotifications: json['enableNotifications'] ?? true,
      enableSound: json['enableSound'] ?? true,
      enableVibration: json['enableVibration'] ?? false,
      theme: json['theme'] ?? 'system',
      language: json['language'] ?? 'zh-CN',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'autoCapture': autoCapture,
      'captureInterval': captureInterval,
      'captureQuality': captureQuality,
      'captureFormat': captureFormat,
      'displayTimeout': displayTimeout,
      'enableNotifications': enableNotifications,
      'enableSound': enableSound,
      'enableVibration': enableVibration,
      'theme': theme,
      'language': language,
    };
  }

  DeviceConfig copyWith({
    bool? autoCapture,
    int? captureInterval,
    int? captureQuality,
    String? captureFormat,
    int? displayTimeout,
    bool? enableNotifications,
    bool? enableSound,
    bool? enableVibration,
    String? theme,
    String? language,
  }) {
    return DeviceConfig(
      autoCapture: autoCapture ?? this.autoCapture,
      captureInterval: captureInterval ?? this.captureInterval,
      captureQuality: captureQuality ?? this.captureQuality,
      captureFormat: captureFormat ?? this.captureFormat,
      displayTimeout: displayTimeout ?? this.displayTimeout,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableSound: enableSound ?? this.enableSound,
      enableVibration: enableVibration ?? this.enableVibration,
      theme: theme ?? this.theme,
      language: language ?? this.language,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceConfig &&
        other.autoCapture == autoCapture &&
        other.captureInterval == captureInterval &&
        other.captureQuality == captureQuality &&
        other.captureFormat == captureFormat &&
        other.displayTimeout == displayTimeout &&
        other.enableNotifications == enableNotifications &&
        other.enableSound == enableSound &&
        other.enableVibration == enableVibration &&
        other.theme == theme &&
        other.language == language;
  }

  @override
  int get hashCode {
    return Object.hash(
      autoCapture,
      captureInterval,
      captureQuality,
      captureFormat,
      displayTimeout,
      enableNotifications,
      enableSound,
      enableVibration,
      theme,
      language,
    );
  }

  @override
  String toString() {
    return 'DeviceConfig(autoCapture: $autoCapture, captureInterval: $captureInterval, captureQuality: $captureQuality, captureFormat: $captureFormat, displayTimeout: $displayTimeout, enableNotifications: $enableNotifications, enableSound: $enableSound, enableVibration: $enableVibration, theme: $theme, language: $language)';
  }
}

/// 设备模型
class Device {
  final String id;
  final String name;
  final DeviceType type;
  final String platform;
  final String version;
  final DeviceStatus status;
  final DeviceCapabilities capabilities;
  final DeviceConfig config;
  final String? ipAddress;
  final int? port;
  final DateTime lastSeen;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const Device({
    required this.id,
    required this.name,
    required this.type,
    required this.platform,
    required this.version,
    required this.status,
    required this.capabilities,
    required this.config,
    this.ipAddress,
    this.port,
    required this.lastSeen,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  factory Device.fromJson(Map<String, dynamic> json) {
    return Device(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: DeviceType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => DeviceType.terminal,
      ),
      platform: json['platform'] ?? '',
      version: json['version'] ?? '',
      status: DeviceStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => DeviceStatus.offline,
      ),
      capabilities: DeviceCapabilities.fromJson(
        json['capabilities'] ?? {},
      ),
      config: DeviceConfig.fromJson(
        json['config'] ?? {},
      ),
      ipAddress: json['ipAddress'],
      port: json['port'],
      lastSeen: DateTime.parse(
        json['lastSeen'] ?? DateTime.now().toIso8601String(),
      ),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'platform': platform,
      'version': version,
      'status': status.name,
      'capabilities': capabilities.toJson(),
      'config': config.toJson(),
      'ipAddress': ipAddress,
      'port': port,
      'lastSeen': lastSeen.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  Device copyWith({
    String? id,
    String? name,
    DeviceType? type,
    String? platform,
    String? version,
    DeviceStatus? status,
    DeviceCapabilities? capabilities,
    DeviceConfig? config,
    String? ipAddress,
    int? port,
    DateTime? lastSeen,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Device(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      platform: platform ?? this.platform,
      version: version ?? this.version,
      status: status ?? this.status,
      capabilities: capabilities ?? this.capabilities,
      config: config ?? this.config,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      lastSeen: lastSeen ?? this.lastSeen,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 检查设备是否在线
  bool get isOnline => status == DeviceStatus.online;

  /// 检查设备是否离线
  bool get isOffline => status == DeviceStatus.offline;

  /// 检查设备是否正在连接
  bool get isConnecting => status == DeviceStatus.connecting;

  /// 检查设备是否有错误
  bool get hasError => status == DeviceStatus.error;

  /// 检查设备是否在维护中
  bool get isInMaintenance => status == DeviceStatus.maintenance;

  /// 获取设备状态显示文本
  String get statusText {
    switch (status) {
      case DeviceStatus.unregistered:
        return '未注册';
      case DeviceStatus.registering:
        return '注册中';
      case DeviceStatus.registered:
        return '已注册';
      case DeviceStatus.online:
        return '在线';
      case DeviceStatus.offline:
        return '离线';
      case DeviceStatus.connecting:
        return '连接中';
      case DeviceStatus.disconnecting:
        return '断开中';
      case DeviceStatus.capturing:
        return '截图中';
      case DeviceStatus.error:
        return '错误';
      case DeviceStatus.maintenance:
        return '维护中';
    }
  }

  /// 获取设备类型显示文本
  String get typeText {
    switch (type) {
      case DeviceType.terminal:
        return '终端设备';
      case DeviceType.control:
        return '控制设备';
      case DeviceType.gateway:
        return '网关设备';
    }
  }

  /// 检查设备是否支持截图
  bool get canCapture => capabilities.canCapture;

  /// 检查设备是否支持显示
  bool get canDisplay => capabilities.canDisplay;

  /// 检查设备是否支持录制
  bool get canRecord => capabilities.canRecord;

  /// 检查设备是否支持流媒体
  bool get canStream => capabilities.canStream;

  /// 检查设备是否已连接（基于在线状态）
  bool get isConnected => status == DeviceStatus.online;

  /// 检查设备是否正在截图（基于配置的自动截图状态）
  bool get capturing => config.autoCapture;

  /// 获取设备网络地址
  String? get networkAddress {
    if (ipAddress != null && port != null && port! > 0) {
      return '$ipAddress:$port';
    }
    return ipAddress;
  }

  /// 获取设备地址（兼容性属性）
  String? get address => ipAddress;

  /// 检查设备是否过期（超过指定时间未更新）
  bool isExpired(Duration timeout) {
    return DateTime.now().difference(lastSeen) > timeout;
  }

  /// 获取设备运行时间
  Duration get uptime {
    return DateTime.now().difference(createdAt);
  }

  /// 获取设备最后活跃时间
  Duration get lastActiveTime {
    return DateTime.now().difference(lastSeen);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Device &&
        other.id == id &&
        other.name == name &&
        other.type == type &&
        other.platform == platform &&
        other.version == version &&
        other.status == status &&
        other.capabilities == capabilities &&
        other.config == config &&
        other.ipAddress == ipAddress &&
        other.port == port &&
        other.lastSeen == lastSeen &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      type,
      platform,
      version,
      status,
      capabilities,
      config,
      ipAddress,
      port,
      lastSeen,
      createdAt,
      updatedAt,
    );
  }

  @override
  String toString() {
    return 'Device(id: $id, name: $name, type: $type, platform: $platform, version: $version, status: $status, ipAddress: $ipAddress, port: $port, lastSeen: $lastSeen)';
  }
}

/// 设备组模型
class DeviceGroup {
  final String id;
  final String name;
  final String description;
  final List<String> deviceIds;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const DeviceGroup({
    required this.id,
    required this.name,
    required this.description,
    required this.deviceIds,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  factory DeviceGroup.fromJson(Map<String, dynamic> json) {
    return DeviceGroup(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      deviceIds: List<String>.from(json['deviceIds'] ?? []),
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'deviceIds': deviceIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  DeviceGroup copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? deviceIds,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return DeviceGroup(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      deviceIds: deviceIds ?? this.deviceIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// 检查组是否包含指定设备
  bool containsDevice(String deviceId) {
    return deviceIds.contains(deviceId);
  }

  /// 获取设备数量
  int get deviceCount => deviceIds.length;

  /// 检查组是否为空
  bool get isEmpty => deviceIds.isEmpty;

  /// 检查组是否非空
  bool get isNotEmpty => deviceIds.isNotEmpty;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceGroup &&
        other.id == id &&
        other.name == name &&
        other.description == description &&
        listEquals(other.deviceIds, deviceIds) &&
        other.createdAt == createdAt &&
        other.updatedAt == updatedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      description,
      deviceIds,
      createdAt,
      updatedAt,
    );
  }

  @override
  String toString() {
    return 'DeviceGroup(id: $id, name: $name, description: $description, deviceCount: $deviceCount)';
  }
}