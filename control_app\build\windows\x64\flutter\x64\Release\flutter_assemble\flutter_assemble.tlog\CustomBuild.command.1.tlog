^E:\AI_CODEE\IMGCT\CONTROL_APP\BUILD\WINDOWS\X64\CMAKEFILES\F607D334B5F8ABCD5E2E34625E5D652D\FLUTTER_WINDOWS.DLL.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=E:\flutter PROJECT_DIR=E:\AI_codeE\imgCT\control_app FLUTTER_ROOT=E:\flutter FLUTTER_EPHEMERAL_DIR=E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral PROJECT_DIR=E:\AI_codeE\imgCT\control_app FLUTTER_TARGET=lib\main.dart DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuOA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZWRhZGE3YzU2ZQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZWYwY2QwMDA5MQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE= DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=true PACKAGE_CONFIG=E:\AI_codeE\imgCT\control_app\.dart_tool\package_config.json E:/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\CONTROL_APP\BUILD\WINDOWS\X64\CMAKEFILES\48C1F09E4306068E98254E937CD04498\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\CONTROL_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-file E:/AI_codeE/imgCT/control_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
