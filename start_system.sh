#!/bin/bash

# Flutter图片切换系统 - 一键启动脚本 (Linux/macOS版)
# 统一网关架构 v1.0.0

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示标题
echo -e "${CYAN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                                                              ║"
echo "║    Flutter图片切换系统 - 统一网关架构 v1.0.0                 ║"
echo "║    一键启动脚本 (Linux/macOS版)                              ║"
echo "║                                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 检查系统环境
echo -e "${BLUE}🔍 检查系统环境...${NC}"

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ 错误: 未检测到Node.js，请先安装Node.js${NC}"
    echo -e "${YELLOW}📥 下载地址: https://nodejs.org/${NC}"
    exit 1
fi

# 检查Flutter
if ! command -v flutter &> /dev/null; then
    echo -e "${RED}❌ 错误: 未检测到Flutter，请先安装Flutter${NC}"
    echo -e "${YELLOW}📥 下载地址: https://flutter.dev/docs/get-started/install${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js 已安装${NC}"
echo -e "${GREEN}✅ Flutter 已安装${NC}"
echo

# 检查依赖
echo -e "${BLUE}🔍 检查网关服务依赖...${NC}"
if [ ! -d "gateway_service/node_modules" ]; then
    echo -e "${YELLOW}📦 安装网关服务依赖...${NC}"
    cd gateway_service
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 网关服务依赖安装失败${NC}"
        exit 1
    fi
    cd ..
    echo -e "${GREEN}✅ 网关服务依赖安装完成${NC}"
else
    echo -e "${GREEN}✅ 网关服务依赖已存在${NC}"
fi

echo -e "${BLUE}🔍 检查控制端应用依赖...${NC}"
if [ ! -d "controller_app/node_modules" ]; then
    echo -e "${YELLOW}📦 安装控制端应用依赖...${NC}"
    cd controller_app
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 控制端应用依赖安装失败${NC}"
        exit 1
    fi
    cd ..
    echo -e "${GREEN}✅ 控制端应用依赖安装完成${NC}"
else
    echo -e "${GREEN}✅ 控制端应用依赖已存在${NC}"
fi

echo -e "${BLUE}🔍 检查Flutter应用依赖...${NC}"
cd terminal_app
flutter pub get > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ Flutter应用依赖获取失败${NC}"
    cd ..
    exit 1
fi
cd ..
echo -e "${GREEN}✅ Flutter应用依赖检查完成${NC}"
echo

# 检查端口占用
echo -e "${BLUE}🔍 检查端口9999占用情况...${NC}"
if lsof -Pi :9999 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  警告: 端口9999已被占用${NC}"
    echo -e "${YELLOW}📋 占用端口的进程:${NC}"
    lsof -Pi :9999 -sTCP:LISTEN
    echo
    read -p "是否要终止占用进程并继续? (y/n): " choice
    if [[ $choice == [Yy]* ]]; then
        echo -e "${YELLOW}🔄 终止占用进程...${NC}"
        lsof -Pi :9999 -sTCP:LISTEN -t | xargs kill -9 2>/dev/null
        echo -e "${GREEN}✅ 进程已终止${NC}"
    else
        echo -e "${RED}❌ 用户取消启动${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ 端口9999可用${NC}"
fi
echo

# 显示启动选项
echo -e "${PURPLE}🚀 请选择启动模式:${NC}"
echo
echo "  1. 完整模式 - 启动网关服务 + Flutter终端应用 + 控制端应用"
echo "  2. 基础模式 - 启动网关服务 + Flutter终端应用"
echo "  3. 仅网关服务 - 只启动网关服务"
echo "  4. 测试模式 - 启动网关服务 + 模拟终端设备"
echo "  5. 退出"
echo
read -p "请输入选择 (1-5): " mode

case $mode in
    1)
        echo -e "${PURPLE}🚀 启动完整模式...${NC}"
        echo
        echo "📋 启动顺序:"
        echo "  1. 网关服务 (端口9999)"
        echo "  2. Flutter终端应用"
        echo "  3. 控制端应用"
        echo
        ;;
    2)
        echo -e "${PURPLE}🚀 启动基础模式...${NC}"
        echo
        echo "📋 启动顺序:"
        echo "  1. 网关服务 (端口9999)"
        echo "  2. Flutter终端应用"
        echo
        ;;
    3)
        echo -e "${PURPLE}🚀 启动仅网关服务模式...${NC}"
        echo
        ;;
    4)
        echo -e "${PURPLE}🚀 启动测试模式...${NC}"
        echo
        echo "📋 启动顺序:"
        echo "  1. 网关服务 (端口9999)"
        echo "  2. 模拟终端设备"
        echo
        ;;
    5)
        echo -e "${CYAN}👋 感谢使用Flutter图片切换系统！${NC}"
        exit 0
        ;;
    *)
        echo -e "${RED}❌ 无效选择，请重新运行脚本${NC}"
        exit 1
        ;;
esac

# 启动网关服务
echo -e "${BLUE}🌐 启动网关服务...${NC}"
echo "📍 位置: gateway_service/final_unified_gateway.js"
echo "🔗 地址: http://localhost:9999"
echo

# 根据操作系统选择终端
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    osascript -e 'tell app "Terminal" to do script "cd \"'$(pwd)'/gateway_service\" && node final_unified_gateway.js"'
else
    # Linux
    if command -v gnome-terminal &> /dev/null; then
        gnome-terminal --title="Flutter图片切换系统 - 网关服务" -- bash -c "cd gateway_service && node final_unified_gateway.js; exec bash"
    elif command -v xterm &> /dev/null; then
        xterm -title "Flutter图片切换系统 - 网关服务" -e "cd gateway_service && node final_unified_gateway.js; bash" &
    else
        echo -e "${YELLOW}⚠️  未找到合适的终端，在后台启动网关服务...${NC}"
        cd gateway_service
        nohup node final_unified_gateway.js > ../gateway.log 2>&1 &
        cd ..
    fi
fi

# 等待网关服务启动
echo -e "${YELLOW}⏳ 等待网关服务启动 (5秒)...${NC}"
sleep 5

# 验证网关服务
echo -e "${BLUE}🔍 验证网关服务状态...${NC}"
if curl -s http://localhost:9999/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ 网关服务启动成功${NC}"
else
    echo -e "${RED}❌ 网关服务启动失败，请检查日志${NC}"
    exit 1
fi

# 根据模式启动其他服务
case $mode in
    3)
        # 仅网关服务
        ;;
    4)
        # 测试模式 - 启动模拟终端设备
        echo
        echo -e "${BLUE}🤖 启动模拟终端设备...${NC}"
        echo "📍 位置: mock_terminal.js"
        echo
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            osascript -e 'tell app "Terminal" to do script "cd \"'$(pwd)'\" && node mock_terminal.js"'
        else
            if command -v gnome-terminal &> /dev/null; then
                gnome-terminal --title="Flutter图片切换系统 - 模拟终端设备" -- bash -c "node mock_terminal.js; exec bash"
            elif command -v xterm &> /dev/null; then
                xterm -title "Flutter图片切换系统 - 模拟终端设备" -e "node mock_terminal.js; bash" &
            fi
        fi
        ;;
    1|2)
        # 启动Flutter终端应用
        echo
        echo -e "${BLUE}📱 启动Flutter终端应用...${NC}"
        echo "📍 位置: terminal_app"
        echo
        
        if [[ "$OSTYPE" == "darwin"* ]]; then
            osascript -e 'tell app "Terminal" to do script "cd \"'$(pwd)'/terminal_app\" && flutter run"'
        else
            if command -v gnome-terminal &> /dev/null; then
                gnome-terminal --title="Flutter图片切换系统 - 终端应用" -- bash -c "cd terminal_app && flutter run; exec bash"
            elif command -v xterm &> /dev/null; then
                xterm -title "Flutter图片切换系统 - 终端应用" -e "cd terminal_app && flutter run; bash" &
            fi
        fi
        
        if [ "$mode" == "1" ]; then
            # 完整模式 - 启动控制端应用
            echo -e "${YELLOW}⏳ 等待Flutter应用启动 (10秒)...${NC}"
            sleep 10
            
            echo
            echo -e "${BLUE}🎮 启动控制端应用...${NC}"
            echo "📍 位置: controller_app/simple_controller.js"
            echo
            
            if [[ "$OSTYPE" == "darwin"* ]]; then
                osascript -e 'tell app "Terminal" to do script "cd \"'$(pwd)'/controller_app\" && node simple_controller.js"'
            else
                if command -v gnome-terminal &> /dev/null; then
                    gnome-terminal --title="Flutter图片切换系统 - 控制端应用" -- bash -c "cd controller_app && node simple_controller.js; exec bash"
                elif command -v xterm &> /dev/null; then
                    xterm -title "Flutter图片切换系统 - 控制端应用" -e "cd controller_app && node simple_controller.js; bash" &
                fi
            fi
        fi
        ;;
esac

# 显示成功信息
echo
echo -e "${GREEN}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                                                              ║"
echo "║    🎉 系统启动完成！                                          ║"
echo "║                                                              ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"
echo
echo "📋 服务信息:"
echo "  🌐 网关服务: http://localhost:9999"
echo "  🏥 健康检查: http://localhost:9999/health"
echo "  📊 终端状态: http://localhost:9999/terminal/status"
echo "  🎯 控制端状态: http://localhost:9999/controller/status"
echo
echo "📚 相关文档:"
echo "  📖 文档中心: docs/README.md"
echo "  📖 启动指南: docs/START_GUIDE.md"
echo "  🏗️ 架构指南: docs/UNIFIED_GATEWAY_GUIDE.md"
echo
echo "💡 提示:"
echo "  - 各服务在独立终端窗口中运行"
echo "  - 关闭终端窗口即可停止对应服务"
echo "  - 如需重启，请先关闭所有服务窗口"
echo

echo -e "${CYAN}👋 感谢使用Flutter图片切换系统！${NC}"
