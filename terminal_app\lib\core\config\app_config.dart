import 'package:flutter/foundation.dart';
import 'dart:io' if (dart.library.html) '../utils/web_platform_stub.dart' as io;

/// 动态应用配置类
class AppConfig {
  final String deviceName;
  final int serverPort;
  final bool autoStart;
  final bool enableCompression;
  final bool fullScreenOnStart;
  final bool showControlOverlay;
  final int compressionQuality;
  final String theme;
  final String language;
  final bool? useCustomImageFolder;
  final String? customImageFolderPath;

  // 静态应用信息
  static const String appName = 'Terminal Display App';
  static const String appVersion = '1.0.0';

  const AppConfig({
    required this.deviceName,
    required this.serverPort,
    required this.autoStart,
    required this.enableCompression,
    required this.fullScreenOnStart,
    required this.showControlOverlay,
    required this.compressionQuality,
    required this.theme,
    required this.language,
    this.useCustomImageFolder,
    this.customImageFolderPath,
  });

  /// 从JSON创建配置
  factory AppConfig.fromJson(Map<String, dynamic> json) {
    return AppConfig(
      deviceName: json['deviceName'] ?? 'Flutter终端设备',
      serverPort: json['serverPort'] ?? 8080,
      autoStart: json['autoStart'] ?? false,
      enableCompression: json['enableCompression'] ?? true,
      fullScreenOnStart: json['fullScreenOnStart'] ?? false,
      showControlOverlay: json['showControlOverlay'] ?? true,
      compressionQuality: json['compressionQuality'] ?? 80,
      theme: json['theme'] ?? 'system',
      language: json['language'] ?? 'zh_CN',
      useCustomImageFolder: json['useCustomImageFolder'],
      customImageFolderPath: json['customImageFolderPath'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'deviceName': deviceName,
      'serverPort': serverPort,
      'autoStart': autoStart,
      'enableCompression': enableCompression,
      'fullScreenOnStart': fullScreenOnStart,
      'showControlOverlay': showControlOverlay,
      'compressionQuality': compressionQuality,
      'theme': theme,
      'language': language,
      'useCustomImageFolder': useCustomImageFolder,
      'customImageFolderPath': customImageFolderPath,
    };
  }

  /// 复制并修改配置
  AppConfig copyWith({
    String? deviceName,
    int? serverPort,
    bool? autoStart,
    bool? enableCompression,
    bool? fullScreenOnStart,
    bool? showControlOverlay,
    int? compressionQuality,
    String? theme,
    String? language,
    bool? useCustomImageFolder,
    String? customImageFolderPath,
  }) {
    return AppConfig(
      deviceName: deviceName ?? this.deviceName,
      serverPort: serverPort ?? this.serverPort,
      autoStart: autoStart ?? this.autoStart,
      enableCompression: enableCompression ?? this.enableCompression,
      fullScreenOnStart: fullScreenOnStart ?? this.fullScreenOnStart,
      showControlOverlay: showControlOverlay ?? this.showControlOverlay,
      compressionQuality: compressionQuality ?? this.compressionQuality,
      theme: theme ?? this.theme,
      language: language ?? this.language,
      useCustomImageFolder: useCustomImageFolder ?? this.useCustomImageFolder,
      customImageFolderPath:
          customImageFolderPath ?? this.customImageFolderPath,
    );
  }
}

/// 静态应用配置
class AppConstants {
  AppConstants._();

  // 应用信息
  static const String appName = 'Terminal Display App';
  static const String appVersion = '1.0.0';
  static const String appDescription = '图片切换终端应用';

  // 网络配置 (统一网关架构 v1.0.0)
  // 注意: 使用统一端口7777，通过路径区分服务类型
  // 终端设备WebSocket路径: /terminal/ws
  static const String defaultGatewayHost = 'localhost';
  static const int defaultGatewayPort = 7777; // 统一网关端口
  static const int defaultGatewayWsPort = 7777; // WebSocket使用同一端口
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration heartbeatInterval = Duration(seconds: 30);
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const int maxReconnectAttempts = 5;

  // 获取默认网关URL
  static String get defaultGatewayUrl =>
      'http://$defaultGatewayHost:$defaultGatewayPort';
  static String get defaultGatewayWsUrl =>
      'ws://$defaultGatewayHost:$defaultGatewayWsPort';

  // 设备配置
  static const String deviceType = 'terminal';
  static const String devicePlatform = 'flutter';
  static const String userAgent = 'Flutter-ImgCT-Terminal/1.0.0';

  // 显示配置
  static const Duration defaultSlideshowInterval = Duration(seconds: 5);
  static const Duration minSlideshowInterval = Duration(seconds: 1);
  static const Duration maxSlideshowInterval = Duration(minutes: 10);
  static const bool defaultAutoFit = true;
  static const bool defaultFullscreen = false;

  // 文件配置
  static const List<String> supportedImageFormats = [
    '.jpg',
    '.jpeg',
    '.png',
    '.gif',
    '.bmp',
    '.webp',
  ];
  static const int maxImageSize = 50 * 1024 * 1024; // 50MB
  static const String defaultImageCacheDir = 'image_cache';
  static const Duration imageCacheExpiry = Duration(hours: 24);

  // 日志配置
  static const String logFileName = 'terminal_app.log';
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxLogFiles = 5;
  static const bool enableFileLogging = true;
  static const bool enableConsoleLogging = true;

  // 系统托盘配置
  static const bool enableSystemTray = true;
  static const String trayIconPath = 'assets/icons/tray_icon.ico';
  static const String trayTooltip = 'Terminal Display App';

  // 窗口配置
  static const double defaultWindowWidth = 1024;
  static const double defaultWindowHeight = 768;
  static const double minWindowWidth = 800;
  static const double minWindowHeight = 600;
  static const bool defaultWindowResizable = true;
  static const bool defaultWindowMaximizable = true;
  static const bool defaultWindowMinimizable = true;

  // 性能配置
  static const int imageLoadConcurrency = 3;
  static const Duration imageLoadTimeout = Duration(seconds: 30);
  static const int maxCachedImages = 100;
  static const Duration uiUpdateThrottle = Duration(milliseconds: 16); // 60fps

  // 安全配置
  static const bool validateImageFormats = true;
  static const bool enableImageSizeLimit = true;
  static const bool enablePathTraversal = false;

  /// 获取平台信息
  static String get platformName {
    if (kIsWeb) return 'web';

    try {
      if (io.Platform.isWindows) return 'windows';
      if (io.Platform.isMacOS) return 'macos';
      if (io.Platform.isLinux) return 'linux';
      return 'unknown';
    } catch (e) {
      return 'unknown';
    }
  }

  /// 获取设备ID
  static String get deviceId {
    // 简单的设备ID生成，实际应用中可能需要更复杂的逻辑
    final hostname = _getSafeHostname();
    final platform = platformName;
    return '${platform}_${hostname}_terminal';
  }

  /// 安全获取主机名（Web兼容）
  static String _getSafeHostname() {
    if (kIsWeb) {
      return 'Web-Terminal';
    }

    try {
      final hostname = io.Platform.localHostname;
      return hostname.isNotEmpty ? hostname : 'Unknown-Device';
    } catch (e) {
      return 'Unknown-Device';
    }
  }

  /// 获取默认应用配置
  static AppConfig get defaultConfig {
    return AppConfig(
      deviceName: '${platformName}_terminal',
      serverPort: 8080,
      autoStart: false,
      enableCompression: true,
      fullScreenOnStart: false,
      showControlOverlay: true,
      compressionQuality: 80,
      theme: 'system',
      language: 'zh_CN',
      useCustomImageFolder: false,
      customImageFolderPath: null,
    );
  }

  /// 检查图片格式是否支持
  static bool isSupportedImageFormat(String fileName) {
    final extension = fileName.toLowerCase();
    return supportedImageFormats.any((format) => extension.endsWith(format));
  }

  /// 获取应用数据目录
  static Future<String> getAppDataDir() async {
    if (kIsWeb) {
      return '/web-storage/$appName'; // Web平台使用虚拟路径
    }

    String baseDir;

    if (io.Platform.isWindows) {
      baseDir = io.Platform.environment['APPDATA'] ??
          io.Platform.environment['USERPROFILE'] ??
          '';
    } else if (io.Platform.isMacOS) {
      baseDir =
          '${io.Platform.environment['HOME']}/Library/Application Support';
    } else {
      baseDir = '${io.Platform.environment['HOME']}/.local/share';
    }

    return '$baseDir/$appName';
  }

  /// 获取日志目录
  static Future<String> getLogDir() async {
    final appDataDir = await getAppDataDir();
    return '$appDataDir/logs';
  }

  /// 获取缓存目录
  static Future<String> getCacheDir() async {
    final appDataDir = await getAppDataDir();
    return '$appDataDir/cache';
  }

  /// 获取配置文件路径
  static Future<String> getConfigFilePath() async {
    final appDataDir = await getAppDataDir();
    return '$appDataDir/config.json';
  }

  /// 验证网关URL格式
  static bool isValidGatewayUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme &&
          (uri.scheme == 'http' || uri.scheme == 'https') &&
          uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// 获取环境变量配置
  static String? getEnvConfig(String key, [String? defaultValue]) {
    if (kIsWeb) {
      return defaultValue; // Web平台不支持环境变量
    }
    return io.Platform.environment[key] ?? defaultValue;
  }

  /// 是否为调试模式
  static bool get isDebugMode {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  /// 是否为生产模式
  static bool get isProductionMode => !isDebugMode;
}
