import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

/// 日志级别枚举
enum LogLevel {
  debug,
  info,
  warning,
  error,
  fatal,
}

/// 日志服务类 - 提供统一的日志管理功能
class LoggerService {
  static LoggerService? _instance;
  static LoggerService get instance => _instance ??= LoggerService._internal();
  
  LoggerService._internal();
  
  late File _logFile;
  bool _initialized = false;
  final DateFormat _dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss.SSS');
  
  /// 初始化日志服务
  Future<void> initialize() async {
    if (_initialized) return;
    
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/imgct_logs');
      if (!await logDir.exists()) {
        await logDir.create(recursive: true);
      }
      
      final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
      _logFile = File('${logDir.path}/imgct_control_$today.log');
      
      _initialized = true;
      info('LoggerService initialized successfully');
    } catch (e) {
      debugPrint('Failed to initialize LoggerService: $e');
    }
  }
  
  /// 记录调试信息
  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    instance._log(LogLevel.debug, message, error, stackTrace);
  }
  
  /// 记录一般信息
  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    instance._log(LogLevel.info, message, error, stackTrace);
  }
  
  /// 记录警告信息
  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    instance._log(LogLevel.warning, message, error, stackTrace);
  }
  
  /// 记录错误信息
  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    instance._log(LogLevel.error, message, error, stackTrace);
  }
  
  /// 记录致命错误
  static void fatal(String message, [Object? error, StackTrace? stackTrace]) {
    instance._log(LogLevel.fatal, message, error, stackTrace);
  }
  
  /// 内部日志记录方法
  void _log(LogLevel level, String message, [Object? error, StackTrace? stackTrace]) {
    final timestamp = _dateFormat.format(DateTime.now());
    final levelStr = level.name.toUpperCase().padRight(7);
    final logMessage = '[$timestamp] [$levelStr] $message';
    
    // 控制台输出
    if (kDebugMode) {
      switch (level) {
        case LogLevel.debug:
          developer.log(logMessage, name: 'ImgCT-Control');
          break;
        case LogLevel.info:
          developer.log(logMessage, name: 'ImgCT-Control');
          break;
        case LogLevel.warning:
          developer.log(logMessage, name: 'ImgCT-Control', level: 900);
          break;
        case LogLevel.error:
        case LogLevel.fatal:
          developer.log(logMessage, name: 'ImgCT-Control', level: 1000, error: error, stackTrace: stackTrace);
          break;
      }
    }
    
    // 写入文件
    _writeToFile(logMessage, error, stackTrace);
  }
  
  /// 写入日志文件
  void _writeToFile(String message, [Object? error, StackTrace? stackTrace]) {
    if (!_initialized) return;
    
    try {
      final buffer = StringBuffer(message);
      
      if (error != null) {
        buffer.writeln();
        buffer.writeln('Error: $error');
      }
      
      if (stackTrace != null) {
        buffer.writeln();
        buffer.writeln('StackTrace: $stackTrace');
      }
      
      buffer.writeln();
      
      _logFile.writeAsStringSync(buffer.toString(), mode: FileMode.append);
    } catch (e) {
      debugPrint('Failed to write log to file: $e');
    }
  }
  
  /// 清理旧日志文件（保留最近7天）
  Future<void> cleanOldLogs() async {
    if (!_initialized) return;
    
    try {
      final directory = await getApplicationDocumentsDirectory();
      final logDir = Directory('${directory.path}/imgct_logs');
      
      if (await logDir.exists()) {
        final files = await logDir.list().toList();
        final cutoffDate = DateTime.now().subtract(const Duration(days: 7));
        
        for (final file in files) {
          if (file is File) {
            final stat = await file.stat();
            if (stat.modified.isBefore(cutoffDate)) {
              await file.delete();
              info('Deleted old log file: ${file.path}');
            }
          }
        }
      }
    } catch (e) {
      error('Failed to clean old logs', e);
    }
  }
  
  /// 获取日志文件路径
  String? get logFilePath => _initialized ? _logFile.path : null;
  
  /// 检查是否已初始化
  bool get isInitialized => _initialized;
}