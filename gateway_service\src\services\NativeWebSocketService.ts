import { Server as HttpServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import { v4 as uuidv4 } from 'uuid';
import url from 'url';

/// 设备信息接口
interface DeviceInfo {
  id: string;
  name: string;
  type: string;
  platform: string;
  version: string;
  ipAddress: string;
  port: number;
  capabilities: Record<string, any>;
  status: string;
  socket: WebSocket;
  lastSeen: Date;
  metadata: Record<string, any>;
}

/// 原生WebSocket服务类
export class NativeWebSocketService {
  private static instance: NativeWebSocketService;
  private wss: WebSocketServer | null = null;
  private connectedDevices = new Map<string, DeviceInfo>();
  private connectedClients = new Map<string, WebSocket>();
  private isInitialized = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;

  private constructor() {}

  /// 获取单例实例
  public static getInstance(): NativeWebSocketService {
    if (!NativeWebSocketService.instance) {
      NativeWebSocketService.instance = new NativeWebSocketService();
    }
    return NativeWebSocketService.instance;
  }

  /// 初始化WebSocket服务
  public initialize(server: HttpServer): void {
    if (this.isInitialized) {
      console.log('Native WebSocket service already initialized');
      return;
    }

    console.log('Initializing Native WebSocket service...');

    this.wss = new WebSocketServer({
      server,
      path: '/ws',
      perMessageDeflate: false,
      maxPayload: 16 * 1024 * 1024 // 16MB
    });

    this.wss.on('connection', (ws: WebSocket, request) => {
      this.handleConnection(ws, request);
    });

    this.wss.on('error', (error) => {
      console.error('WebSocket Server error:', error);
    });

    // 启动心跳检测
    this.startHeartbeat();

    this.isInitialized = true;
    console.log('✅ Native WebSocket service initialized successfully on /ws');
  }

  /// 处理新连接
  private handleConnection(ws: WebSocket, request: any): void {
    const clientId = uuidv4();
    const clientIp = request.socket.remoteAddress || 'unknown';
    
    console.log(`New WebSocket connection: ${clientId} from ${clientIp}`);

    // 设置连接属性
    (ws as any).clientId = clientId;
    (ws as any).clientIp = clientIp;
    (ws as any).isAlive = true;
    (ws as any).lastSeen = new Date();

    // 添加到客户端列表
    this.connectedClients.set(clientId, ws);

    // 设置消息处理
    ws.on('message', (data: Buffer) => {
      this.handleMessage(ws, data);
    });

    // 设置心跳响应
    ws.on('pong', () => {
      (ws as any).isAlive = true;
      (ws as any).lastSeen = new Date();
    });

    // 设置断开连接处理
    ws.on('close', (code: number, reason: Buffer) => {
      this.handleDisconnection(ws, code, reason);
    });

    // 设置错误处理
    ws.on('error', (error: Error) => {
      console.error(`WebSocket error for client ${clientId}:`, error);
    });

    // 发送欢迎消息
    this.sendMessage(ws, {
      type: 'connection',
      action: 'welcome',
      payload: {
        clientId,
        timestamp: new Date().toISOString(),
        message: 'Connected to Gateway WebSocket'
      },
      senderId: 'gateway'
    });
  }

  /// 处理消息
  private handleMessage(ws: WebSocket, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString());
      const clientId = (ws as any).clientId;
      
      console.log(`Message from ${clientId}:`, message.type, message.action || 'no-action');

      // 更新最后活动时间
      (ws as any).lastSeen = new Date();

      // 兼容 terminal_app 的消息格式
      switch (message.type) {
        case 'device_register':
          this.handleTerminalDeviceRegister(ws, message);
          break;
        case 'device_status':
          this.handleTerminalDeviceStatus(ws, message);
          break;
        case 'heartbeat':
          this.handleTerminalHeartbeat(ws, message);
          break;
        case 'device':
          this.handleDeviceMessage(ws, message);
          break;
        case 'control':
          this.handleControlMessage(ws, message);
          break;
        case 'discovery':
          this.handleDiscovery(ws, message);
          break;
        default:
          console.warn(`Unknown message type: ${message.type}`);
          this.sendError(ws, 'Unknown message type', message.type);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
      this.sendError(ws, 'Invalid message format', error);
    }
  }

  /// 处理设备消息
  private handleDeviceMessage(ws: WebSocket, message: any): void {
    switch (message.action) {
      case 'register':
        this.registerDevice(ws, message);
        break;
      case 'status_update':
        this.updateDeviceStatus(ws, message);
        break;
      case 'screenshot_result':
        this.handleScreenshotResult(ws, message);
        break;
      default:
        console.warn(`Unknown device action: ${message.action}`);
    }
  }

  /// 处理控制消息
  private handleControlMessage(ws: WebSocket, message: any): void {
    switch (message.action) {
      case 'get_devices':
        this.sendDeviceList(ws);
        break;
      case 'send_command':
        this.forwardCommandToDevice(ws, message);
        break;
      default:
        console.warn(`Unknown control action: ${message.action}`);
    }
  }

  /// 注册设备
  private registerDevice(ws: WebSocket, message: any): void {
    const deviceInfo = message.payload;
    const clientId = (ws as any).clientId;
    const deviceId = deviceInfo.deviceId || clientId;

    const device: DeviceInfo = {
      id: deviceId,
      name: deviceInfo.name || `Device ${deviceId}`,
      type: deviceInfo.type || 'terminal',
      platform: deviceInfo.platform || 'unknown',
      version: deviceInfo.version || '1.0.0',
      ipAddress: (ws as any).clientIp,
      port: deviceInfo.port || 0,
      capabilities: deviceInfo.capabilities || {},
      status: 'online',
      socket: ws,
      lastSeen: new Date(),
      metadata: deviceInfo.metadata || {}
    };

    // 存储设备信息
    this.connectedDevices.set(deviceId, device);
    (ws as any).deviceId = deviceId;
    (ws as any).deviceType = 'device';

    console.log(`✅ Device registered: ${deviceId} (${device.name})`);

    // 响应设备
    this.sendMessage(ws, {
      type: 'device',
      action: 'register_response',
      payload: {
        success: true,
        deviceId,
        message: 'Device registered successfully'
      },
      senderId: 'gateway'
    });

    // 通知所有控制端
    this.broadcastToClients({
      type: 'device',
      action: 'device_registered',
      payload: {
        device: this.serializeDeviceInfo(device)
      },
      senderId: 'gateway'
    });
  }

  /// 更新设备状态
  private updateDeviceStatus(ws: WebSocket, message: any): void {
    const deviceId = (ws as any).deviceId;
    const device = this.connectedDevices.get(deviceId);
    
    if (device) {
      device.status = message.payload.status || device.status;
      device.lastSeen = new Date();
      device.metadata = { ...device.metadata, ...message.payload.metadata };

      // 通知控制端
      this.broadcastToClients({
        type: 'device',
        action: 'status_updated',
        payload: {
          deviceId,
          status: device.status,
          metadata: device.metadata,
          lastSeen: device.lastSeen.toISOString()
        },
        senderId: 'gateway'
      });
    }
  }

  /// 处理截图结果
  private handleScreenshotResult(ws: WebSocket, message: any): void {
    // 转发截图结果给控制端
    this.broadcastToClients({
      type: 'screenshot',
      action: 'result',
      payload: message.payload,
      senderId: (ws as any).deviceId || 'unknown'
    });
  }

  /// 发送设备列表
  private sendDeviceList(ws: WebSocket): void {
    const devices = Array.from(this.connectedDevices.values()).map(device => 
      this.serializeDeviceInfo(device)
    );

    this.sendMessage(ws, {
      type: 'control',
      action: 'device_list',
      payload: { devices },
      senderId: 'gateway'
    });
  }

  /// 转发命令到设备
  private forwardCommandToDevice(ws: WebSocket, message: any): void {
    const { deviceId, command } = message.payload;
    const device = this.connectedDevices.get(deviceId);

    if (device && device.socket.readyState === WebSocket.OPEN) {
      this.sendMessage(device.socket, {
        type: 'command',
        action: command.action,
        payload: command.payload,
        senderId: 'gateway'
      });
    } else {
      this.sendError(ws, 'Device not found or offline', deviceId);
    }
  }

  /// 处理心跳
  private handleHeartbeat(ws: WebSocket, message: any): void {
    this.sendMessage(ws, {
      type: 'heartbeat',
      action: 'pong',
      payload: { timestamp: new Date().toISOString() },
      senderId: 'gateway'
    });
  }

  /// 处理 terminal_app 设备注册
  private handleTerminalDeviceRegister(ws: WebSocket, message: any): void {
    const deviceInfo = message.data;
    const clientId = (ws as any).clientId;
    const deviceId = deviceInfo.id || clientId;

    const device: DeviceInfo = {
      id: deviceId,
      name: deviceInfo.name || `Device ${deviceId}`,
      type: deviceInfo.type || 'terminal',
      platform: deviceInfo.platform || 'unknown',
      version: deviceInfo.version || '1.0.0',
      ipAddress: (ws as any).clientIp,
      port: deviceInfo.port || 0,
      capabilities: deviceInfo.capabilities || {},
      status: 'online',
      socket: ws,
      lastSeen: new Date(),
      metadata: deviceInfo.metadata || {}
    };

    // 存储设备信息
    this.connectedDevices.set(deviceId, device);
    (ws as any).deviceId = deviceId;
    (ws as any).deviceType = 'device';

    console.log(`✅ Terminal device registered: ${deviceId} (${device.name})`);

    // 响应设备 (terminal_app 格式)
    this.sendMessage(ws, {
      type: 'device_registered',
      data: {
        success: true,
        deviceId,
        message: 'Device registered successfully'
      },
      timestamp: new Date().toISOString()
    });

    // 通知所有控制端
    this.broadcastToClients({
      type: 'device',
      action: 'device_registered',
      payload: {
        device: this.serializeDeviceInfo(device)
      },
      senderId: 'gateway'
    });
  }

  /// 处理 terminal_app 设备状态
  private handleTerminalDeviceStatus(ws: WebSocket, message: any): void {
    const deviceId = (ws as any).deviceId;
    const device = this.connectedDevices.get(deviceId);
    
    if (device) {
      const statusData = message.data;
      device.status = statusData.status || device.status;
      device.lastSeen = new Date();
      device.metadata = { ...device.metadata, ...statusData };

      console.log(`Device status updated: ${deviceId} - ${device.status}`);

      // 通知控制端
      this.broadcastToClients({
        type: 'device',
        action: 'status_updated',
        payload: {
          deviceId,
          status: device.status,
          metadata: device.metadata,
          lastSeen: device.lastSeen.toISOString()
        },
        senderId: 'gateway'
      });
    }
  }

  /// 处理 terminal_app 心跳
  private handleTerminalHeartbeat(ws: WebSocket, message: any): void {
    const deviceId = (ws as any).deviceId;
    const device = this.connectedDevices.get(deviceId);
    
    if (device) {
      device.lastSeen = new Date();
    }

    // 响应心跳 (terminal_app 格式)
    this.sendMessage(ws, {
      type: 'pong',
      data: {
        timestamp: new Date().toISOString(),
        device_id: deviceId
      },
      timestamp: new Date().toISOString()
    });
  }

  /// 处理发现请求
  private handleDiscovery(ws: WebSocket, message: any): void {
    if (message.action === 'request') {
      const devices = Array.from(this.connectedDevices.values()).map(device => 
        this.serializeDeviceInfo(device)
      );

      this.sendMessage(ws, {
        type: 'discovery',
        action: 'response',
        payload: { devices },
        senderId: 'gateway'
      });
    }
  }

  /// 处理断开连接
  private handleDisconnection(ws: WebSocket, code: number, reason: Buffer): void {
    const clientId = (ws as any).clientId;
    const deviceId = (ws as any).deviceId;
    
    console.log(`WebSocket disconnected: ${clientId} (code: ${code})`);

    // 从客户端列表中移除
    this.connectedClients.delete(clientId);

    // 如果是设备，从设备列表中移除
    if (deviceId && this.connectedDevices.has(deviceId)) {
      const device = this.connectedDevices.get(deviceId)!;
      device.status = 'offline';
      device.lastSeen = new Date();

      // 通知控制端设备离线
      this.broadcastToClients({
        type: 'device',
        action: 'device_disconnected',
        payload: {
          deviceId,
          lastSeen: device.lastSeen.toISOString()
        },
        senderId: 'gateway'
      });

      // 从连接列表中移除
      this.connectedDevices.delete(deviceId);
      console.log(`Device disconnected: ${deviceId}`);
    }
  }

  /// 发送消息到指定WebSocket
  private sendMessage(ws: WebSocket, message: any): void {
    if (ws.readyState === WebSocket.OPEN) {
      try {
        ws.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
      }
    }
  }

  /// 发送错误消息
  private sendError(ws: WebSocket, error: string, details?: any): void {
    this.sendMessage(ws, {
      type: 'error',
      action: 'error',
      payload: {
        error,
        details: details?.toString(),
        timestamp: new Date().toISOString()
      },
      senderId: 'gateway'
    });
  }

  /// 广播消息给所有控制端
  private broadcastToClients(message: any): void {
    this.connectedClients.forEach((ws, clientId) => {
      // 只发送给非设备客户端
      if (!(ws as any).deviceType) {
        this.sendMessage(ws, message);
      }
    });
  }

  /// 序列化设备信息
  private serializeDeviceInfo(device: DeviceInfo): Record<string, any> {
    return {
      id: device.id,
      name: device.name,
      type: device.type,
      platform: device.platform,
      version: device.version,
      ipAddress: device.ipAddress,
      port: device.port,
      capabilities: device.capabilities,
      status: device.status,
      lastSeen: device.lastSeen.toISOString(),
      metadata: device.metadata
    };
  }

  /// 启动心跳检测
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.connectedClients.forEach((ws, clientId) => {
        if ((ws as any).isAlive === false) {
          console.log(`Terminating inactive connection: ${clientId}`);
          ws.terminate();
          return;
        }

        (ws as any).isAlive = false;
        if (ws.readyState === WebSocket.OPEN) {
          ws.ping();
        }
      });
    }, 30000); // 30秒心跳检测
  }

  /// 停止服务
  public stop(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    this.connectedDevices.clear();
    this.connectedClients.clear();
    this.isInitialized = false;

    console.log('Native WebSocket service stopped');
  }

  /// 获取连接统计
  public getStats(): { devices: number; clients: number } {
    return {
      devices: this.connectedDevices.size,
      clients: this.connectedClients.size
    };
  }

  /// 检查服务是否运行
  public static isRunning(): boolean {
    return NativeWebSocketService.getInstance().isInitialized;
  }

  /// 处理终端设备注册
  private handleTerminalDeviceRegister(ws: WebSocket, message: any): void {
    console.log('Processing terminal device registration:', message);
    
    const deviceData = message.data;
    const clientId = (ws as any).clientId;
    const deviceId = deviceData?.device_id || clientId;

    const device: DeviceInfo = {
      id: deviceId,
      name: deviceData?.device_name || `Terminal ${deviceId}`,
      type: 'terminal',
      platform: deviceData?.platform || 'unknown',
      version: deviceData?.version || '1.0.0',
      ipAddress: (ws as any).clientIp,
      port: deviceData?.port || 0,
      capabilities: deviceData?.capabilities || {},
      status: 'online',
      socket: ws,
      lastSeen: new Date(),
      metadata: deviceData || {}
    };

    // 存储设备信息
    this.connectedDevices.set(deviceId, device);
    (ws as any).deviceId = deviceId;
    (ws as any).deviceType = 'device';

    console.log(`✅ Terminal device registered: ${deviceId} (${device.name})`);

    // 响应设备注册成功
    this.sendMessage(ws, {
      type: 'device_registered',
      data: {
        success: true,
        device_id: deviceId,
        message: 'Device registered successfully'
      },
      timestamp: Date.now()
    });

    // 通知所有控制端有新设备注册
    this.broadcastToClients({
      type: 'device_registered',
      data: {
        device: this.serializeDeviceInfo(device)
      },
      timestamp: Date.now()
    });
  }

  /// 处理终端设备状态更新
  private handleTerminalDeviceStatus(ws: WebSocket, message: any): void {
    console.log('Processing terminal device status:', message);
    
    const deviceId = (ws as any).deviceId;
    const device = this.connectedDevices.get(deviceId);
    
    if (device) {
      // 更新设备状态
      device.lastSeen = new Date();
      device.status = 'online';
      if (message.data) {
        device.metadata = { ...device.metadata, ...message.data };
      }
      
      console.log(`📊 Device status updated: ${deviceId}`);
      
      // 通知控制端设备状态更新
      this.broadcastToClients({
        type: 'device_status_updated',
        data: {
          device_id: deviceId,
          status: device.status,
          last_seen: device.lastSeen.toISOString(),
          metadata: device.metadata
        },
        timestamp: Date.now()
      });
    } else {
      console.warn(`Device not found for status update: ${deviceId}`);
    }
  }

  /// 处理终端设备心跳
  private handleTerminalHeartbeat(ws: WebSocket, message: any): void {
    const deviceId = (ws as any).deviceId || message.data?.device_id;
    const device = this.connectedDevices.get(deviceId);
    
    if (device) {
      // 更新最后活跃时间
      device.lastSeen = new Date();
      device.status = 'online';
      
      console.log(`💓 Heartbeat received from device: ${deviceId}`);
      
      // 响应心跳
      this.sendMessage(ws, {
        type: 'ping',
        data: {
          timestamp: Date.now()
        },
        timestamp: Date.now()
      });
    } else {
      console.warn(`Device not found for heartbeat: ${deviceId}`);
      // 如果设备未注册，要求重新注册
      this.sendMessage(ws, {
        type: 'error',
        data: {
          message: 'Device not registered, please register first',
          code: 'DEVICE_NOT_REGISTERED'
        },
        timestamp: Date.now()
      });
    }
  }
}

export default NativeWebSocketService;