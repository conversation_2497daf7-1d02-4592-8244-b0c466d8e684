^E:\AI_CODEE\IMGCT\CONTROL_APP\BUILD\WINDOWS\X64\CMAKEFILES\BA85999C94E6D25B4D94BD2862074B37\GENERATE.STAMP.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/AI_codeE/imgCT/control_app/build/windows/x64/flutter_imgct_control.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
