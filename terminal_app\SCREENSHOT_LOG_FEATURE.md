# 截图日志功能实现说明

## 功能概述

为截图服务页面添加了完整的日志记录功能，支持按天生成日志文件，并提供可视化的日志查看界面，用户可以点击日志条目跳转到对应的图片显示页面。

## 功能特性

### 🔄 自动日志记录
- **按天生成**：每天自动创建独立的日志文件（格式：`screenshot_yyyy-MM-dd.log`）
- **实时记录**：每次截图操作都会自动记录详细信息
- **成功/失败追踪**：记录截图操作的成功状态和错误信息
- **元数据记录**：保存文件大小、格式、压缩状态等详细信息

### 📊 可视化日志界面
- **分标签显示**：按日期分标签展示日志，今天的标签特殊标记
- **统计信息**：显示最近7天的截图统计（总数、成功数、失败数、成功率）
- **条目详情**：每个日志条目显示时间、状态、文件信息等
- **状态图标**：直观的成功/失败状态图标显示

### 🖼️ 图片查看功能
- **点击跳转**：点击包含图片的日志条目可直接跳转到图片查看页面
- **缩放查看**：支持图片缩放、平移等交互操作
- **图片信息**：显示图片的详细信息（文件名、大小、路径等）
- **错误处理**：妥善处理图片不存在或损坏的情况

### 🗃️ 自动维护
- **日志清理**：自动清理30天前的旧日志文件
- **性能优化**：高效的文件读写和内存管理
- **错误恢复**：robust的错误处理和恢复机制

## 实现的文件

### 核心服务
1. **`screenshot_log_service.dart`** - 截图日志服务
   - 日志条目模型（ScreenshotLogEntry）
   - 日志记录和查询功能
   - 文件管理和清理功能

### 用户界面
2. **`screenshot_log_page.dart`** - 日志列表页面
   - 分标签日期显示
   - 统计信息卡片
   - 日志条目列表

3. **`image_viewer_page.dart`** - 图片查看页面
   - 图片缩放查看
   - 图片信息显示
   - 错误状态处理

### 服务集成
4. **`screenshot_service.dart`** - 更新截图服务
   - 集成日志记录功能
   - 成功/失败状态记录

5. **`screenshot_page.dart`** - 更新截图页面
   - 添加"查看日志"按钮
   - 导航到日志页面

## 使用方法

### 1. 查看日志
在截图服务页面，点击右上角的"历史"图标（📱 history）即可进入日志页面。

### 2. 浏览日志
- 在顶部的标签栏中选择不同的日期
- 查看统计信息卡片了解整体情况
- 滚动浏览具体的日志条目

### 3. 查看图片
- 点击包含图片文件的日志条目
- 在图片查看器中缩放、查看图片
- 点击信息按钮查看图片详细信息

## 日志条目信息

每个日志条目包含以下信息：
- ✅/❌ **操作状态**：成功或失败的图标显示
- 🕐 **时间戳**：精确到秒的操作时间
- 📄 **操作类型**：立即截图、定时截图、批量截图等
- 📁 **文件路径**：保存的图片文件路径（如果成功）
- ❗ **错误信息**：失败时的详细错误描述
- 📊 **元数据**：文件大小、格式等技术信息

## 技术特点

### 性能优化
- 异步文件IO操作，不阻塞UI
- 按需加载日志数据
- 高效的内存管理

### 可靠性
- 完善的错误处理机制
- 文件损坏时的恢复能力
- 网络或存储异常的容错处理

### 扩展性
- 模块化的服务设计
- 易于添加新的日志类型
- 支持自定义日志格式

## 配置说明

### 日志存储
- **存储位置**：应用文档目录下的 `screenshot_logs` 文件夹
- **文件格式**：JSON Lines格式，每行一个日志条目
- **清理策略**：自动删除30天前的日志文件

### 依赖库
- `intl`：日期时间格式化
- `path_provider`：获取应用文档目录
- `photo_view`：图片缩放查看
- `flutter_riverpod`：状态管理

## 后续扩展建议

1. **导出功能**：支持导出日志为CSV或Excel格式
2. **过滤功能**：按状态、时间范围过滤日志
3. **搜索功能**：支持关键词搜索日志条目
4. **云同步**：支持日志文件的云端备份
5. **图表分析**：添加截图频次和成功率的图表分析