﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\x64\Debug\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\flutter\x64\Debug\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\plugins\connectivity_plus\Debug\connectivity_plus_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\plugins\permission_handler_windows\Debug\permission_handler_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\plugins\screen_retriever\Debug\screen_retriever_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\plugins\system_tray\Debug\system_tray_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\plugins\window_manager\Debug\window_manager_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\runner\Debug\flutter_imgct_terminal.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\terminal_app\build\windows\x64\x64\Debug\ALL_BUILD</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>