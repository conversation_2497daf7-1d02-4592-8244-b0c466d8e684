import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:getwidget/getwidget.dart'; // 移除GetWidget导入

/// 设备管理页面
class DevicesPage extends ConsumerStatefulWidget {
  const DevicesPage({super.key});

  @override
  ConsumerState<DevicesPage> createState() => _DevicesPageState();
}

class _DevicesPageState extends ConsumerState<DevicesPage> {
  final List<Device> _devices = [
    const Device(
      id: '1',
      name: '会议室大屏',
      type: DeviceType.display,
      status: DeviceStatus.online,
      ipAddress: '*************',
      location: '会议室A',
    ),
    const Device(
      id: '2',
      name: '大厅显示屏',
      type: DeviceType.display,
      status: DeviceStatus.online,
      ipAddress: '*************',
      location: '大厅',
    ),
    const Device(
      id: '3',
      name: '接待处屏幕',
      type: DeviceType.display,
      status: DeviceStatus.offline,
      ipAddress: '*************',
      location: '接待处',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 页面标题和操作按钮
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '设备管理',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addDevice,
                  icon: const Icon(
                    Icons.add,
                    size: 18,
                  ),
                  label: const Text('添加设备'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    textStyle: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 设备统计卡片
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    '在线设备',
                    _devices
                        .where((d) => d.status == DeviceStatus.online)
                        .length
                        .toString(),
                    Icons.devices,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: _buildStatCard(
                    '离线设备',
                    _devices
                        .where((d) => d.status == DeviceStatus.offline)
                        .length
                        .toString(),
                    Icons.device_unknown,
                    Colors.red,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatCard(
                    '总设备数',
                    _devices.length.toString(),
                    Icons.dashboard,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 设备列表
            Expanded(
              child: Card(
                color: Theme.of(context).colorScheme.surface,
                elevation: 4,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Text(
                        '设备列表',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Expanded(
                      child: ListView.builder(
                        itemCount: _devices.length,
                        itemBuilder: (context, index) {
                          final device = _devices[index];
                          return _buildDeviceItem(device);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6),
            Text(
              value,
              style: TextStyle(
                color: color,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceItem(Device device) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 2),
      child: Card(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        elevation: 1,
        child: ListTile(
          leading: Icon(
            _getDeviceIcon(device.type),
            color: _getStatusColor(device.status),
            size: 20,
          ),
          title: Text(
            device.name,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${device.location} • ${device.ipAddress}',
                style: TextStyle(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              Text(
                device.status.displayName,
                style: TextStyle(
                  color: _getStatusColor(device.status),
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          contentPadding: const EdgeInsets.all(12),
          onTap: () => _showDeviceDetails(device),
        ),
      ),
    );
  }

  IconData _getDeviceIcon(DeviceType type) {
    switch (type) {
      case DeviceType.display:
        return Icons.tv;
      case DeviceType.projector:
        return Icons.videocam;
      case DeviceType.tablet:
        return Icons.tablet;
    }
  }

  Color _getStatusColor(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return Colors.green;
      case DeviceStatus.offline:
        return Colors.red;
      case DeviceStatus.maintenance:
        return Colors.orange;
    }
  }

  void _addDevice() {
    // 添加设备逻辑
    print('添加设备');
  }

  void _showDeviceDetails(Device device) {
    // 显示设备详情
    print('显示设备详情: ${device.name}');
  }
}

/// 设备类型枚举
enum DeviceType {
  display('display', '显示屏'),
  projector('projector', '投影仪'),
  tablet('tablet', '平板');

  const DeviceType(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// 设备状态枚举
enum DeviceStatus {
  online('online', '在线'),
  offline('offline', '离线'),
  maintenance('maintenance', '维护中');

  const DeviceStatus(this.value, this.displayName);
  final String value;
  final String displayName;
}

/// 设备模型
class Device {
  const Device({
    required this.id,
    required this.name,
    required this.type,
    required this.status,
    required this.ipAddress,
    required this.location,
  });

  final String id;
  final String name;
  final DeviceType type;
  final DeviceStatus status;
  final String ipAddress;
  final String location;
}
