import '../entities/user.dart';

/// 认证仓库接口
abstract class AuthRepository {
  /// 用户登录
  /// 
  /// [username] 用户名
  /// [password] 密码
  /// 返回登录成功的用户信息，失败时抛出异常
  Future<User> login(String username, String password);

  /// 用户注册
  /// 
  /// [username] 用户名
  /// [password] 密码
  /// [email] 邮箱（可选）
  /// 返回注册成功的用户信息，失败时抛出异常
  Future<User> register(String username, String password, {String? email});

  /// 用户登出
  Future<void> logout();

  /// 获取当前用户信息
  /// 
  /// 返回当前登录的用户信息，未登录时返回 null
  Future<User?> getCurrentUser();

  /// 检查用户是否已登录
  Future<bool> isLoggedIn();

  /// 刷新用户令牌
  /// 
  /// 返回新的用户信息，失败时抛出异常
  Future<User> refreshToken();

  /// 更新用户资料
  /// 
  /// [username] 新用户名
  /// [email] 新邮箱
  /// [avatar] 新头像
  /// 返回更新后的用户信息
  Future<User> updateProfile({
    String? username,
    String? email,
    String? avatar,
  });

  /// 验证令牌有效性
  /// 
  /// [token] 要验证的令牌
  /// 返回令牌是否有效
  Future<bool> validateToken(String token);

  /// 获取存储的访问令牌
  Future<String?> getAccessToken();

  /// 获取存储的刷新令牌
  Future<String?> getRefreshToken();

  /// 清除所有认证数据
  Future<void> clearAuthData();

  /// 修改密码
  /// 
  /// [oldPassword] 旧密码
  /// [newPassword] 新密码
  /// 返回修改是否成功
  Future<bool> changePassword(String oldPassword, String newPassword);
}