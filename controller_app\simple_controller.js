// 简单控制端应用 - 用于测试统一网关服务
import WebSocket from 'ws';
import readline from 'readline';

console.log('🎮 启动简单控制端应用...');

// 创建控制端WebSocket连接
const ws = new WebSocket('ws://localhost:7777/controller/ws');

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

let isConnected = false;
let connectedDevices = [];

ws.on('open', () => {
  console.log('✅ 控制端连接成功');
  isConnected = true;
  
  // 获取设备列表
  ws.send(JSON.stringify({
    type: 'get_devices'
  }));
  
  showMenu();
});

ws.on('message', (data) => {
  try {
    const message = JSON.parse(data.toString());
    console.log('\n📨 收到消息:', message);
    
    switch (message.type) {
      case 'welcome':
        console.log(`🎉 ${message.message}`);
        console.log(`🆔 客户端ID: ${message.clientId}`);
        break;
        
      case 'devices_list':
        connectedDevices = message.data.devices;
        console.log(`📱 连接的设备数量: ${message.data.count}`);
        console.log(`📋 设备列表:`, connectedDevices);
        break;
        
      case 'command_sent':
        console.log('✅ 命令发送成功');
        console.log(`🎯 目标设备: ${message.data.targetDevice}`);
        break;
        
      case 'error':
        console.error('❌ 错误:', message.data.message);
        break;
        
      default:
        console.log('❓ 未知消息类型:', message.type);
    }
    
    showPrompt();
  } catch (error) {
    console.error('❌ 消息解析错误:', error);
    showPrompt();
  }
});

ws.on('error', (error) => {
  console.error('❌ WebSocket错误:', error);
  isConnected = false;
});

ws.on('close', () => {
  console.log('📱 控制端连接关闭');
  isConnected = false;
  process.exit(0);
});

function showMenu() {
  console.log('\n╔══════════════════════════════════════════════════════════════╗');
  console.log('║                                                              ║');
  console.log('║                    简单控制端应用                             ║');
  console.log('║                                                              ║');
  console.log('╚══════════════════════════════════════════════════════════════╝');
  console.log('\n📋 可用命令:');
  console.log('  1. list     - 获取设备列表');
  console.log('  2. send     - 发送命令到设备');
  console.log('  3. ping     - 发送心跳');
  console.log('  4. status   - 显示连接状态');
  console.log('  5. help     - 显示帮助');
  console.log('  6. quit     - 退出应用');
  console.log('');
}

function showPrompt() {
  if (isConnected) {
    rl.question('🎮 控制端 > ', handleCommand);
  }
}

function handleCommand(input) {
  const command = input.trim().toLowerCase();
  
  switch (command) {
    case '1':
    case 'list':
      console.log('📡 获取设备列表...');
      ws.send(JSON.stringify({
        type: 'get_devices'
      }));
      break;
      
    case '2':
    case 'send':
      if (connectedDevices.length === 0) {
        console.log('❌ 没有连接的设备');
        showPrompt();
        return;
      }
      
      console.log('📱 可用设备:');
      connectedDevices.forEach((device, index) => {
        console.log(`  ${index + 1}. ${device}`);
      });
      
      rl.question('选择设备编号: ', (deviceIndex) => {
        const index = parseInt(deviceIndex) - 1;
        if (index >= 0 && index < connectedDevices.length) {
          const targetDevice = connectedDevices[index];
          
          rl.question('输入命令内容: ', (commandContent) => {
            console.log(`📤 发送命令到设备 ${targetDevice}...`);
            ws.send(JSON.stringify({
              type: 'send_command',
              data: {
                targetDevice: targetDevice,
                command: {
                  type: 'custom_command',
                  content: commandContent,
                  timestamp: new Date().toISOString()
                }
              }
            }));
            showPrompt();
          });
        } else {
          console.log('❌ 无效的设备编号');
          showPrompt();
        }
      });
      return;
      
    case '3':
    case 'ping':
      console.log('💓 发送心跳...');
      ws.send(JSON.stringify({
        type: 'heartbeat'
      }));
      break;
      
    case '4':
    case 'status':
      console.log(`🔗 连接状态: ${isConnected ? '已连接' : '未连接'}`);
      console.log(`📱 设备数量: ${connectedDevices.length}`);
      console.log(`📋 设备列表:`, connectedDevices);
      break;
      
    case '5':
    case 'help':
      showMenu();
      break;
      
    case '6':
    case 'quit':
    case 'exit':
      console.log('👋 再见！');
      ws.close();
      rl.close();
      process.exit(0);
      break;
      
    default:
      console.log('❓ 未知命令，输入 help 查看帮助');
      break;
  }
  
  if (command !== '2' && command !== 'send') {
    showPrompt();
  }
}

// 处理Ctrl+C
process.on('SIGINT', () => {
  console.log('\n👋 收到退出信号，正在关闭...');
  ws.close();
  rl.close();
  process.exit(0);
});

// 等待连接建立
setTimeout(() => {
  if (!isConnected) {
    console.log('❌ 连接超时，请检查网关服务是否运行');
    process.exit(1);
  }
}, 5000);
