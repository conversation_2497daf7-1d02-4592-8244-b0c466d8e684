import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/config/app_config.dart';
import '../../../../core/providers/app_state_provider.dart';
import '../../../../core/providers/device_provider.dart';
import '../../../../core/providers/auth_provider.dart';
import '../../../../core/services/websocket_service.dart';
import '../../../../core/services/device_registration_service.dart';
import '../../../../core/services/logger_service.dart';
import '../../../../core/models/device_model.dart';

class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _initializeApp();
  }

  void _setupAnimations() {
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _animationController.forward();
  }

  Future<void> _initializeApp() async {
    try {
      final appStateNotifier = ref.read(appStateProvider.notifier);
      final deviceNotifier = ref.read(deviceProvider.notifier);
      final authNotifier = ref.read(authProvider.notifier);

      // 延迟设置初始化状态，避免在widget构建过程中修改provider
      Future(() => appStateNotifier.setInitializing(true));

      // 延迟显示启动画面
      await Future.delayed(const Duration(seconds: 1));

      // 检查认证状态
      await authNotifier.checkAuthStatus();

      // 初始化设备信息
      await deviceNotifier.initializeDevice();

      // 尝试连接到网关
      try {
        final gatewayUrl = AppConstants.defaultGatewayWsUrl;
        LoggerService.info('正在连接到网关: $gatewayUrl');

        // 初始化WebSocket服务
        final webSocketService = WebSocketService.instance;
        await webSocketService.initialize();

        // 监听WebSocket连接状态变化
        webSocketService.statusStream.listen((status) {
          switch (status) {
            case WebSocketConnectionState.connected:
              LoggerService.info('🎉 WebSocket连接状态更新: 已连接');
              deviceNotifier.setConnected(true, gatewayUrl: gatewayUrl);
              break;
            case WebSocketConnectionState.disconnected:
            case WebSocketConnectionState.error:
              LoggerService.info('💔 WebSocket连接状态更新: 已断开');
              deviceNotifier.setConnected(false);
              break;
            default:
              break;
          }
        });

        // 检查当前状态（防止监听器设置时WebSocket已经连接成功）
        if (webSocketService.isConnected) {
          LoggerService.info('🔄 WebSocket已在连接状态，同步设备状态');
          deviceNotifier.setConnected(true, gatewayUrl: gatewayUrl);
        }

        // 连接到网关
        final connected = await webSocketService.connect(gatewayUrl);
        if (!connected) {
          LoggerService.warning('网关连接失败');
          // 状态更新通过statusStream监听器处理
        }
      } catch (e) {
        LoggerService.error('网关连接错误: $e');
        // 状态更新通过statusStream监听器处理
      }

      // 初始化设备注册服务并尝试注册设备
      try {
        final deviceService = DeviceRegistrationService();
        await deviceService.initialize();
        await deviceService.registerDevice();
        deviceNotifier.updateStatus(DeviceStatus.registered);
        LoggerService.info('设备注册成功');
      } catch (e) {
        deviceNotifier.updateStatus(
          DeviceStatus.error,
          errorMessage: '设备注册失败: $e',
        );
        LoggerService.error('设备注册失败: $e');
      }

      // 延迟完成初始化状态更新
      Future(() => appStateNotifier.setInitializing(false));

      // 等待动画完成
      await _animationController.forward();

      // 更新连接状态
      final deviceState = ref.read(deviceProvider);
      if (deviceState.isConnected) {
        appStateNotifier.setConnectionStatus(ConnectionStatus.connected);
      } else {
        appStateNotifier.setConnectionStatus(ConnectionStatus.disconnected);
      }

      // 导航到主页面
      if (mounted) {
        // 等待一段时间确保所有状态都已更新
        await Future.delayed(const Duration(milliseconds: 300));
        if (mounted) {
          context.go('/screenshot');
        }
      }
    } catch (e) {
      // 处理初始化错误
      final appStateNotifier = ref.read(appStateProvider.notifier);
      appStateNotifier.setError('初始化失败: $e');
      appStateNotifier.setInitializing(false);

      // 记录错误但仍然导航到主页面
      LoggerService.error('初始化错误: $e');
      if (mounted) {
        // 等待确保Overlay可用后再导航
        await Future.delayed(const Duration(milliseconds: 500));
        if (mounted) {
          context.go('/screenshot');
        }
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.primary,
      body: Consumer(
        builder: (context, ref, child) {
          final appState = ref.watch(appStateProvider);
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 应用图标
                AnimatedBuilder(
                  animation: _scaleAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _scaleAnimation.value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.screenshot_monitor,
                          size: 60,
                          color: Colors.blue,
                        ),
                      ),
                    );
                  },
                ),

                const SizedBox(height: 40),

                // 应用标题
                AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value,
                      child: Column(
                        children: [
                          Text(
                            AppConstants.appName,
                            style: Theme.of(context)
                                .textTheme
                                .headlineMedium
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            '终端显示应用',
                            style:
                                Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      color: Colors.white70,
                                    ),
                          ),
                        ],
                      ),
                    );
                  },
                ),

                const SizedBox(height: 60),

                // 加载指示器和状态
                if (appState.isInitializing) ...[
                  const CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    '正在初始化...',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white70,
                        ),
                  ),
                ] else if (appState.hasConnectionError) ...[
                  Icon(
                    Icons.error_outline,
                    color: Colors.red[300],
                    size: 40,
                  ),
                  const SizedBox(height: 20),
                  Text(
                    appState.connectionError ?? '未知错误',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.red[300],
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],

                const SizedBox(height: 40),

                // 版本信息
                AnimatedBuilder(
                  animation: _fadeAnimation,
                  builder: (context, child) {
                    return Opacity(
                      opacity: _fadeAnimation.value * 0.7,
                      child: Text(
                        'v${AppConstants.appVersion}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white54,
                            ),
                      ),
                    );
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
