import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:window_manager/window_manager.dart';

import '../../core/services/display_service.dart';
import '../../core/services/logger_service.dart';
import '../../core/providers/service_providers.dart';
import 'presentation/widgets/display_controls_widget.dart';

/// 显示屏幕
class DisplayScreen extends ConsumerStatefulWidget {
  const DisplayScreen({super.key});

  @override
  ConsumerState<DisplayScreen> createState() => _DisplayScreenState();
}

class _DisplayScreenState extends ConsumerState<DisplayScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  
  ImageData? _currentImage;
  bool _isFullscreen = false;
  
  @override
  void initState() {
    super.initState();
    
    // 初始化动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    // 监听显示状态变化
    _listenToDisplayState();
  }
  
  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }
  
  /// 监听显示状态变化
  void _listenToDisplayState() {
    ref.listen<AsyncValue<DisplayState>>(displayStateStreamProvider, (previous, next) {
      next.whenData((state) {
        _handleDisplayStateChange(state);
      });
    });
  }
  
  /// 处理显示状态变化
  void _handleDisplayStateChange(DisplayState state) {
    // 更新全屏状态
    if (state.isFullscreen != _isFullscreen) {
      _updateFullscreenMode(state.isFullscreen);
    }
    
    // 更新当前图片
    if (state.currentImageId != null) {
      _updateCurrentImage(state.currentImageId!);
    } else {
      _clearCurrentImage();
    }
  }
  
  /// 更新全屏模式
  void _updateFullscreenMode(bool isFullscreen) {
    setState(() {
      _isFullscreen = isFullscreen;
    });
    
    if (isFullscreen) {
      // 进入全屏模式
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
    } else {
      // 退出全屏模式
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
      ]);
    }
  }
  
  /// 更新当前图片
  void _updateCurrentImage(String imageId) {
    final displayService = ref.read(displayServiceProvider);
    final imageData = displayService.getCachedImage(imageId);
    
    if (imageData != null && imageData != _currentImage) {
      setState(() {
        _currentImage = imageData;
      });
      
      // 播放淡入动画
      _fadeController.reset();
      _fadeController.forward();
      
      LoggerService.debug('Updated current image: $imageId');
    }
  }
  
  /// 清除当前图片
  void _clearCurrentImage() {
    if (_currentImage != null) {
      setState(() {
        _currentImage = null;
      });
      
      _fadeController.reset();
      
      LoggerService.debug('Cleared current image');
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final displayState = ref.watch(displayStateStreamProvider);
    
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: () => _onDoubleTap(),
        onSecondaryTap: () => _showContextMenu(),
        child: displayState.when(
          data: (state) => _buildDisplayContent(state),
          loading: () => _buildLoadingContent(),
          error: (error, stack) => _buildErrorContent(error),
        ),
      ),
    );
  }
  
  /// 处理双击事件
  void _onDoubleTap() {
    _toggleFullScreen();
  }
  
  /// 切换全屏
  Future<void> _toggleFullScreen() async {
    try {
      final isFullScreen = await windowManager.isFullScreen();
      await windowManager.setFullScreen(!isFullScreen);
    } catch (error) {
      LoggerService.error('Failed to toggle fullscreen', error);
      // Fallback: try to set fullscreen to true
      try {
        await windowManager.setFullScreen(true);
      } catch (fallbackError) {
        LoggerService.error('Fallback fullscreen failed', fallbackError);
      }
    }
  }
  
  /// 显示右键菜单
  void _showContextMenu() {
    showDialog(
      context: context,
      barrierColor: Colors.transparent,
      builder: (context) {
        return Stack(
          children: [
            // 点击空白区域关闭菜单
            GestureDetector(
              onTap: () => Navigator.of(context).pop(),
              child: Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.transparent,
              ),
            ),
            
            // 右键菜单
            Positioned(
              right: 50,
              top: 50,
              child: ContextMenuWidget(
                onFullScreen: () {
                  Navigator.of(context).pop();
                  _toggleFullScreen();
                },
                onScreenshot: () {
                  Navigator.of(context).pop();
                  _takeScreenshot();
                },
                onSettings: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamed('/settings');
                },
                onExit: () {
                  Navigator.of(context).pop();
                  _exitApp();
                },
              ),
            ),
          ],
        );
      },
    );
  }
  
  /// 截图
  Future<void> _takeScreenshot() async {
    try {
      final screenshotService = ref.read(screenshotServiceProvider);
      final result = await screenshotService.takeScreenshot(
        id: 'display_screenshot_${DateTime.now().millisecondsSinceEpoch}',
      );
      
      if (result.success) {
        _showMessage('截图成功');
      } else {
        _showMessage('截图失败: ${result.error}');
      }
    } catch (error) {
      LoggerService.error('Screenshot failed', error);
      _showMessage('截图失败');
    }
  }
  
  /// 退出应用
  Future<void> _exitApp() async {
    try {
      await windowManager.close();
    } catch (error) {
      LoggerService.error('Failed to close window', error);
    }
  }
  
  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        backgroundColor: Colors.black87,
      ),
    );
  }
  
  /// 构建显示内容
  Widget _buildDisplayContent(DisplayState state) {
    if (!state.isDisplaying || _currentImage == null) {
      return _buildIdleContent(state);
    }
    
    return Container(
      color: state.config.backgroundColor,
      child: Stack(
        children: [
          // 主图片显示
          _buildImageDisplay(state),
          
          // 控制组件
          const DisplayControlsWidget(),
          
          // 状态指示器
          const DisplayStatusIndicator(),
          
          // 图片信息覆盖层
          ImageInfoOverlay(
            imageName: _currentImage?.name ?? '',
            imageSize: _formatFileSize(_currentImage?.data.length ?? 0),
            currentIndex: state.currentIndex,
            totalCount: state.playlist.length,
          ),
        ],
      ),
    );
  }
  
  /// 构建图片显示
  Widget _buildImageDisplay(DisplayState state) {
    return Center(
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          constraints: const BoxConstraints.expand(),
          child: _buildImageWidget(state),
        ),
      ),
    );
  }
  
  /// 构建图片组件
  Widget _buildImageWidget(DisplayState state) {
    if (_currentImage == null) {
      return const SizedBox.shrink();
    }
    
    return Opacity(
      opacity: state.config.opacity,
      child: Image.memory(
        _currentImage!.data,
        fit: state.config.fit,
        errorBuilder: (context, error, stackTrace) {
          LoggerService.error('Failed to display image: ${_currentImage!.id}', error);
          return _buildImageErrorWidget();
        },
      ),
    );
  }
  
  /// 构建图片错误组件
  Widget _buildImageErrorWidget() {
    return Container(
      color: Colors.grey[900],
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '图片加载失败',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
  

  
  /// 构建空闲内容
  Widget _buildIdleContent(DisplayState state) {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.tv,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '等待显示内容...',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 18,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '终端已就绪，等待控制端发送图片',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建加载内容
  Widget _buildLoadingContent() {
    return Container(
      color: Colors.black,
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              '正在初始化显示服务...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建错误内容
  Widget _buildErrorContent(Object error) {
    return Container(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              '显示服务错误',
              style: TextStyle(
                color: Colors.red,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                // 重新初始化显示服务
                ref.read(displayServiceProvider).initialize();
              },
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 格式化文件大小
  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }
}

/// 显示屏幕路由
class DisplayScreenRoute {
  static const String name = '/display';
  
  static Route<void> route() {
    return MaterialPageRoute<void>(
      settings: const RouteSettings(name: name),
      builder: (_) => const DisplayScreen(),
    );
  }
}