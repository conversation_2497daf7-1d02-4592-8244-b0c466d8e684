import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 存储服务
/// 负责应用数据的本地持久化存储
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  /// 获取单例实例
  static StorageService get instance => _instance;

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  /// 初始化存储服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      debugPrint('StorageService initialized');
    } catch (e) {
      debugPrint('Error initializing StorageService: $e');
      rethrow;
    }
  }

  /// 确保服务已初始化
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// 存储字符串值
  Future<bool> setString(String key, String value) async {
    await _ensureInitialized();
    try {
      return await _prefs!.setString(key, value);
    } catch (e) {
      debugPrint('Error setting string value for key $key: $e');
      return false;
    }
  }

  /// 获取字符串值
  Future<String?> getString(String key) async {
    await _ensureInitialized();
    try {
      return _prefs!.getString(key);
    } catch (e) {
      debugPrint('Error getting string value for key $key: $e');
      return null;
    }
  }

  /// 存储整数值
  Future<bool> setInt(String key, int value) async {
    await _ensureInitialized();
    try {
      return await _prefs!.setInt(key, value);
    } catch (e) {
      debugPrint('Error setting int value for key $key: $e');
      return false;
    }
  }

  /// 获取整数值
  Future<int?> getInt(String key) async {
    await _ensureInitialized();
    try {
      return _prefs!.getInt(key);
    } catch (e) {
      debugPrint('Error getting int value for key $key: $e');
      return null;
    }
  }

  /// 存储双精度浮点数值
  Future<bool> setDouble(String key, double value) async {
    await _ensureInitialized();
    try {
      return await _prefs!.setDouble(key, value);
    } catch (e) {
      debugPrint('Error setting double value for key $key: $e');
      return false;
    }
  }

  /// 获取双精度浮点数值
  Future<double?> getDouble(String key) async {
    await _ensureInitialized();
    try {
      return _prefs!.getDouble(key);
    } catch (e) {
      debugPrint('Error getting double value for key $key: $e');
      return null;
    }
  }

  /// 存储布尔值
  Future<bool> setBool(String key, bool value) async {
    await _ensureInitialized();
    try {
      return await _prefs!.setBool(key, value);
    } catch (e) {
      debugPrint('Error setting bool value for key $key: $e');
      return false;
    }
  }

  /// 获取布尔值
  Future<bool?> getBool(String key) async {
    await _ensureInitialized();
    try {
      return _prefs!.getBool(key);
    } catch (e) {
      debugPrint('Error getting bool value for key $key: $e');
      return null;
    }
  }

  /// 存储字符串列表
  Future<bool> setStringList(String key, List<String> value) async {
    await _ensureInitialized();
    try {
      return await _prefs!.setStringList(key, value);
    } catch (e) {
      debugPrint('Error setting string list for key $key: $e');
      return false;
    }
  }

  /// 获取字符串列表
  Future<List<String>?> getStringList(String key) async {
    await _ensureInitialized();
    try {
      return _prefs!.getStringList(key);
    } catch (e) {
      debugPrint('Error getting string list for key $key: $e');
      return null;
    }
  }

  /// 存储Map对象（JSON序列化）
  Future<bool> setMap(String key, Map<String, dynamic> value) async {
    await _ensureInitialized();
    try {
      final jsonString = jsonEncode(value);
      return await _prefs!.setString(key, jsonString);
    } catch (e) {
      debugPrint('Error setting map for key $key: $e');
      return false;
    }
  }

  /// 获取Map对象（JSON反序列化）
  Future<Map<String, dynamic>?> getMap(String key) async {
    await _ensureInitialized();
    try {
      final jsonString = _prefs!.getString(key);
      if (jsonString == null) return null;
      return Map<String, dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      debugPrint('Error getting map for key $key: $e');
      return null;
    }
  }

  /// 存储List对象（JSON序列化）
  Future<bool> setList(String key, List<dynamic> value) async {
    await _ensureInitialized();
    try {
      final jsonString = jsonEncode(value);
      return await _prefs!.setString(key, jsonString);
    } catch (e) {
      debugPrint('Error setting list for key $key: $e');
      return false;
    }
  }

  /// 获取List对象（JSON反序列化）
  Future<List<dynamic>?> getList(String key) async {
    await _ensureInitialized();
    try {
      final jsonString = _prefs!.getString(key);
      if (jsonString == null) return null;
      return List<dynamic>.from(jsonDecode(jsonString));
    } catch (e) {
      debugPrint('Error getting list for key $key: $e');
      return null;
    }
  }

  /// JSON编码静态方法
  static String encodeJson(dynamic data) {
    return jsonEncode(data);
  }

  /// JSON解码静态方法
  static dynamic decodeJson(String jsonString) {
    return jsonDecode(jsonString);
  }

  /// 检查键是否存在
  Future<bool> containsKey(String key) async {
    await _ensureInitialized();
    try {
      return _prefs!.containsKey(key);
    } catch (e) {
      debugPrint('Error checking key existence for $key: $e');
      return false;
    }
  }

  /// 删除指定键的值
  Future<bool> remove(String key) async {
    await _ensureInitialized();
    try {
      return await _prefs!.remove(key);
    } catch (e) {
      debugPrint('Error removing key $key: $e');
      return false;
    }
  }

  /// 删除多个键的值
  Future<bool> removeKeys(List<String> keys) async {
    await _ensureInitialized();
    try {
      bool allSuccess = true;
      for (final key in keys) {
        final success = await _prefs!.remove(key);
        if (!success) allSuccess = false;
      }
      return allSuccess;
    } catch (e) {
      debugPrint('Error removing keys $keys: $e');
      return false;
    }
  }

  /// 清除所有存储的数据
  Future<bool> clear() async {
    await _ensureInitialized();
    try {
      return await _prefs!.clear();
    } catch (e) {
      debugPrint('Error clearing storage: $e');
      return false;
    }
  }

  /// 获取所有键
  Future<Set<String>> getKeys() async {
    await _ensureInitialized();
    try {
      return _prefs!.getKeys();
    } catch (e) {
      debugPrint('Error getting all keys: $e');
      return <String>{};
    }
  }

  /// 获取所有键值对
  Future<Map<String, dynamic>> getAll() async {
    await _ensureInitialized();
    try {
      final keys = _prefs!.getKeys();
      final Map<String, dynamic> result = {};
      
      for (final key in keys) {
        final value = _prefs!.get(key);
        result[key] = value;
      }
      
      return result;
    } catch (e) {
      debugPrint('Error getting all key-value pairs: $e');
      return {};
    }
  }

  /// 批量设置键值对
  Future<bool> setBatch(Map<String, dynamic> data) async {
    await _ensureInitialized();
    try {
      bool allSuccess = true;
      
      for (final entry in data.entries) {
        final key = entry.key;
        final value = entry.value;
        
        bool success = false;
        if (value is String) {
          success = await _prefs!.setString(key, value);
        } else if (value is int) {
          success = await _prefs!.setInt(key, value);
        } else if (value is double) {
          success = await _prefs!.setDouble(key, value);
        } else if (value is bool) {
          success = await _prefs!.setBool(key, value);
        } else if (value is List<String>) {
          success = await _prefs!.setStringList(key, value);
        } else {
          // 对于其他类型，尝试JSON序列化
          try {
            final jsonString = jsonEncode(value);
            success = await _prefs!.setString(key, jsonString);
          } catch (e) {
            debugPrint('Error serializing value for key $key: $e');
            success = false;
          }
        }
        
        if (!success) allSuccess = false;
      }
      
      return allSuccess;
    } catch (e) {
      debugPrint('Error setting batch data: $e');
      return false;
    }
  }

  /// 获取存储大小（估算）
  Future<int> getStorageSize() async {
    await _ensureInitialized();
    try {
      final allData = await getAll();
      final jsonString = jsonEncode(allData);
      return jsonString.length;
    } catch (e) {
      debugPrint('Error calculating storage size: $e');
      return 0;
    }
  }

  /// 导出所有数据为JSON字符串
  Future<String?> exportData() async {
    try {
      final allData = await getAll();
      return jsonEncode(allData);
    } catch (e) {
      debugPrint('Error exporting data: $e');
      return null;
    }
  }

  /// 从JSON字符串导入数据
  Future<bool> importData(String jsonData) async {
    try {
      final data = Map<String, dynamic>.from(jsonDecode(jsonData));
      return await setBatch(data);
    } catch (e) {
      debugPrint('Error importing data: $e');
      return false;
    }
  }

  /// 备份数据到指定前缀
  Future<bool> backupData(String backupPrefix) async {
    try {
      final allData = await getAll();
      final backupData = <String, dynamic>{};
      
      for (final entry in allData.entries) {
        backupData['${backupPrefix}_${entry.key}'] = entry.value;
      }
      
      return await setBatch(backupData);
    } catch (e) {
      debugPrint('Error backing up data: $e');
      return false;
    }
  }

  /// 从备份恢复数据
  Future<bool> restoreData(String backupPrefix) async {
    try {
      final allData = await getAll();
      final restoreData = <String, dynamic>{};
      
      for (final entry in allData.entries) {
        if (entry.key.startsWith('${backupPrefix}_')) {
          final originalKey = entry.key.substring(backupPrefix.length + 1);
          restoreData[originalKey] = entry.value;
        }
      }
      
      if (restoreData.isNotEmpty) {
        return await setBatch(restoreData);
      }
      
      return true;
    } catch (e) {
      debugPrint('Error restoring data: $e');
      return false;
    }
  }

  /// 删除备份数据
  Future<bool> deleteBackup(String backupPrefix) async {
    try {
      final allKeys = await getKeys();
      final backupKeys = allKeys
          .where((key) => key.startsWith('${backupPrefix}_'))
          .toList();
      
      if (backupKeys.isNotEmpty) {
        return await removeKeys(backupKeys);
      }
      
      return true;
    } catch (e) {
      debugPrint('Error deleting backup: $e');
      return false;
    }
  }

  /// 获取存储统计信息
  Future<Map<String, dynamic>> getStorageStats() async {
    try {
      final allData = await getAll();
      final keys = await getKeys();
      final size = await getStorageSize();
      
      final stats = {
        'totalKeys': keys.length,
        'totalSize': size,
        'averageKeySize': keys.isNotEmpty ? size / keys.length : 0,
        'keyTypes': <String, int>{},
        'lastUpdated': DateTime.now().toIso8601String(),
      };
      
      // 统计不同类型的键数量
      final keyTypes = stats['keyTypes'] as Map<String, int>;
      for (final entry in allData.entries) {
        final valueType = entry.value.runtimeType.toString();
        keyTypes[valueType] = (keyTypes[valueType] ?? 0) + 1;
      }
      
      return stats;
    } catch (e) {
      debugPrint('Error getting storage stats: $e');
      return {
        'error': 'Failed to get storage stats: $e',
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 清理过期数据（根据键名模式）
  Future<bool> cleanupExpiredData({
    required String keyPattern,
    required Duration maxAge,
  }) async {
    try {
      final allKeys = await getKeys();
      final expiredKeys = <String>[];
      final cutoffTime = DateTime.now().subtract(maxAge);
      
      for (final key in allKeys) {
        if (key.contains(keyPattern)) {
          // 尝试从键名或值中获取时间戳
          final timestampKey = '${key}_timestamp';
          final timestampString = await getString(timestampKey);
          
          if (timestampString != null) {
            try {
              final timestamp = DateTime.parse(timestampString);
              if (timestamp.isBefore(cutoffTime)) {
                expiredKeys.add(key);
                expiredKeys.add(timestampKey);
              }
            } catch (e) {
              debugPrint('Error parsing timestamp for key $key: $e');
            }
          }
        }
      }
      
      if (expiredKeys.isNotEmpty) {
        return await removeKeys(expiredKeys);
      }
      
      return true;
    } catch (e) {
      debugPrint('Error cleaning up expired data: $e');
      return false;
    }
  }

  /// 检查服务是否已初始化
  bool get isInitialized => _isInitialized;

  /// 获取SharedPreferences实例（仅供内部使用）
  SharedPreferences? get prefs => _prefs;

  // 用户数据相关方法
  /// 保存用户数据
  Future<bool> saveUserData(Map<String, dynamic> userData) async {
    try {
      final jsonString = jsonEncode(userData);
      return await setString('user_data', jsonString);
    } catch (e) {
      debugPrint('Error saving user data: $e');
      return false;
    }
  }

  /// 获取用户数据
  Future<Map<String, dynamic>?> getUserData() async {
    try {
      final jsonString = await getString('user_data');
      if (jsonString != null) {
        return Map<String, dynamic>.from(jsonDecode(jsonString));
      }
      return null;
    } catch (e) {
      debugPrint('Error getting user data: $e');
      return null;
    }
  }

  /// 清除用户数据
  Future<bool> clearUserData() async {
    try {
      return await remove('user_data');
    } catch (e) {
      debugPrint('Error clearing user data: $e');
      return false;
    }
  }
}