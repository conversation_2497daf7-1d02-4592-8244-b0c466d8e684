#!/usr/bin/env node

/**
 * Flutter Image Control Gateway - Main Entry Point
 * 
 * This is the main entry point for the Flutter Image Control Gateway service.
 * It initializes and starts the gateway server with all required services.
 */

import gatewayServer from './server';
import { config } from './config';

// ASCII Art Banner
const banner = `
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    Flutter Image Control Gateway                             ║
║    Version: 1.0.0                                           ║
║                                                              ║
║    🖼️  Multi-device screenshot capture and display          ║
║    🔄  Real-time image synchronization                       ║
║    📱  Flutter mobile control interface                      ║
║    🌐  WebSocket-based communication                         ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
`;

// Display banner
console.log(banner);

// Environment information
console.log('🔧 Environment Information:');
console.log(`   Node.js: ${process.version}`);
console.log(`   Platform: ${process.platform}`);
console.log(`   Architecture: ${process.arch}`);
console.log(`   Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`   Working Directory: ${process.cwd()}`);
console.log('');

// Start the gateway server
gatewayServer.start().catch((error) => {
  console.error('❌ Critical error starting gateway server:', error);
  
  // Log additional error details in development
  if (process.env.NODE_ENV === 'development') {
    console.error('Stack trace:', error.stack);
  }
  
  // Exit with error code
  process.exit(1);
});

// Export for programmatic use
export default gatewayServer;
export { config };