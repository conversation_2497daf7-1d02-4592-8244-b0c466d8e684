{"name": "flutter-imgct-system", "private": true, "version": "1.0.0", "type": "module", "description": "Flutter图片切换系统 - 跨平台分布式图片控制系统", "scripts": {"gateway:dev": "nodemon gateway_service/src/app.ts", "gateway:build": "tsc -p gateway_service/tsconfig.json", "gateway:start": "node gateway_service/dist/app.js", "control:flutter": "cd control_app && flutter run", "terminal:flutter": "cd terminal_app && flutter run", "build:all": "npm run gateway:build && cd control_app && flutter build windows && cd ../terminal_app && flutter build windows", "dev": "npm run gateway:dev", "lint": "eslint gateway_service/src", "test": "echo \"Tests will be added in development phase\""}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.4", "sharp": "^0.33.2", "socket.io": "^4.7.5", "socket.io-client": "^4.8.1", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "ws": "^8.16.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^22.15.30", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "concurrently": "^9.2.0", "eslint": "^9.25.0", "globals": "^16.0.0", "nodemon": "^3.1.10", "tsx": "^4.20.3", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1"}}