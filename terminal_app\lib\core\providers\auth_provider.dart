import 'package:flutter_riverpod/flutter_riverpod.dart';

// 认证状态枚举
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

// 用户数据类
class User {
  final String id;
  final String username;
  final String email;
  final String? avatar;

  const User({
    required this.id,
    required this.username,
    required this.email,
    this.avatar,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      username: json['username'] as String,
      email: json['email'] as String,
      avatar: json['avatar'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'avatar': avatar,
    };
  }
}

// 认证状态数据类
class AuthState {
  final AuthStatus status;
  final User? user;
  final String? error;
  final bool isLoading;

  const AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.error,
    this.isLoading = false,
  });

  AuthState copyWith({
    AuthStatus? status,
    User? user,
    String? error,
    bool? isLoading,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      error: error ?? this.error,
      isLoading: isLoading ?? this.isLoading,
    );
  }

  bool get isAuthenticated => status == AuthStatus.authenticated && user != null;
}

// 认证 Provider
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthState());

  // 登录
  Future<void> login(String username, String password) async {
    state = state.copyWith(status: AuthStatus.loading, isLoading: true);
    
    try {
      // 模拟登录请求
      await Future.delayed(const Duration(seconds: 1));
      
      // 模拟成功登录
      final user = User(
        id: '1',
        username: username,
        email: '$<EMAIL>',
      );
      
      state = state.copyWith(
        status: AuthStatus.authenticated,
        user: user,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  // 登出
  Future<void> logout() async {
    state = state.copyWith(status: AuthStatus.loading, isLoading: true);
    
    try {
      // 模拟登出请求
      await Future.delayed(const Duration(milliseconds: 500));
      
      state = const AuthState(
        status: AuthStatus.unauthenticated,
        user: null,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  // 检查认证状态
  Future<void> checkAuthStatus() async {
    state = state.copyWith(status: AuthStatus.loading, isLoading: true);
    
    try {
      // 模拟检查认证状态
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 这里应该检查本地存储或服务器验证token
      // 暂时模拟为未认证状态
      state = state.copyWith(
        status: AuthStatus.unauthenticated,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        error: e.toString(),
        isLoading: false,
      );
    }
  }

  // 清除错误
  void clearError() {
    state = state.copyWith(error: null);
  }
}

// 认证 Provider 实例
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});