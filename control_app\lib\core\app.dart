import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../features/auth/presentation/pages/login_page.dart';
import '../features/dashboard/presentation/pages/main_dashboard.dart';
import '../features/splash/presentation/pages/splash_page.dart';
import 'theme/app_theme.dart';
import 'providers/auth_provider.dart';
import 'providers/app_state_provider.dart';

class FlutterImgctControlApp extends ConsumerWidget {
  const FlutterImgctControlApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appState = ref.watch(appStateProvider);
    final authState = ref.watch(authProvider);

    return MaterialApp(
      title: 'Flutter图片切换控制系统',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      themeMode: ThemeMode.light,
      locale: appState.locale,
      // onGenerateRoute: AppRoutes.generateRoute,
      home: _buildHomePage(appState, authState),
    );
  }

  Widget _buildHomePage(AppState appState, AuthState authState) {
    // Show splash screen during initialization
    if (appState.isInitializing) {
      return const SplashPage();
    }

    // Show login page if not authenticated
    if (authState != AuthState.authenticated) {
      return const LoginPage();
    }

    // Show main dashboard if authenticated
    return const MainDashboard();
  }
}
