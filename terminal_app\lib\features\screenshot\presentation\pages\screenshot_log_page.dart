import 'dart:io';
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/services/screenshot_log_service.dart';
import '../../../../core/services/logger_service.dart';
import 'image_viewer_page.dart';

class ScreenshotLogPage extends ConsumerStatefulWidget {
  const ScreenshotLogPage({super.key});

  @override
  ConsumerState<ScreenshotLogPage> createState() => _ScreenshotLogPageState();
}

class _ScreenshotLogPageState extends ConsumerState<ScreenshotLogPage>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late TabController _tabController;

  List<DateTime> _availableDates = [];
  Map<String, List<ScreenshotLogEntry>> _logsByDate = {};
  Map<String, dynamic>? _logStats;
  bool _isLoading = true;
  String? _error;
  Timer? _refreshTimer;
  int _currentTabIndex = 0; // 保持当前标签索引
  bool _tabControllerInitialized = false; // 标记TabController是否已初始化
  DateTime? _lastCacheUpdate; // 缓存更新时间

  @override
  bool get wantKeepAlive => true; // 保持页面状态

  @override
  void initState() {
    super.initState();
    _loadLogData();
    _startAutoRefresh();
    _listenToLogStream(); // 监听日志流
  }

  /// 监听日志流，实时更新新日志
  void _listenToLogStream() {
    ScreenshotLogService.instance.logStream.listen((newLog) {
      if (mounted) {
        final dateStr = DateFormat('yyyy-MM-dd').format(newLog.timestamp);
        setState(() {
          if (_logsByDate.containsKey(dateStr)) {
            // 在列表开头插入新日志（最新的在前）
            final currentLogs =
                List<ScreenshotLogEntry>.from(_logsByDate[dateStr]!);
            currentLogs.insert(0, newLog);
            _logsByDate[dateStr] = currentLogs;
          } else {
            // 如果是新的日期，创建新的日志列表
            _logsByDate[dateStr] = [newLog];
          }
        });
      }
    });
  }

  /// 开始自动刷新
  void _startAutoRefresh() {
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _refreshCurrentData(); // 只刷新当前数据，不重置所有数据
      }
    });
  }

  /// 刷新当前数据（智能刷新）
  Future<void> _refreshCurrentData() async {
    try {
      // 只刷新今天的数据和统计信息
      final today = DateTime.now();
      final todayStr = DateFormat('yyyy-MM-dd').format(today);

      // 更新今天的日志
      final todayLogs =
          await ScreenshotLogService.instance.getLogsByDate(today);
      final newStats = await ScreenshotLogService.instance.getLogStats(days: 7);

      setState(() {
        if (_logsByDate.containsKey(todayStr)) {
          _logsByDate[todayStr] = todayLogs;
        }
        _logStats = newStats;
        _lastCacheUpdate = DateTime.now();
      });
    } catch (error) {
      // 静默失败，不影响用户体验
      LoggerService.warning('Auto refresh failed: $error');
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _refreshTimer?.cancel();
    super.dispose();
  }

  Future<void> _loadLogData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // 获取可用的日志日期
      final dates = await ScreenshotLogService.instance.getAvailableLogDates();

      // 如果没有日志，至少显示今天
      if (dates.isEmpty) {
        dates.add(DateTime.now());
      }

      // 获取统计信息
      final stats = await ScreenshotLogService.instance.getLogStats(days: 7);

      // 初始化TabController（只在第一次或日期数量变化时）
      if (!_tabControllerInitialized || _tabController.length != dates.length) {
        if (_tabControllerInitialized) {
          // 保存当前标签索引
          _currentTabIndex = _tabController.index;
          _tabController.dispose();
        }
        _tabController = TabController(length: dates.length, vsync: this);
        // 恢复标签索引（确保不超出范围）
        if (_currentTabIndex < dates.length) {
          _tabController.index = _currentTabIndex;
        } else {
          _currentTabIndex = 0;
          _tabController.index = 0;
        }
        // 添加监听器保存标签状态
        _tabController.addListener(() {
          if (_tabController.indexIsChanging) {
            _currentTabIndex = _tabController.index;
          }
        });
        _tabControllerInitialized = true;
      }

      // 加载每个日期的日志（保持现有数据，只更新变化的部分）
      final logsByDate =
          Map<String, List<ScreenshotLogEntry>>.from(_logsByDate);

      for (final date in dates) {
        final dateStr = DateFormat('yyyy-MM-dd').format(date);
        // 只在缓存中没有或需要刷新时才加载
        if (!logsByDate.containsKey(dateStr) ||
            (_lastCacheUpdate == null ||
                DateTime.now().difference(_lastCacheUpdate!).inMinutes > 2)) {
          final logs = await ScreenshotLogService.instance.getLogsByDate(date);
          logsByDate[dateStr] = logs;
        }
      }

      setState(() {
        _availableDates = dates;
        _logsByDate = logsByDate;
        _logStats = stats;
        _isLoading = false;
        _lastCacheUpdate = DateTime.now(); // 更新缓存时间
      });
    } catch (error) {
      LoggerService.error('Failed to load log data', error);
      setState(() {
        _error = error.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 支持AutomaticKeepAliveClientMixin
    return Scaffold(
      appBar: AppBar(
        title: const Text('截图日志'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLogData,
          ),
        ],
        bottom: _isLoading || _availableDates.isEmpty
            ? null
            : TabBar(
                controller: _tabController,
                isScrollable: true,
                labelColor: Colors.white,
                unselectedLabelColor: Colors.white70,
                indicatorColor: Colors.white,
                tabs: _availableDates.map((date) {
                  final dateStr = DateFormat('MM-dd').format(date);
                  final isToday = _isToday(date);
                  return Tab(
                    text: isToday ? '今天 ($dateStr)' : dateStr,
                  );
                }).toList(),
              ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return _buildErrorView();
    }

    if (_availableDates.isEmpty) {
      return _buildEmptyView();
    }

    return Column(
      children: [
        // 统计信息卡片
        if (_logStats != null) _buildStatsCard(),

        // 日志列表
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: _availableDates.map((date) {
              final dateStr = DateFormat('yyyy-MM-dd').format(date);
              final logs = _logsByDate[dateStr] ?? [];
              return _buildLogList(date, logs);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildStatsCard() {
    final stats = _logStats!;

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '最近7天统计',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    '总数',
                    '${stats['totalCount']}',
                    Icons.photo_camera,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '成功',
                    '${stats['successCount']}',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '失败',
                    '${stats['failureCount']}',
                    Icons.error,
                    Colors.red,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    '成功率',
                    '${stats['successRate']}%',
                    Icons.trending_up,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildLogList(DateTime date, List<ScreenshotLogEntry> logs) {
    if (logs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_library_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '这一天还没有截图记录',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      key: PageStorageKey('log_list_${DateFormat('yyyy-MM-dd').format(date)}'),
      padding: const EdgeInsets.all(16),
      itemCount: logs.length,
      itemBuilder: (context, index) {
        final log = logs[index];
        return _buildLogItem(log);
      },
    );
  }

  Widget _buildLogItem(ScreenshotLogEntry log) {
    final hasImage = log.filePath != null;
    final imageExists = hasImage ? File(log.filePath!).existsSync() : false;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部信息行
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: log.success ? Colors.green : Colors.red,
                  radius: 16,
                  child: Icon(
                    log.success ? Icons.check : Icons.close,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getActionText(log.action),
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        log.formattedTime,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                // 状态指示器
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: log.success
                        ? Colors.green.withOpacity(0.1)
                        : Colors.red.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: log.success ? Colors.green : Colors.red,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    log.statusText,
                    style: TextStyle(
                      fontSize: 10,
                      color: log.success ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            // 错误信息（如果有）
            if (log.error != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.red.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 16,
                      color: Colors.red,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '错误: ${log.error}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.red,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // 图片信息和查看按钮
            if (hasImage) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: imageExists
                      ? Colors.blue.withOpacity(0.1)
                      : Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: imageExists
                        ? Colors.blue.withOpacity(0.3)
                        : Colors.grey.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          imageExists ? Icons.image : Icons.broken_image,
                          size: 16,
                          color: imageExists ? Colors.blue : Colors.grey,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            _getFileName(log.filePath!),
                            style: TextStyle(
                              fontSize: 12,
                              color: imageExists ? Colors.blue : Colors.grey,
                              fontWeight: FontWeight.w500,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    if (imageExists) ...[
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => _viewImage(log.filePath!, log.id),
                          icon: const Icon(
                            Icons.visibility,
                            size: 16,
                          ),
                          label: const Text(
                            '查看图片',
                            style: TextStyle(fontSize: 12),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 12,
                            ),
                            minimumSize: const Size(0, 32),
                          ),
                        ),
                      ),
                    ] else ...[
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          '图片文件不存在或已被删除',
                          style: TextStyle(
                            fontSize: 11,
                            color: Colors.grey,
                            fontStyle: FontStyle.italic,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              '加载日志失败',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: const TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadLogData,
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '还没有截图记录',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '开始截图后，记录会显示在这里',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  void _viewImage(String imagePath, String logId) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ImageViewerPage(
          imagePath: imagePath,
          title: '截图 - $logId',
        ),
      ),
    );
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  String _getActionText(String action) {
    switch (action) {
      case 'take_screenshot':
        return '立即截图';
      case 'scheduled_screenshot':
        return '定时截图';
      case 'batch_screenshot':
        return '批量截图';
      default:
        return action;
    }
  }

  String _getFileName(String filePath) {
    return filePath.split('/').last;
  }
}
