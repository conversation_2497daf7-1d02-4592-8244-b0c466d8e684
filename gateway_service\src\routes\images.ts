import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import sharp from 'sharp';
import { v4 as uuidv4 } from 'uuid';
import { databaseService } from '../services/DatabaseService';
import { WebSocketService } from '../services/WebSocketService';

const router = express.Router();

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = path.join(process.cwd(), 'uploads', 'images');
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}_${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, and BMP are allowed.'));
    }
  }
});

// Generate thumbnail
const generateThumbnail = async (imagePath: string, filename: string): Promise<string> => {
  const thumbnailDir = path.join(process.cwd(), 'uploads', 'thumbnails');
  if (!fs.existsSync(thumbnailDir)) {
    fs.mkdirSync(thumbnailDir, { recursive: true });
  }
  
  const thumbnailName = `thumb_${filename}`;
  const thumbnailPath = path.join(thumbnailDir, thumbnailName);
  
  await sharp(imagePath)
    .resize(200, 200, {
      fit: 'inside',
      withoutEnlargement: true
    })
    .jpeg({ quality: 80 })
    .toFile(thumbnailPath);
  
  return `/thumbnails/${thumbnailName}`;
};

// Upload image
router.post('/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
      return;
    }
    
    const { deviceId, timestamp, metadata } = req.body;
    
    if (!deviceId) {
      res.status(400).json({
        success: false,
        error: 'Device ID is required'
      });
      return;
    }
    
    const file = req.file; // Type assertion for non-null
    
    const imageId = `img_${uuidv4()}`;
    const imagePath = `/images/${file.filename}`;
    
    // Generate thumbnail
    const thumbnailPath = await generateThumbnail(file.path, file.filename);
    
    // Save image info to database
    await databaseService.query(
      `INSERT INTO images (id, filename, original_name, device_id, file_size, format, metadata, thumbnail_path)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        imageId,
        file.filename,
        file.originalname,
        deviceId,
        file.size,
        path.extname(file.filename).slice(1),
        metadata || '{}',
        thumbnailPath
      ]
    );
    
    const imageInfo = {
      id: imageId,
      filename: file.filename,
      originalName: file.originalname,
      deviceId,
      url: imagePath,
      thumbnail: thumbnailPath,
      fileSize: file.size,
      format: path.extname(file.filename).slice(1),
      timestamp: timestamp || new Date().toISOString()
    };
    
    // Notify all clients about new image
    WebSocketService.getInstance().broadcastToClients('image_uploaded', imageInfo);
    
    res.status(201).json({
      success: true,
      data: imageInfo,
      message: 'Image uploaded successfully'
    });
  } catch (error) {
    console.error('Error uploading image:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to upload image'
    });
  }
});

// Get all images
router.get('/', async (req, res) => {
  try {
    const { deviceId, limit = 50, offset = 0, sortBy = 'created_at', order = 'DESC' } = req.query;
    
    let query = 'SELECT * FROM images';
    const params: any[] = [];
    
    if (deviceId) {
      query += ' WHERE device_id = ?';
      params.push(deviceId);
    }
    
    query += ` ORDER BY ${sortBy} ${order} LIMIT ? OFFSET ?`;
    params.push(parseInt(limit as string), parseInt(offset as string));
    
    const images = await databaseService.query(query, params);
    
    // Get total count
    let countQuery = 'SELECT COUNT(*) as total FROM images';
    const countParams: any[] = [];
    
    if (deviceId) {
      countQuery += ' WHERE device_id = ?';
      countParams.push(deviceId);
    }
    
    const countResult = await databaseService.query(countQuery, countParams);
    const total = countResult[0].total;
    
    res.json({
      success: true,
      data: images.map(img => ({
        ...img,
        url: `/images/${img.filename}`,
        metadata: JSON.parse(img.metadata || '{}')
      })),
      pagination: {
        total,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore: parseInt(offset as string) + parseInt(limit as string) < total
      }
    });
  } catch (error) {
    console.error('Error fetching images:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch images'
    });
  }
});

// Get image by ID
router.get('/:imageId', async (req, res) => {
  try {
    const { imageId } = req.params;
    
    const images = await databaseService.query(
      'SELECT * FROM images WHERE id = ?',
      [imageId]
    );
    
    if (images.length === 0) {
      res.status(404).json({
        success: false,
        error: 'Image not found'
      });
      return;
    }
    
    const image = images[0];
    
    res.json({
      success: true,
      data: {
        ...image,
        url: `/images/${image.filename}`,
        metadata: JSON.parse(image.metadata || '{}')
      }
    });
  } catch (error) {
    console.error('Error fetching image:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch image'
    });
  }
});

// Delete image
router.delete('/:imageId', async (req, res) => {
  try {
    const { imageId } = req.params;
    
    // Get image info first
    const images = await databaseService.query(
      'SELECT * FROM images WHERE id = ?',
      [imageId]
    );
    
    if (images.length === 0) {
      res.status(404).json({
        success: false,
        error: 'Image not found'
      });
      return;
    }
    
    const image = images[0];
    
    // Delete files from filesystem
    const imagePath = path.join(process.cwd(), 'uploads', 'images', image.filename);
    const thumbnailPath = path.join(process.cwd(), 'uploads', 'thumbnails', `thumb_${image.filename}`);
    
    try {
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
      if (fs.existsSync(thumbnailPath)) {
        fs.unlinkSync(thumbnailPath);
      }
    } catch (fileError) {
      console.warn('Error deleting files:', fileError);
    }
    
    // Delete from database
    await databaseService.query('DELETE FROM images WHERE id = ?', [imageId]);
    
    // Notify all clients about image deletion
    WebSocketService.getInstance().broadcastToClients('image_deleted', { imageId });
    
    res.json({
      success: true,
      message: 'Image deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting image:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete image'
    });
  }
});

// Batch delete images
router.delete('/batch/delete', async (req, res) => {
  try {
    const { imageIds } = req.body;
    
    if (!Array.isArray(imageIds) || imageIds.length === 0) {
      res.status(400).json({
        success: false,
        error: 'Image IDs array is required'
      });
      return;
    }
    
    const placeholders = imageIds.map(() => '?').join(',');
    const images = await databaseService.query(
      `SELECT * FROM images WHERE id IN (${placeholders})`,
      imageIds
    );
    
    // Delete files from filesystem
    for (const image of images) {
      const imagePath = path.join(process.cwd(), 'uploads', 'images', image.filename);
      const thumbnailPath = path.join(process.cwd(), 'uploads', 'thumbnails', `thumb_${image.filename}`);
      
      try {
        if (fs.existsSync(imagePath)) {
          fs.unlinkSync(imagePath);
        }
        if (fs.existsSync(thumbnailPath)) {
          fs.unlinkSync(thumbnailPath);
        }
      } catch (fileError) {
        console.warn('Error deleting files:', fileError);
      }
    }
    
    // Delete from database
    await databaseService.query(
      `DELETE FROM images WHERE id IN (${placeholders})`,
      imageIds
    );
    
    // Notify all clients about batch deletion
    WebSocketService.getInstance().broadcastToClients('images_batch_deleted', { imageIds });
    
    res.json({
      success: true,
      message: `${images.length} images deleted successfully`,
      deletedCount: images.length
    });
  } catch (error) {
    console.error('Error batch deleting images:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete images'
    });
  }
});

// Search images
router.get('/search/query', async (req, res) => {
  try {
    const { q, deviceId, startDate, endDate, format } = req.query;
    
    let query = 'SELECT * FROM images WHERE 1=1';
    const params: any[] = [];
    
    if (q) {
      query += ' AND (original_name LIKE ? OR metadata LIKE ?)';
      params.push(`%${q}%`, `%${q}%`);
    }
    
    if (deviceId) {
      query += ' AND device_id = ?';
      params.push(deviceId);
    }
    
    if (startDate) {
      query += ' AND created_at >= ?';
      params.push(startDate);
    }
    
    if (endDate) {
      query += ' AND created_at <= ?';
      params.push(endDate);
    }
    
    if (format) {
      query += ' AND format = ?';
      params.push(format);
    }
    
    query += ' ORDER BY created_at DESC LIMIT 100';
    
    const images = await databaseService.query(query, params);
    
    res.json({
      success: true,
      data: images.map(img => ({
        ...img,
        url: `/images/${img.filename}`,
        metadata: JSON.parse(img.metadata || '{}')
      })),
      count: images.length
    });
  } catch (error) {
    console.error('Error searching images:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to search images'
    });
  }
});

export default router;