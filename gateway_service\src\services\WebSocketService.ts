import { Server as SocketIOServer, Socket } from 'socket.io';
import { Server as HttpServer } from 'http';
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { databaseService } from './DatabaseService';
import { 
  WebSocketMessage, 
  WebSocketMessageType, 
  WebSocketMessageAction,
  MessageValidator 
} from '../models/websocket-message';

/// 认证Socket接口
interface AuthenticatedSocket extends Socket {
  userId?: string;
  userRole?: string;
  deviceId?: string;
  clientType: 'device' | 'control';
}

/// 设备信息接口
interface DeviceInfo {
  id: string;
  name: string;
  type: string;
  platform: string;
  version: string;
  ipAddress: string;
  port: number;
  capabilities: Record<string, any>;
  status: string;
  socket: AuthenticatedSocket;
  lastSeen: Date;
  metadata: Record<string, any>;
}

/// 控制端信息接口
interface ControlClientInfo {
  id: string;
  userId: string;
  userRole: string;
  socket: AuthenticatedSocket;
  connectedAt: Date;
  lastActivity: Date;
}

/// WebSocket服务类
export class WebSocketService {
  private static instance: WebSocketService;
  private io: SocketIOServer | null = null;
  private connectedDevices = new Map<string, DeviceInfo>();
  private connectedClients = new Map<string, ControlClientInfo>();
  private messageHandlers = new Map<string, Function>();
  private isInitialized = false;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  
  private constructor() {
    this.setupMessageHandlers();
  }
  
  /// 获取单例实例
  public static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }
  
  /// 初始化WebSocket服务
  public initialize(io: SocketIOServer): void {
    if (this.isInitialized) {
      console.log('WebSocket service already initialized');
      return;
    }
    
    console.log('Initializing WebSocket service...');
    console.log('Received io parameter:', typeof io, io.constructor.name);
    console.log('io.use method exists:', typeof io.use);
    
    this.io = io;
    
    // 设置认证中间件
    this.setupAuthentication();
    
    // 设置连接处理
    this.setupConnectionHandling();
    
    // 启动心跳检测
    this.startHeartbeat();
    
    this.isInitialized = true;
    console.log('✅ WebSocket service initialized successfully');
  }
  
  /// 设置认证中间件
  private setupAuthentication(): void {
    if (!this.io) return;
    
    console.log('🔧 Setting up authentication middleware...');
    
    this.io.use(async (socket: any, next) => {
      try {
        console.log('🔐 Authentication middleware triggered');
        console.log('   Handshake auth:', socket.handshake.auth);
        console.log('   Handshake query:', socket.handshake.query);
        console.log('   Handshake headers:', socket.handshake.headers);
        
        const token = socket.handshake.auth.token || 
                     socket.handshake.headers.authorization?.replace('Bearer ', '');
        const deviceId = socket.handshake.auth.deviceId || socket.handshake.query.deviceId;
        const clientType = socket.handshake.auth.clientType || 
                          socket.handshake.query.type || 
                          'control';
        
        console.log(`   Extracted - clientType: ${clientType}, deviceId: ${deviceId}, hasToken: ${!!token}`);
        
        if (clientType === 'device') {
          // 设备认证 - 简化验证
          if (!deviceId) {
            console.log('❌ Device authentication failed: missing deviceId');
            return next(new Error('Device ID required for device connections'));
          }
          socket.deviceId = deviceId;
          socket.clientType = 'device';
          console.log(`✅ Device authentication successful: ${deviceId}`);
        } else {
          // 控制端认证 - JWT验证
          if (!token) {
            // 开发环境允许无token连接
            if (process.env.NODE_ENV === 'development') {
              socket.userId = 'dev_user';
              socket.userRole = 'admin';
              socket.clientType = 'control';
              console.log('✅ Development mode: allowing control connection without token');
            } else {
              console.log('❌ Control authentication failed: missing token in production');
              return next(new Error('Authentication token required for control connections'));
            }
          } else {
            try {
              const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret') as any;
              socket.userId = decoded.userId;
              socket.userRole = decoded.role || 'user';
              socket.clientType = 'control';
              console.log(`Control client authentication successful: ${socket.userId}`);
            } catch (jwtError) {
              return next(new Error('Invalid authentication token'));
            }
          }
        }
        
        next();
      } catch (error) {
        console.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }
  
  /// 设置连接处理
  private setupConnectionHandling(): void {
    if (!this.io) return;
    
    console.log('🔧 Setting up connection handling...');
    
    this.io.on('connection', (socket: any) => {
      console.log('🔗 New Socket.IO connection established:', socket.id);
      console.log('   Client info:', socket.handshake.query);
      console.log('   Auth info:', socket.handshake.auth);
      console.log(`🔗 New ${socket.clientType || 'unknown'} connected: ${socket.id}`);
      console.log(`   Client info:`, {
        clientType: socket.clientType,
        userId: socket.userId,
        deviceId: socket.deviceId,
        handshake: {
          auth: socket.handshake.auth,
          query: socket.handshake.query
        }
      });
      
      if (socket.clientType === 'device') {
        this.handleDeviceConnection(socket);
      } else {
        this.handleControlConnection(socket);
      }
      
      // 通用事件处理
      this.setupCommonEventHandlers(socket);
    });
    
    // 添加连接错误处理
    this.io.on('connect_error', (error: any) => {
      console.error('❌ Socket.IO connection error:', error);
    });
    
    console.log('✅ Socket.IO connection handling configured');
  }
  
  /// 处理设备连接
  private handleDeviceConnection(socket: AuthenticatedSocket): void {
    const deviceId = socket.deviceId!;
    
    // 设备注册处理
    socket.on('message', async (data: string) => {
      try {
        const message = WebSocketMessage.fromJsonString(data);
        await this.handleDeviceMessage(socket, message);
      } catch (error) {
        console.error('Error handling device message:', error);
        this.sendErrorToSocket(socket, 'Invalid message format', error);
      }
    });
    
    // 兼容旧版事件处理
    socket.on('device:register', async (deviceInfo: any) => {
      const message = WebSocketMessage.deviceRegister(deviceInfo, deviceId);
      await this.handleDeviceMessage(socket, message);
    });
    
    socket.on('device:status', async (status: any) => {
      const message = WebSocketMessage.deviceStatusUpdate(deviceId, status.status, status.metadata, deviceId);
      await this.handleDeviceMessage(socket, message);
    });
  }
  
  /// 处理控制端连接
  private handleControlConnection(socket: AuthenticatedSocket): void {
    const clientId = socket.id;
    const userId = socket.userId!;
    
    // 注册控制端
    const clientInfo: ControlClientInfo = {
      id: clientId,
      userId: userId,
      userRole: socket.userRole || 'user',
      socket: socket,
      connectedAt: new Date(),
      lastActivity: new Date()
    };
    
    this.connectedClients.set(clientId, clientInfo);
    
    // 加入控制端房间
    socket.join('control_clients');
    
    // 发送当前设备列表
    this.sendDeviceListToClient(socket);
    
    // 消息处理
    socket.on('message', async (data: string) => {
      try {
        const message = WebSocketMessage.fromJsonString(data);
        await this.handleControlMessage(socket, message);
      } catch (error) {
        console.error('Error handling control message:', error);
        this.sendErrorToSocket(socket, 'Invalid message format', error);
      }
    });
    
    console.log(`Control client registered: ${userId} (${clientId})`);
  }
  
  /// 设置通用事件处理
  private setupCommonEventHandlers(socket: AuthenticatedSocket): void {
    socket.on('disconnect', () => {
      this.handleDisconnection(socket);
    });
    
    socket.on('error', (error: Error) => {
      console.error(`Socket error for ${socket.clientType} ${socket.id}:`, error);
    });
    
    socket.on('ping', () => {
      socket.emit('pong', {});
    });
  }
  
  /// 处理设备消息
  private async handleDeviceMessage(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const validation = MessageValidator.validate(message);
    if (!validation.valid) {
      this.sendErrorToSocket(socket, 'Invalid message', validation.errors.join(', '));
      return;
    }
    
    try {
      switch (message.type) {
        case WebSocketMessageType.DEVICE:
          await this.handleDeviceTypeMessage(socket, message);
          break;
        case WebSocketMessageType.SCREENSHOT:
          await this.handleScreenshotMessage(socket, message);
          break;
        case WebSocketMessageType.DISPLAY:
          await this.handleDisplayMessage(socket, message);
          break;
        case WebSocketMessageType.HEARTBEAT:
          await this.handleHeartbeatMessage(socket, message);
          break;
        default:
          console.warn(`Unknown message type from device: ${message.type}`);
      }
    } catch (error) {
      console.error('Error processing device message:', error);
      this.sendErrorToSocket(socket, 'Message processing failed', error);
    }
  }
  
  /// 处理控制端消息
  private async handleControlMessage(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const validation = MessageValidator.validate(message);
    if (!validation.valid) {
      this.sendErrorToSocket(socket, 'Invalid message', validation.errors.join(', '));
      return;
    }
    
    // 更新客户端活动时间
    const clientInfo = this.connectedClients.get(socket.id);
    if (clientInfo) {
      clientInfo.lastActivity = new Date();
    }
    
    try {
      switch (message.type) {
        case WebSocketMessageType.DEVICE:
          await this.handleDeviceTypeMessage(socket, message);
          break;
        case WebSocketMessageType.SCREENSHOT:
          await this.forwardScreenshotRequest(socket, message);
          break;
        case WebSocketMessageType.DISPLAY:
          await this.forwardDisplayRequest(socket, message);
          break;
        case WebSocketMessageType.DISCOVERY:
          await this.handleDiscoveryMessage(socket, message);
          break;
        case WebSocketMessageType.HEARTBEAT:
          await this.handleHeartbeatMessage(socket, message);
          break;
        default:
          console.warn(`Unknown message type from control: ${message.type}`);
      }
    } catch (error) {
      console.error('Error processing control message:', error);
      this.sendErrorToSocket(socket, 'Message processing failed', error);
    }
  }
  
  /// 处理设备类型消息
  private async handleDeviceTypeMessage(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    switch (message.action) {
      case WebSocketMessageAction.DEVICE_REGISTER:
        await this.registerDevice(socket, message);
        break;
      case WebSocketMessageAction.DEVICE_STATUS:
        await this.updateDeviceStatus(socket, message);
        break;
      case WebSocketMessageAction.DEVICE_UNREGISTER:
        await this.handleDeviceUnregister(socket, message);
        break;
      default:
        console.warn(`Unknown device action: ${message.action}`);
    }
  }
  
  /// 注册设备
  private async registerDevice(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const deviceInfo = message.payload!;
    const deviceId = socket.deviceId!;
    
    // 验证设备注册信息
    const validation = MessageValidator.validateDeviceRegister(deviceInfo);
    if (!validation.valid) {
      this.sendErrorToSocket(socket, 'Invalid device registration', validation.errors.join(', '));
      return;
    }
    
    try {
      // 创建设备信息
      const device: DeviceInfo = {
        id: deviceId,
        name: deviceInfo.name || `Device ${deviceId}`,
        type: deviceInfo.type || 'terminal',
        platform: deviceInfo.platform || 'unknown',
        version: deviceInfo.version || '1.0.0',
        ipAddress: (socket as any).handshake?.address || 'unknown',
        port: deviceInfo.port || 0,
        capabilities: deviceInfo.capabilities || {},
        status: 'online',
        socket: socket,
        lastSeen: new Date(),
        metadata: deviceInfo.metadata || {}
      };
      
      // 存储设备信息
      this.connectedDevices.set(deviceId, device);
      
      // 加入设备房间
      socket.join(`device:${deviceId}`);
      
      // 更新数据库
      await this.updateDeviceInDatabase(device);
      
      // 通知控制端
      this.broadcastToClients('device:registered', {
        device: this.serializeDeviceInfo(device)
      });
      
      // 响应设备
      const response = WebSocketMessage.deviceRegister({ success: true, deviceId }, 'gateway');
      this.sendMessageToSocket(socket, response);
      
      console.log(`✅ Device registered: ${deviceId} (${device.name})`);
    } catch (error) {
      console.error('Device registration error:', error);
      this.sendErrorToSocket(socket, 'Registration failed', error);
    }
  }
  
  /// 更新设备状态
  private async updateDeviceStatus(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const deviceId = socket.deviceId!;
    const device = this.connectedDevices.get(deviceId);
    
    if (!device) {
      this.sendErrorToSocket(socket, 'Device not registered');
      return;
    }
    
    const { status, metadata } = message.payload!;
    
    // 更新设备信息
    device.status = status || device.status;
    device.lastSeen = new Date();
    if (metadata) {
      device.metadata = { ...device.metadata, ...metadata };
    }
    
    // 通知控制端
    this.broadcastToClients('device:status_updated', {
      deviceId,
      status: device.status,
      metadata: device.metadata,
      lastSeen: device.lastSeen.toISOString()
    });
    
    console.log(`Device status updated: ${deviceId} -> ${device.status}`);
  }
  
  /// 处理设备注销
  private async handleDeviceUnregister(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const deviceId = socket.deviceId!;
    const device = this.connectedDevices.get(deviceId);
    
    if (!device) {
      this.sendErrorToSocket(socket, 'Device not registered');
      return;
    }
    
    // 更新设备状态为离线
    device.status = 'offline';
    device.lastSeen = new Date();
    
    // 通知控制端
    this.broadcastToClients('device:unregistered', {
      deviceId,
      lastSeen: device.lastSeen.toISOString()
    });
    
    // 从连接列表中移除
    this.connectedDevices.delete(deviceId);
    
    // 响应设备
    const response = WebSocketMessage.deviceRegister({ success: true, deviceId }, 'gateway');
    this.sendMessageToSocket(socket, response);
    
    console.log(`Device unregistered: ${deviceId}`);
  }
  
  /// 转发截图请求
  private async forwardScreenshotRequest(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const { deviceId, deviceIds } = message.payload!;
    
    if (deviceId) {
      // 单设备截图
      const device = this.connectedDevices.get(deviceId);
      if (device) {
        this.sendMessageToSocket(device.socket, message);
      } else {
        this.sendErrorToSocket(socket, `Device not found: ${deviceId}`);
      }
    } else if (deviceIds && Array.isArray(deviceIds)) {
      // 批量截图
      for (const id of deviceIds) {
        const device = this.connectedDevices.get(id);
        if (device) {
          const deviceMessage = new WebSocketMessage({
            type: message.type,
            action: message.action,
            payload: { ...message.payload, deviceId: id },
            senderId: message.senderId
          });
          this.sendMessageToSocket(device.socket, deviceMessage);
        }
      }
    }
  }
  
  /// 转发显示请求
  private async forwardDisplayRequest(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const { deviceId, deviceIds } = message.payload!;
    
    if (deviceId) {
      // 单设备显示
      const device = this.connectedDevices.get(deviceId);
      if (device) {
        this.sendMessageToSocket(device.socket, message);
      } else {
        this.sendErrorToSocket(socket, `Device not found: ${deviceId}`);
      }
    } else if (deviceIds && Array.isArray(deviceIds)) {
      // 批量显示
      for (const id of deviceIds) {
        const device = this.connectedDevices.get(id);
        if (device) {
          const deviceMessage = new WebSocketMessage({
            type: message.type,
            action: message.action,
            payload: { ...message.payload, deviceId: id },
            senderId: message.senderId
          });
          this.sendMessageToSocket(device.socket, deviceMessage);
        }
      }
    }
  }
  
  /// 处理截图消息
  private async handleScreenshotMessage(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    // 转发截图结果给控制端
    this.broadcastToClients('screenshot:result', message.payload);
  }
  
  /// 处理显示消息
  private async handleDisplayMessage(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    // 转发显示状态给控制端
    this.broadcastToClients('display:status', message.payload);
  }
  
  /// 处理心跳消息
  private async handleHeartbeatMessage(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    const response = WebSocketMessage.heartbeatPong(message.senderId);
    this.sendMessageToSocket(socket, response);
  }
  
  /// 处理发现消息
  private async handleDiscoveryMessage(socket: AuthenticatedSocket, message: WebSocketMessage): Promise<void> {
    if (message.action === WebSocketMessageAction.DISCOVERY_REQUEST) {
      // 返回当前连接的设备列表
      const devices = Array.from(this.connectedDevices.values()).map(device => 
        this.serializeDeviceInfo(device)
      );
      
      const response = new WebSocketMessage({
        type: WebSocketMessageType.DISCOVERY,
        action: WebSocketMessageAction.DISCOVERY_RESPONSE,
        payload: { devices },
        senderId: 'gateway'
      });
      
      this.sendMessageToSocket(socket, response);
    }
  }
  
  /// 处理断开连接
  private handleDisconnection(socket: AuthenticatedSocket): void {
    console.log(`${socket.clientType} disconnected: ${socket.id}`);
    
    if (socket.clientType === 'device' && socket.deviceId) {
      const deviceId = socket.deviceId;
      const device = this.connectedDevices.get(deviceId);
      
      if (device) {
        // 更新设备状态为离线
        device.status = 'offline';
        device.lastSeen = new Date();
        
        // 通知控制端
        this.broadcastToClients('device:disconnected', {
          deviceId,
          lastSeen: device.lastSeen.toISOString()
        });
        
        // 从连接列表中移除
        this.connectedDevices.delete(deviceId);
        
        console.log(`Device disconnected: ${deviceId}`);
      }
    } else if (socket.clientType === 'control') {
      // 移除控制端
      this.connectedClients.delete(socket.id);
      console.log(`Control client disconnected: ${socket.userId}`);
    }
  }
  
  /// 发送设备列表给控制端
  private sendDeviceListToClient(socket: AuthenticatedSocket): void {
    const devices = Array.from(this.connectedDevices.values()).map(device => 
      this.serializeDeviceInfo(device)
    );
    
    const message = new WebSocketMessage({
      type: WebSocketMessageType.DEVICE,
      action: WebSocketMessageAction.DEVICE_LIST,
      payload: { devices },
      senderId: 'gateway'
    });
    
    this.sendMessageToSocket(socket, message);
  }
  
  /// 广播消息给所有控制端
  public broadcastToClients(event: string, data: any): void {
    if (this.io) {
      this.io.to('control_clients').emit(event, data);
    }
  }
  
  /// 发送消息给指定Socket
  private sendMessageToSocket(socket: AuthenticatedSocket, message: WebSocketMessage): void {
    socket.emit('message', message.toJsonString());
  }
  
  /// 发送错误消息给Socket
  private sendErrorToSocket(socket: AuthenticatedSocket, error: string, details?: any): void {
    const errorMessage = WebSocketMessage.error(error, details?.toString(), undefined, 'gateway');
    this.sendMessageToSocket(socket, errorMessage);
  }
  
  /// 序列化设备信息
  private serializeDeviceInfo(device: DeviceInfo): Record<string, any> {
    return {
      id: device.id,
      name: device.name,
      type: device.type,
      platform: device.platform,
      version: device.version,
      ipAddress: device.ipAddress,
      port: device.port,
      capabilities: device.capabilities,
      status: device.status,
      lastSeen: device.lastSeen.toISOString(),
      metadata: device.metadata
    };
  }
  
  /// 更新设备到数据库
  private async updateDeviceInDatabase(device: DeviceInfo): Promise<void> {
    try {
      await databaseService.query(
        `INSERT OR REPLACE INTO devices 
         (id, name, type, platform, version, ip_address, port, capabilities, status, last_seen, metadata) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          device.id,
          device.name,
          device.type,
          device.platform,
          device.version,
          device.ipAddress,
          device.port,
          JSON.stringify(device.capabilities),
          device.status,
          device.lastSeen.toISOString(),
          JSON.stringify(device.metadata)
        ]
      );
    } catch (error) {
      console.error('Failed to update device in database:', error);
    }
  }
  
  /// 启动心跳检测
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      this.checkDeviceHeartbeats();
      this.cleanupStaleConnections();
    }, 30000); // 每30秒检查一次
  }
  
  /// 检查设备心跳
  private checkDeviceHeartbeats(): void {
    const now = new Date();
    const timeoutThreshold = 2 * 60 * 1000; // 2分钟超时
    
    for (const [deviceId, device] of this.connectedDevices) {
      const timeSinceLastSeen = now.getTime() - device.lastSeen.getTime();
      
      if (timeSinceLastSeen > timeoutThreshold) {
        console.log(`Device timeout detected: ${deviceId}`);
        
        // 标记为离线
        device.status = 'offline';
        
        // 通知控制端
        this.broadcastToClients('device:timeout', {
          deviceId,
          lastSeen: device.lastSeen.toISOString()
        });
        
        // 断开连接
        device.socket.disconnect();
      }
    }
  }
  
  /// 清理过期连接
  private cleanupStaleConnections(): void {
    const now = new Date();
    const staleThreshold = 10 * 60 * 1000; // 10分钟
    
    // 清理过期的控制端连接
    for (const [clientId, client] of this.connectedClients) {
      const timeSinceLastActivity = now.getTime() - client.lastActivity.getTime();
      
      if (timeSinceLastActivity > staleThreshold) {
        console.log(`Cleaning up stale control client: ${clientId}`);
        this.connectedClients.delete(clientId);
        client.socket.disconnect();
      }
    }
  }
  
  /// 设置消息处理器
  private setupMessageHandlers(): void {
    // 可以在这里添加自定义消息处理器
  }
  
  /// 获取连接的设备数量
  public static getConnectedDevicesCount(): number {
    return WebSocketService.getInstance().connectedDevices.size;
  }

  /// 获取连接的客户端数量
  public static getConnectedClientsCount(): number {
    return WebSocketService.getInstance().connectedClients.size;
  }

  /// 获取连接统计
  public getConnectionStats(): Record<string, any> {
    return {
      connectedDevices: this.connectedDevices.size,
      connectedClients: this.connectedClients.size,
      devices: Array.from(this.connectedDevices.values()).map(device => ({
        id: device.id,
        name: device.name,
        status: device.status,
        lastSeen: device.lastSeen.toISOString()
      })),
      clients: Array.from(this.connectedClients.values()).map(client => ({
        id: client.id,
        userId: client.userId,
        connectedAt: client.connectedAt.toISOString(),
        lastActivity: client.lastActivity.toISOString()
      }))
    };
  }
  
  /// 关闭服务
  public shutdown(): void {
    console.log('Shutting down WebSocket service...');
    
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
    }
    
    if (this.io) {
      this.io.close();
    }
    
    this.connectedDevices.clear();
    this.connectedClients.clear();
    this.isInitialized = false;
    
    console.log('✅ WebSocket service shut down');
  }
}

/// 导出单例实例
export default WebSocketService.getInstance();