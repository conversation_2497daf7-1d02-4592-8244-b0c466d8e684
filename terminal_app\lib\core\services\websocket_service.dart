import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/io.dart';
import 'package:flutter/foundation.dart';
import '../config/app_config.dart' show AppConstants;
import '../models/device_model.dart'
    show Device, DeviceCapabilities, DeviceType, DeviceStatus;
import 'logger_service.dart';
import 'device_service.dart';

/// WebSocket连接状态
enum WebSocketConnectionState {
  disconnected,
  connecting,
  connected,
  reconnecting,
  error,
}

/// WebSocket服务
class WebSocketService {
  static WebSocketService? _instance;
  static WebSocketService get instance => _instance ??= WebSocketService._();

  WebSocketService._();

  WebSocketChannel? _channel;
  StreamSubscription? _subscription;
  Timer? _heartbeatTimer;
  Timer? _reconnectTimer;

  WebSocketConnectionState _status = WebSocketConnectionState.disconnected;
  String? _gatewayUrl;
  int _reconnectAttempts = 0;
  bool _isInitialized = false;

  final StreamController<Map<String, dynamic>> _messageController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<WebSocketConnectionState> _statusController =
      StreamController<WebSocketConnectionState>.broadcast();

  /// 消息流
  Stream<Map<String, dynamic>> get messageStream => _messageController.stream;

  /// 状态流
  Stream<WebSocketConnectionState> get statusStream => _statusController.stream;

  /// 当前状态
  WebSocketConnectionState get status => _status;

  /// 是否已连接
  bool get isConnected => _status == WebSocketConnectionState.connected;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化WebSocket服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggerService.info('初始化WebSocket服务...');
      _isInitialized = true;
      LoggerService.info('WebSocket服务初始化完成');
    } catch (e, stackTrace) {
      LoggerService.error('WebSocket服务初始化失败: $e', stackTrace);
      rethrow;
    }
  }

  /// 连接到网关
  Future<bool> connect(String gatewayUrl) async {
    if (_status == WebSocketConnectionState.connecting ||
        _status == WebSocketConnectionState.connected) {
      return _status == WebSocketConnectionState.connected;
    }

    _gatewayUrl = gatewayUrl;
    _updateStatus(WebSocketConnectionState.connecting);

    try {
      LoggerService.info('连接到网关: $gatewayUrl');

      // 构建WebSocket URL - 统一网关架构 v1.0.0
      // 使用终端设备专用路径: /terminal/ws
      // 注意: 控制端使用不同路径: /controller/ws
      String wsUrl;
      if (gatewayUrl.startsWith('ws://') || gatewayUrl.startsWith('wss://')) {
        // 如果已经是WebSocket URL，直接使用
        wsUrl = gatewayUrl;
      } else {
        // 如果是HTTP URL，转换为WebSocket URL，使用统一路径
        // 统一网关架构: 同一端口，使用/ws路径
        wsUrl = '${gatewayUrl.replaceFirst('http', 'ws')}/ws';
      }
      LoggerService.info('构建的WebSocket URL: $wsUrl');
      debugPrint('WebSocket连接URL: $wsUrl');

      // 先测试网关是否可达
      try {
        LoggerService.info('🔍 测试网关连接性...');
        debugPrint('🔍 测试网关连接性...');

        final httpClient = HttpClient();
        final request =
            await httpClient.getUrl(Uri.parse('$gatewayUrl/health'));
        request.headers.set('User-Agent', AppConstants.userAgent);
        final response = await request.close();
        final responseBody = await response.transform(utf8.decoder).join();

        LoggerService.info('✅ 网关健康检查成功: ${response.statusCode}');
        LoggerService.info('📋 网关响应: $responseBody');
        debugPrint('✅ 网关健康检查成功: ${response.statusCode}');
        debugPrint('📋 网关响应: $responseBody');

        httpClient.close();
      } catch (healthError) {
        LoggerService.error('❌ 网关健康检查失败', healthError);
        debugPrint('❌ 网关健康检查失败: $healthError');
        throw Exception('网关服务不可达: $healthError');
      }

      // 创建WebSocket连接
      LoggerService.info('🔌 创建WebSocket连接...');
      debugPrint('🔌 创建WebSocket连接...');

      _channel = IOWebSocketChannel.connect(
        Uri.parse(wsUrl),
        headers: {
          'User-Agent': AppConstants.userAgent,
          'X-Device-Type': AppConstants.deviceType,
          'X-Device-Platform': AppConstants.platformName,
        },
      );

      LoggerService.info('📡 WebSocket通道已创建，等待连接确认...');
      debugPrint('📡 WebSocket通道已创建，等待连接确认...');

      // 监听消息
      _subscription = _channel!.stream.listen(
        _handleMessage,
        onError: _handleError,
        onDone: _handleDisconnection,
      );

      _updateStatus(WebSocketConnectionState.connected);
      _reconnectAttempts = 0;

      LoggerService.info('WebSocket连接成功');
      debugPrint('🎉 WebSocket连接成功，状态已更新为connected');

      // 等待一小段时间确保连接稳定，然后发送设备注册消息
      await Future.delayed(const Duration(milliseconds: 500));
      await _sendDeviceRegistration();

      // 启动心跳
      _startHeartbeat();

      // 确保状态更新事件被发送
      _notifyConnectionSuccess();

      return true;
    } catch (e) {
      LoggerService.error('WebSocket连接失败: $e');
      _updateStatus(WebSocketConnectionState.error);
      _scheduleReconnect();
      return false;
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    LoggerService.info('断开WebSocket连接');

    _stopHeartbeat();
    _stopReconnect();

    await _subscription?.cancel();
    await _channel?.sink.close();

    _subscription = null;
    _channel = null;

    _updateStatus(WebSocketConnectionState.disconnected);
  }

  /// 发送消息
  Future<bool> sendMessage(Map<String, dynamic> message) async {
    if (!isConnected || _channel == null) {
      LoggerService.warning('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      final jsonMessage = json.encode(message);
      _channel!.sink.add(jsonMessage);
      LoggerService.debug('发送WebSocket消息: ${message['type']}');
      return true;
    } catch (e) {
      LoggerService.error('发送WebSocket消息失败: $e');
      return false;
    }
  }

  /// 发送设备注册消息
  Future<void> _sendDeviceRegistration() async {
    final deviceInfo = DeviceService.instance.deviceInfo;
    LoggerService.info('准备发送设备注册消息，设备信息: ${deviceInfo?.id}');

    if (deviceInfo == null) {
      LoggerService.error('设备信息为空，无法注册');
      return;
    }

    final registrationMessage = {
      'type': 'device_register',
      'data': deviceInfo.toJson(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    LoggerService.info('发送设备注册消息: ${deviceInfo.id}');
    final success = await sendMessage(registrationMessage);
    LoggerService.info('设备注册消息发送结果: $success');
  }

  /// 更新设备信息
  Future<void> updateDeviceInfo(Device device) async {
    final updateMessage = {
      'type': 'device_update',
      'data': device.toJson(),
      'timestamp': DateTime.now().toIso8601String(),
    };

    await sendMessage(updateMessage);
    LoggerService.info('发送设备信息更新');
  }

  /// 发送设备能力信息
  Future<bool> sendDeviceCapabilities(Map<String, dynamic> capabilities) async {
    final message = {
      'type': 'device_capabilities',
      'data': {
        'deviceId': DeviceService.instance.deviceInfo?.id,
        'capabilities': capabilities,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    return await sendMessage(message);
  }

  /// 发送设备状态信息
  Future<bool> sendDeviceStatus(Map<String, dynamic> status) async {
    final message = {
      'type': 'device_status',
      'data': {
        'deviceId': DeviceService.instance.deviceInfo?.id,
        'status': status,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    return await sendMessage(message);
  }

  /// 发送设备配置信息
  Future<bool> sendDeviceConfig(Map<String, dynamic> config) async {
    final message = {
      'type': 'device_config',
      'data': {
        'deviceId': DeviceService.instance.deviceInfo?.id,
        'config': config,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    return await sendMessage(message);
  }

  /// 处理接收到的消息
  void _handleMessage(dynamic data) {
    try {
      LoggerService.info('📥 收到原始WebSocket数据: $data');
      debugPrint('📥 收到原始WebSocket数据: $data');

      final message = json.decode(data.toString()) as Map<String, dynamic>;
      LoggerService.info('📋 解析后的消息: $message');
      LoggerService.info('📋 消息类型: ${message['type']}');
      debugPrint('📋 解析后的消息: $message');
      debugPrint('📋 消息类型: ${message['type']}');

      // 处理特殊消息类型
      switch (message['type']) {
        case 'ping':
          _handlePing(message);
          break;
        case 'device_registered':
          _handleDeviceRegistered(message);
          break;
        case 'error':
          _handleServerError(message);
          break;
        default:
          // 转发给监听器
          _messageController.add(message);
      }
    } catch (e) {
      LoggerService.error('处理WebSocket消息失败: $e');
    }
  }

  /// 处理Ping消息
  void _handlePing(Map<String, dynamic> message) {
    final pongMessage = {
      'type': 'pong',
      'data': message['data'],
      'timestamp': DateTime.now().toIso8601String(),
    };
    sendMessage(pongMessage);
  }

  /// 处理设备注册成功
  void _handleDeviceRegistered(Map<String, dynamic> message) {
    LoggerService.info('✅ 设备注册成功');
    debugPrint('✅ 设备注册成功');

    // 更新设备服务状态
    DeviceService.instance.updateStatus(DeviceStatus.online);

    // 通知设备状态更新 - 这里需要通过回调或事件通知UI更新连接状态
    _notifyConnectionSuccess();
  }

  /// 通知连接成功
  void _notifyConnectionSuccess() {
    // 发送连接成功事件
    if (_status == WebSocketConnectionState.connected) {
      _statusController.add(WebSocketConnectionState.connected);
      LoggerService.info('🎉 连接状态已更新为已连接');
      debugPrint('🎉 连接状态已更新为已连接');
    }
  }

  /// 处理服务器错误
  void _handleServerError(Map<String, dynamic> message) {
    final error = message['data']?['message'] ?? '未知错误';
    LoggerService.error('服务器错误: $error');
  }

  /// 处理连接错误
  void _handleError(dynamic error) {
    LoggerService.error('💥 WebSocket连接错误: $error');
    debugPrint('💥 WebSocket连接错误: $error');
    debugPrint('💥 错误类型: ${error.runtimeType}');

    if (error is WebSocketException) {
      debugPrint('💥 WebSocket异常详情: ${error.message}');
      LoggerService.error('WebSocket异常详情: ${error.message}');
    } else if (error is SocketException) {
      debugPrint('💥 Socket异常详情: ${error.message}');
      LoggerService.error('Socket异常详情: ${error.message}');
    }

    _updateStatus(WebSocketConnectionState.error);

    LoggerService.info('🔄 准备重连...');
    debugPrint('🔄 准备重连...');
    _scheduleReconnect();
  }

  /// 处理连接断开
  void _handleDisconnection() {
    LoggerService.warning('WebSocket连接断开');
    _updateStatus(WebSocketConnectionState.disconnected);
    _scheduleReconnect();
  }

  /// 启动心跳
  void _startHeartbeat() {
    _stopHeartbeat();

    _heartbeatTimer = Timer.periodic(
      AppConstants.heartbeatInterval,
      (_) => _sendHeartbeat(),
    );
  }

  /// 停止心跳
  void _stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
  }

  /// 发送心跳
  void _sendHeartbeat() {
    final heartbeatMessage = {
      'type': 'heartbeat',
      'data': {
        'device_id': DeviceService.instance.deviceInfo?.id,
        'timestamp': DateTime.now().toIso8601String(),
      },
    };
    sendMessage(heartbeatMessage);
  }

  /// 安排重连
  void _scheduleReconnect() {
    if (_reconnectAttempts >= AppConstants.maxReconnectAttempts) {
      LoggerService.error('达到最大重连次数，停止重连');
      return;
    }

    _stopReconnect();

    final delay = Duration(
      seconds: AppConstants.reconnectDelay.inSeconds * (_reconnectAttempts + 1),
    );

    LoggerService.info(
        '${delay.inSeconds}秒后尝试重连 (${_reconnectAttempts + 1}/${AppConstants.maxReconnectAttempts})');

    _reconnectTimer = Timer(delay, () {
      _reconnectAttempts++;
      _updateStatus(WebSocketConnectionState.reconnecting);

      if (_gatewayUrl != null) {
        connect(_gatewayUrl!);
      }
    });
  }

  /// 停止重连
  void _stopReconnect() {
    _reconnectTimer?.cancel();
    _reconnectTimer = null;
  }

  /// 更新状态
  void _updateStatus(WebSocketConnectionState status) {
    if (_status != status) {
      _status = status;
      _statusController.add(status);
      LoggerService.info('WebSocket状态更新: $status');
    }
  }

  /// 重置重连计数
  void resetReconnectAttempts() {
    _reconnectAttempts = 0;
  }

  /// 获取连接统计信息
  Map<String, dynamic> getConnectionStats() {
    return {
      'status': _status.toString(),
      'gateway_url': _gatewayUrl,
      'reconnect_attempts': _reconnectAttempts,
      'is_connected': isConnected,
      'is_initialized': _isInitialized,
    };
  }

  /// 释放资源
  Future<void> dispose() async {
    await disconnect();
    await _messageController.close();
    await _statusController.close();
    _isInitialized = false;
    LoggerService.info('WebSocket服务已释放');
  }
}
