# Flutter图片切换系统 - 技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[Flutter控制端应用] --> B[WebSocket客户端]
    C[Flutter终端应用] --> D[WebSocket客户端]
    B --> E[Node.js网关服务]
    D --> E
    E --> F[设备发现服务]
    E --> G[消息路由服务]
    E --> H[文件传输服务]
    
    I[截图服务] --> C
    J[显示控制服务] --> C
    K[系统监控服务] --> C
    
    L[图片存储] --> E
    M[配置数据库] --> E
    N[日志系统] --> E
    
    subgraph "Flutter客户端层"
        A
        C
    end
    
    subgraph "网关服务层"
        E
        F
        G
        H
    end
    
    subgraph "终端服务层"
        I
        J
        K
    end
    
    subgraph "数据存储层"
        L
        M
        N
    end
```

## 2. Technology Description

* **控制端**: Flutter\@3.16 + Riverpod\@2.4 + dio\@5.3 + web\_socket\_channel\@2.4

* **终端端**: Flutter\@3.16 + screen\_capturer\@0.1 + window\_manager\@0.3 + system\_tray\@2.0

* **网关服务**: Node.js\@18 + Express\@4.18 + Socket.io\@4.7 + multer\@1.4

* **数据存储**: SQLite (本地配置) + 文件系统 (图片存储)

* **通信协议**: WebSocket (实时通信) + HTTP (文件传输)

## 3. Route definitions

### 控制端路由

| Route      | Purpose           |
| ---------- | ----------------- |
| /dashboard | 主控制台，显示设备状态和快速操作  |
| /devices   | 设备管理页面，设备发现、配置和分组 |
| /capture   | 截图控制页面，批量截图和定时任务  |
| /gallery   | 图片管理页面，浏览和管理截图库   |
| /display   | 同步显示页面，多屏显示控制     |
| /settings  | 系统设置页面，网络和性能配置    |
| /login     | 登录页面，用户身份验证       |

### 终端端路由

| Route   | Purpose          |
| ------- | ---------------- |
| /status | 状态页面，显示设备信息和连接状态 |
| /config | 配置页面，本地参数设置      |
| /logs   | 日志页面，查看运行日志和错误信息 |

## 4. API definitions

### 4.1 WebSocket 消息协议

**设备发现消息**

```typescript
interface DeviceDiscoveryMessage {
  type: 'device_discovery';
  action: 'announce' | 'request' | 'response';
  deviceInfo: {
    id: string;
    name: string;
    ip: string;
    platform: string;
    version: string;
    capabilities: string[];
  };
}
```

**截图控制消息**

```typescript
interface CaptureControlMessage {
  type: 'capture_control';
  action: 'start' | 'stop' | 'status';
  targetDevices: string[];
  options: {
    quality: number;
    region?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
    format: 'png' | 'jpg';
  };
}
```

**显示控制消息**

```typescript
interface DisplayControlMessage {
  type: 'display_control';
  action: 'show' | 'hide' | 'update';
  targetDevices: string[];
  content: {
    imageUrl: string;
    displayMode: 'fullscreen' | 'fit' | 'stretch';
    duration?: number;
  };
}
```

### 4.2 HTTP API

**图片上传**

```
POST /api/images/upload
```

Request (multipart/form-data):

| Param Name | Param Type | isRequired | Description |
| ---------- | ---------- | ---------- | ----------- |
| file       | File       | true       | 图片文件        |
| deviceId   | string     | true       | 设备ID        |
| timestamp  | number     | true       | 截图时间戳       |
| metadata   | string     | false      | 图片元数据JSON   |

Response:

```json
{
  "success": true,
  "imageId": "img_123456",
  "url": "/images/img_123456.png",
  "thumbnail": "/thumbnails/img_123456_thumb.png"
}
```

**设备配置**

```
PUT /api/devices/{deviceId}/config
```

Request:

```json
{
  "captureQuality": 90,
  "autoCapture": true,
  "captureInterval": 30,
  "displaySettings": {
    "brightness": 100,
    "contrast": 100
  }
}
```

Response:

```json
{
  "success": true,
  "message": "配置更新成功"
}
```

## 5. Server architecture diagram

```mermaid
graph TD
    A[HTTP请求层] --> B[路由控制层]
    B --> C[业务逻辑层]
    C --> D[数据访问层]
    D --> E[(SQLite数据库)]
    
    F[WebSocket连接层] --> G[消息处理层]
    G --> H[设备管理服务]
    G --> I[截图控制服务]
    G --> J[显示控制服务]
    
    H --> K[设备注册表]
    I --> L[任务队列]
    J --> M[显示状态管理]
    
    subgraph "网关服务器"
        B
        C
        D
        G
        H
        I
        J
    end
    
    subgraph "数据层"
        E
        K
        L
        M
    end
```

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    DEVICE ||--o{ CAPTURE_TASK : executes
    DEVICE ||--o{ DISPLAY_TASK : displays
    CAPTURE_TASK ||--|| IMAGE : produces
    IMAGE ||--o{ DISPLAY_TASK : uses
    DEVICE_GROUP ||--o{ DEVICE : contains
    USER ||--o{ CAPTURE_TASK : creates
    USER ||--o{ DISPLAY_TASK : creates
    
    DEVICE {
        string id PK
        string name
        string ip_address
        string platform
        string version
        json capabilities
        string status
        datetime last_seen
        json config
        string group_id FK
    }
    
    DEVICE_GROUP {
        string id PK
        string name
        string description
        json default_config
        datetime created_at
    }
    
    CAPTURE_TASK {
        string id PK
        string device_id FK
        string user_id FK
        string status
        json options
        datetime created_at
        datetime completed_at
        string error_message
    }
    
    DISPLAY_TASK {
        string id PK
        string device_id FK
        string image_id FK
        string user_id FK
        string status
        json display_options
        datetime created_at
        datetime started_at
        datetime ended_at
    }
    
    IMAGE {
        string id PK
        string filename
        string original_name
        string device_id FK
        string task_id FK
        integer file_size
        string format
        json metadata
        datetime created_at
        string thumbnail_path
    }
    
    USER {
        string id PK
        string username
        string password_hash
        string role
        json permissions
        datetime created_at
        datetime last_login
    }
```

### 6.2 Data Definition Language

**设备表 (devices)**

```sql
-- 创建设备表
CREATE TABLE devices (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    ip_address TEXT NOT NULL,
    platform TEXT NOT NULL,
    version TEXT NOT NULL,
    capabilities TEXT, -- JSON格式
    status TEXT DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'busy')),
    last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
    config TEXT, -- JSON格式
    group_id TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (group_id) REFERENCES device_groups(id)
);

-- 创建索引
CREATE INDEX idx_devices_status ON devices(status);
CREATE INDEX idx_devices_group ON devices(group_id);
CREATE INDEX idx_devices_last_seen ON devices(last_seen DESC);
```

**设备组表 (device\_groups)**

```sql
CREATE TABLE device_groups (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    default_config TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**截图任务表 (capture\_tasks)**

```sql
CREATE TABLE capture_tasks (
    id TEXT PRIMARY KEY,
    device_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    options TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    error_message TEXT,
    FOREIGN KEY (device_id) REFERENCES devices(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE INDEX idx_capture_tasks_device ON capture_tasks(device_id);
CREATE INDEX idx_capture_tasks_status ON capture_tasks(status);
CREATE INDEX idx_capture_tasks_created ON capture_tasks(created_at DESC);
```

**图片表 (images)**

```sql
CREATE TABLE images (
    id TEXT PRIMARY KEY,
    filename TEXT NOT NULL,
    original_name TEXT,
    device_id TEXT NOT NULL,
    task_id TEXT,
    file_size INTEGER,
    format TEXT,
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    thumbnail_path TEXT,
    FOREIGN KEY (device_id) REFERENCES devices(id),
    FOREIGN KEY (task_id) REFERENCES capture_tasks(id)
);

CREATE INDEX idx_images_device ON images(device_id);
CREATE INDEX idx_images_created ON images(created_at DESC);
```

**显示任务表 (display\_tasks)**

```sql
CREATE TABLE display_tasks (
    id TEXT PRIMARY KEY,
    device_id TEXT NOT NULL,
    image_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed', 'cancelled')),
    display_options TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    ended_at DATETIME,
    FOREIGN KEY (device_id) REFERENCES devices(id),
    FOREIGN KEY (image_id) REFERENCES images(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE INDEX idx_display_tasks_device ON display_tasks(device_id);
CREATE INDEX idx_display_tasks_status ON display_tasks(status);
```

**用户表 (users)**

```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    role TEXT DEFAULT 'operator' CHECK (role IN ('admin', 'operator', 'viewer')),
    permissions TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME
);

-- 初始化数据
INSERT INTO users (id, username, password_hash, role) VALUES 
('admin_001', 'admin', '$2b$10$hash_here', 'admin');

INSERT INTO device_groups (id, name, description) VALUES 
('group_default', '默认分组', '系统默认设备分组'),
('group_meeting', '会议室', '会议室显示设备'),
('group_monitor', '监控中心', '监控中心显示设备');
```

