import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:window_manager/window_manager.dart';

import 'theme/app_theme.dart';
import 'routes/app_routes.dart';
import 'providers/device_provider.dart';
import 'providers/app_state_provider.dart' as app_state;
import 'providers/display_provider.dart';

class FlutterImgctTerminalApp extends ConsumerWidget {
  const FlutterImgctTerminalApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appState = ref.watch(app_state.appStateProvider);
    final deviceState = ref.watch(deviceProvider);
    final displayState = ref.watch(displayProvider);

    return MaterialApp.router(
      title: 'Flutter图片切换终端系统',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: appState.isDarkMode ? ThemeMode.dark : ThemeMode.light,
      routerConfig: AppRoutes.router,
      builder: (context, child) {
        return _WindowWrapper(child: child!);
      },
    );
  }
}

class _WindowWrapper extends ConsumerWidget {
  final Widget child;

  const _WindowWrapper({required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final displayState = ref.watch(displayProvider);

    // Safely get current route, fallback to empty string if not available
    String currentRoute = '';
    try {
      final routerState = GoRouterState.of(context);
      currentRoute = routerState.uri.path;
    } catch (e) {
      // Route state not available yet, assume we're on splash
      currentRoute = '/splash';
    }

    return GestureDetector(
      onDoubleTap: () async {
        // Double tap to toggle fullscreen - 使用安全包装器
        debugPrint('🖱️ 双击切换全屏');
        // 简单的全屏切换，避免复杂的状态检查
        try {
          // 直接尝试设置全屏，不检查当前状态
          await windowManager.setFullScreen(true);
          debugPrint('✅ 全屏设置成功');
        } catch (error) {
          debugPrint('❌ 全屏设置失败: $error');
        }
      },
      onSecondaryTap: () {
        // Right click to show context menu
        _showContextMenu(context, ref);
      },
      child: Stack(
        children: [
          child,
          // Show overlay controls only on main pages, not on splash or auth pages
          if (!displayState.isFullscreen &&
              !currentRoute.startsWith('/splash') &&
              !currentRoute.startsWith('/auth'))
            Positioned(
              top: 8,
              right: 8,
              child: _buildControlOverlay(context, ref),
            ),
        ],
      ),
    );
  }

  Widget _buildControlOverlay(BuildContext context, WidgetRef ref) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black54,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () {
              context.go('/settings');
            },
            tooltip: '设置',
          ),
          IconButton(
            icon: const Icon(Icons.fullscreen, color: Colors.white),
            onPressed: () async {
              try {
                await windowManager.setFullScreen(true);
              } catch (error) {
                debugPrint('Error setting fullscreen: $error');
              }
            },
            tooltip: '全屏',
          ),
          IconButton(
            icon: const Icon(Icons.minimize, color: Colors.white),
            onPressed: () async {
              try {
                await windowManager.minimize();
              } catch (error) {
                debugPrint('Error minimizing window: $error');
              }
            },
            tooltip: '最小化',
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () async {
              try {
                await windowManager.hide();
              } catch (error) {
                debugPrint('Error hiding window: $error');
              }
            },
            tooltip: '隐藏到系统托盘',
          ),
        ],
      ),
    );
  }

  void _showContextMenu(BuildContext context, WidgetRef ref) {
    showMenu(
      context: context,
      position: const RelativeRect.fromLTRB(100, 100, 0, 0),
      items: [
        const PopupMenuItem(
          value: 'settings',
          child: ListTile(
            leading: Icon(Icons.settings),
            title: Text('设置'),
          ),
        ),
        const PopupMenuItem(
          value: 'fullscreen',
          child: ListTile(
            leading: Icon(Icons.fullscreen),
            title: Text('全屏'),
          ),
        ),
        const PopupMenuItem(
          value: 'minimize',
          child: ListTile(
            leading: Icon(Icons.minimize),
            title: Text('最小化'),
          ),
        ),
        const PopupMenuItem(
          value: 'hide',
          child: ListTile(
            leading: Icon(Icons.visibility_off),
            title: Text('隐藏'),
          ),
        ),
      ],
    ).then((value) async {
      switch (value) {
        case 'settings':
          context.go('/settings');
          break;
        case 'fullscreen':
          try {
            await windowManager.setFullScreen(true);
          } catch (error) {
            debugPrint('Error setting fullscreen from menu: $error');
          }
          break;
        case 'minimize':
          try {
            await windowManager.minimize();
          } catch (error) {
            debugPrint('Error minimizing window from menu: $error');
          }
          break;
        case 'hide':
          try {
            await windowManager.hide();
          } catch (error) {
            debugPrint('Error hiding window from menu: $error');
          }
          break;
      }
    });
  }
}
