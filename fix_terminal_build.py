#!/usr/bin/env python3
"""
终端应用编译修复脚本
解决Flutter Windows编译问题
"""

import os
import subprocess
import shutil

def clean_build_directory():
    """清理构建目录"""
    build_dir = os.path.join("terminal_app", "build")
    if os.path.exists(build_dir):
        print("清理构建目录...")
        shutil.rmtree(build_dir)
        print("✓ 构建目录清理完成")
    else:
        print("⚠ 构建目录不存在，跳过清理")

def clean_windows_directory():
    """清理Windows特定目录"""
    windows_dir = os.path.join("terminal_app", "windows")
    if os.path.exists(windows_dir):
        print("清理Windows目录...")
        # 删除除必要的配置文件外的内容
        for item in os.listdir(windows_dir):
            if item not in ["flutter", "Runner", "Runner.exe"]:
                item_path = os.path.join(windows_dir, item)
                if os.path.isdir(item_path):
                    shutil.rmtree(item_path)
                else:
                    os.remove(item_path)
        print("✓ Windows目录清理完成")
    else:
        print("⚠ Windows目录不存在，跳过清理")

def run_flutter_clean():
    """运行Flutter clean命令"""
    print("运行Flutter clean...")
    try:
        result = subprocess.run(
            ["flutter", "clean"],
            cwd="terminal_app",
            capture_output=True,
            text=True,
            timeout=120
        )
        if result.returncode == 0:
            print("✓ Flutter clean成功")
            return True
        else:
            print(f"✗ Flutter clean失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Flutter clean异常: {e}")
        return False

def run_flutter_pub_get():
    """运行Flutter pub get命令"""
    print("运行Flutter pub get...")
    try:
        result = subprocess.run(
            ["flutter", "pub", "get"],
            cwd="terminal_app",
            capture_output=True,
            text=True,
            timeout=120
        )
        if result.returncode == 0:
            print("✓ Flutter pub get成功")
            return True
        else:
            print(f"✗ Flutter pub get失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Flutter pub get异常: {e}")
        return False

def regenerate_windows_files():
    """重新生成Windows文件"""
    print("重新生成Windows文件...")
    try:
        # 删除windows目录并重新创建
        windows_dir = os.path.join("terminal_app", "windows")
        if os.path.exists(windows_dir):
            shutil.rmtree(windows_dir)
        
        # 运行flutter create . 来重新生成Windows文件
        result = subprocess.run(
            ["flutter", "create", ".", "--platforms=windows"],
            cwd="terminal_app",
            capture_output=True,
            text=True,
            timeout=180
        )
        
        if result.returncode == 0:
            print("✓ Windows文件重新生成成功")
            return True
        else:
            print(f"✗ Windows文件重新生成失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ Windows文件重新生成异常: {e}")
        return False

def main():
    print("=" * 60)
    print("终端应用编译修复工具")
    print("=" * 60)
    
    # 检查当前目录
    if not os.path.exists("terminal_app"):
        print("❌ 错误: terminal_app目录不存在")
        return False
    
    print("\n开始修复终端应用编译问题...")
    
    # 执行修复步骤
    steps = [
        ("清理构建目录", clean_build_directory),
        ("清理Windows目录", clean_windows_directory),
        ("运行Flutter clean", run_flutter_clean),
        ("运行Flutter pub get", run_flutter_pub_get),
        ("重新生成Windows文件", regenerate_windows_files),
    ]
    
    success = True
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        if not step_func():
            success = False
            print(f"⚠ {step_name} 步骤出现问题")
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 修复完成！")
        print("现在可以尝试运行: cd terminal_app && flutter run -d windows")
    else:
        print("❌ 修复过程中出现问题")
        print("建议: 删除terminal_app/windows目录后重新运行flutter create .")
    
    print("=" * 60)
    return success

if __name__ == "__main__":
    main()