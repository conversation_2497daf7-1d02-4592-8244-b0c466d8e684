# 📚 ImgCT 项目文档分类索引

## 📋 文档分类说明

本项目的文档已按功能和用途进行分类整理，以便于查找和维护。

---

## 🏗️ 项目架构文档

### 核心架构
- `README.md` - 项目主要说明文档
- `docs/PROJECT_COMPLETION_SUMMARY.md` - 项目完成总结
- `docs/VERSION_MANAGEMENT.md` - 版本管理说明

### 系统架构
- `docs/UNIFIED_GATEWAY_GUIDE.md` - 统一网关架构指南
- `CONNECTION_STATUS_FIX_COMPLETE.md` - 连接状态修复完整说明
- `GATEWAY_REAL_TIME_LOGGING_SUMMARY.md` - 网关实时日志总结

---

## 📖 使用指南文档

### 启动指南
- `docs/START_GUIDE.md` - 完整启动指南
- `START_TERMINAL_README.md` - 终端启动说明
- `docs/STARTUP_SCRIPTS_GUIDE.md` - 启动脚本使用指南
- `STARTUP_SOLUTIONS.md` - 启动问题解决方案

### Python启动器
- `PYTHON_LAUNCHER_GUIDE.md` - Python启动器基础指南
- `PYTHON_LAUNCHER_COMPLETE_GUIDE.md` - Python启动器完整指南

---

## 🔧 开发文档

### 功能实现
- `SCREENSHOT_PAGE_CLEANUP_SUMMARY.md` - 截图页面清理总结
- `terminal_app/LOG_PERSISTENCE_OPTIMIZATION.md` - 日志持久化优化

### 技术细节
- `docs/ENCODING_FIX.md` - 编码问题修复
- `docs/troubleshooting` - 故障排除指南

---

## 📝 版本更新文档

### 变更日志
- `CHANGELOG.md` - 项目变更日志
- `UPDATE_NOTES.md` - 更新说明
- `docs/changelog/VERSION_2.1.0_RELEASE_NOTES.md` - 2.1.0版本发布说明
- `VERSION_2.1.0_RELEASE_NOTES.md` - 2.1.0版本完整发布说明

---

## 🛠️ 配置和部署

### 启动脚本
- `Start_gateway.bat` - 网关启动脚本
- `start_complete_system.bat` - 完整系统启动脚本
- `start_system.py` - Python系统启动脚本
- `start_system.sh` - Shell系统启动脚本
- `start_terminal.bat` - 终端启动脚本

### 诊断工具
- `diagnose_gateway_connection.bat` - 网关连接诊断脚本

---

## 🧪 测试和调试

### 测试文件（待清理）
- `test_connection_status.js` - 连接状态测试
- `test_connection_status_fix.cjs` - 连接状态修复测试
- `test_gateway_connection.js` - 网关连接测试
- `mock_terminal.js` - 模拟终端测试

---

## 📊 项目统计

### 文档数量统计
- **架构文档**: 6个
- **使用指南**: 6个  
- **开发文档**: 4个
- **版本更新**: 4个
- **配置部署**: 6个
- **测试调试**: 4个

### 文档总大小
- 约 150KB+ 的文档内容
- 覆盖项目的所有主要方面

---

## 🎯 文档维护建议

### 分类原则
1. **按功能分类**：相同功能的文档放在同一目录
2. **按用户群体分类**：开发者文档vs用户文档
3. **按更新频率分类**：经常更新vs稳定文档

### 命名规范
- 使用英文文件名，便于跨平台兼容
- 文件名简洁明确，体现文档内容
- 版本文档包含版本号便于识别

### 更新维护
- 新功能开发时同步更新相关文档
- 定期检查文档的时效性和准确性
- 重大更新时创建完整的发布说明

---

## 🔗 相关链接

- [项目主页](README.md)
- [开发指南](docs/START_GUIDE.md)
- [问题排查](docs/troubleshooting)
- [版本历史](CHANGELOG.md)

**最后更新**: 2025-08-24  
**文档版本**: 2.1.0