import 'package:flutter/foundation.dart';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../../../core/services/logger_service.dart';

/// 认证状态枚举
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// 认证状态管理Provider
class AuthProvider extends ChangeNotifier {
  final AuthRepository _authRepository;

  AuthProvider(this._authRepository);

  // 状态变量
  AuthStatus _status = AuthStatus.initial;
  User? _currentUser;
  String? _errorMessage;
  bool _isLoading = false;

  // Getters
  AuthStatus get status => _status;
  User? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _status == AuthStatus.authenticated;

  /// 初始化认证状态
  Future<void> initialize() async {
    try {
      _setLoading(true);
      LoggerService.info('Initializing auth provider');

      // 检查是否有保存的认证信息
      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        _currentUser = user;
        _status = AuthStatus.authenticated;
        LoggerService.info('User already authenticated: ${user.username}');
      } else {
        _status = AuthStatus.unauthenticated;
        LoggerService.info('No authenticated user found');
      }
    } catch (e) {
      LoggerService.error('Failed to initialize auth provider', e);
      _status = AuthStatus.error;
      _errorMessage = e.toString();
    } finally {
      _setLoading(false);
    }
  }

  /// 用户登录
  Future<bool> login(String username, String password) async {
    try {
      _setLoading(true);
      _clearError();
      LoggerService.info('Attempting login for user: $username');

      final user = await _authRepository.login(username, password);
      _currentUser = user;
      _status = AuthStatus.authenticated;
      LoggerService.info('Login successful for user: $username');
      notifyListeners();
      return true;
    } catch (e) {
      LoggerService.error('Login error for user: $username', e);
      _status = AuthStatus.error;
      _errorMessage = '登录失败：${e.toString()}';
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 用户注册
  Future<bool> register(String username, String password, String email) async {
    try {
      _setLoading(true);
      _clearError();
      LoggerService.info('Attempting registration for user: $username');

      final user =
          await _authRepository.register(username, password, email: email);
      _currentUser = user;
      _status = AuthStatus.authenticated;
      LoggerService.info('Registration successful for user: $username');
      notifyListeners();
      return true;
    } catch (e) {
      LoggerService.error('Registration error for user: $username', e);
      _status = AuthStatus.error;
      _errorMessage = '注册失败：${e.toString()}';
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 用户登出
  Future<void> logout() async {
    try {
      _setLoading(true);
      LoggerService.info('Logging out user: ${_currentUser?.username}');

      await _authRepository.logout();
      _currentUser = null;
      _status = AuthStatus.unauthenticated;
      _clearError();

      LoggerService.info('Logout successful');
      notifyListeners();
    } catch (e) {
      LoggerService.error('Logout error', e);
      _errorMessage = '登出失败：${e.toString()}';
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// 刷新用户信息
  Future<void> refreshUser() async {
    if (_currentUser == null) return;

    try {
      LoggerService.info('Refreshing user info for: ${_currentUser!.username}');

      final user = await _authRepository.getCurrentUser();
      if (user != null) {
        _currentUser = user;
        LoggerService.info('User info refreshed successfully');
        notifyListeners();
      }
    } catch (e) {
      LoggerService.error('Failed to refresh user info', e);
    }
  }

  /// 更新用户资料
  Future<bool> updateProfile({
    String? username,
    String? email,
    String? avatar,
  }) async {
    if (_currentUser == null) return false;

    try {
      _setLoading(true);
      LoggerService.info(
          'Updating profile for user: ${_currentUser!.username}');

      final updatedUser = await _authRepository.updateProfile(
        username: username,
        email: email,
        avatar: avatar,
      );

      _currentUser = updatedUser;
      LoggerService.info('Profile updated successfully');
      notifyListeners();
      return true;
    } catch (e) {
      LoggerService.error('Profile update error', e);
      _errorMessage = '更新资料失败：${e.toString()}';
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 修改密码
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    if (_currentUser == null) return false;

    try {
      _setLoading(true);
      LoggerService.info(
          'Changing password for user: ${_currentUser!.username}');

      final success =
          await _authRepository.changePassword(oldPassword, newPassword);
      if (success) {
        LoggerService.info('Password changed successfully');
        return true;
      } else {
        _errorMessage = '密码修改失败：原密码错误';
        LoggerService.warning('Password change failed');
        notifyListeners();
        return false;
      }
    } catch (e) {
      LoggerService.error('Password change error', e);
      _errorMessage = '密码修改失败：${e.toString()}';
      notifyListeners();
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _status = AuthStatus.loading;
    }
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }

  /// 清除错误信息（公共方法）
  void clearError() {
    _clearError();
    notifyListeners();
  }

  @override
  void dispose() {
    LoggerService.info('AuthProvider disposed');
    super.dispose();
  }
}
