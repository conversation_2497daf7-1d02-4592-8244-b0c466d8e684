import 'package:equatable/equatable.dart';

class User extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? avatar;
  final String token;
  final String role;
  final List<String> permissions;
  final DateTime createdAt;
  final DateTime? lastLoginAt;
  final bool isActive;
  final Map<String, dynamic>? preferences;

  const User({
    required this.id,
    required this.name,
    required this.email,
    this.avatar,
    required this.token,
    required this.role,
    required this.permissions,
    required this.createdAt,
    this.lastLoginAt,
    required this.isActive,
    this.preferences,
  });

  // 从JSON创建User对象
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'].toString(), // 处理id可能是int或String的情况
      name: json['name'] as String,
      email: json['email'] as String,
      avatar: json['avatar'] as String?,
      token: json['token'] as String,
      role: json['role'] as String? ?? 'user',
      permissions: List<String>.from(json['permissions'] as List? ?? []),
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLoginAt: json['last_login_at'] != null 
          ? DateTime.parse(json['last_login_at'] as String)
          : null,
      isActive: json['is_active'] as bool? ?? true,
      preferences: json['preferences'] as Map<String, dynamic>?,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'token': token,
      'role': role,
      'permissions': permissions,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'is_active': isActive,
      'preferences': preferences,
    };
  }

  // 复制并修改部分属性
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? avatar,
    String? token,
    String? role,
    List<String>? permissions,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    bool? isActive,
    Map<String, dynamic>? preferences,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      token: token ?? this.token,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      preferences: preferences ?? this.preferences,
    );
  }

  // 获取用户显示名称
  String get displayName => name.isNotEmpty ? name : email;

  // 获取用户头像URL或默认头像
  String get avatarUrl {
    if (avatar != null && avatar!.isNotEmpty) {
      return avatar!;
    }
    // 返回默认头像URL或生成基于用户名的头像
    return 'https://ui-avatars.com/api/?name=${Uri.encodeComponent(displayName)}&background=2196F3&color=fff';
  }

  // 检查是否为管理员
  bool get isAdmin => role == 'admin' || role == 'administrator';

  // 检查是否为超级管理员
  bool get isSuperAdmin => role == 'super_admin';

  // 检查是否有特定权限
  bool hasPermission(String permission) {
    return permissions.contains(permission) || isAdmin;
  }

  // 检查是否有任一权限
  bool hasAnyPermission(List<String> permissionList) {
    return permissionList.any((permission) => hasPermission(permission));
  }

  // 检查是否有所有权限
  bool hasAllPermissions(List<String> permissionList) {
    return permissionList.every((permission) => hasPermission(permission));
  }

  // 获取用户偏好设置
  T? getPreference<T>(String key, [T? defaultValue]) {
    if (preferences == null) return defaultValue;
    return preferences![key] as T? ?? defaultValue;
  }

  // 设置用户偏好
  User setPreference(String key, dynamic value) {
    final newPreferences = Map<String, dynamic>.from(preferences ?? {});
    newPreferences[key] = value;
    return copyWith(preferences: newPreferences);
  }

  // 移除用户偏好
  User removePreference(String key) {
    if (preferences == null) return this;
    final newPreferences = Map<String, dynamic>.from(preferences!);
    newPreferences.remove(key);
    return copyWith(preferences: newPreferences);
  }

  // 检查token是否即将过期（假设token有效期为24小时）
  bool get isTokenExpiringSoon {
    if (lastLoginAt == null) return true;
    final now = DateTime.now();
    final tokenAge = now.difference(lastLoginAt!);
    return tokenAge.inHours >= 23; // 23小时后认为即将过期
  }

  // 检查用户是否在线（基于最后登录时间）
  bool get isOnline {
    if (lastLoginAt == null) return false;
    final now = DateTime.now();
    final timeSinceLogin = now.difference(lastLoginAt!);
    return timeSinceLogin.inMinutes <= 30; // 30分钟内认为在线
  }

  // 获取用户状态文本
  String get statusText {
    if (!isActive) return '已禁用';
    if (isOnline) return '在线';
    if (lastLoginAt == null) return '从未登录';
    
    final now = DateTime.now();
    final timeSinceLogin = now.difference(lastLoginAt!);
    
    if (timeSinceLogin.inDays > 0) {
      return '${timeSinceLogin.inDays}天前在线';
    } else if (timeSinceLogin.inHours > 0) {
      return '${timeSinceLogin.inHours}小时前在线';
    } else {
      return '${timeSinceLogin.inMinutes}分钟前在线';
    }
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        avatar,
        token,
        role,
        permissions,
        createdAt,
        lastLoginAt,
        isActive,
        preferences,
      ];

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $role, isActive: $isActive)';
  }
}

// 用户角色枚举
enum UserRole {
  user('user', '普通用户'),
  admin('admin', '管理员'),
  superAdmin('super_admin', '超级管理员');

  const UserRole(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.user,
    );
  }
}

// 用户权限常量
class UserPermissions {
  // 设备管理权限
  static const String deviceView = 'device:view';
  static const String deviceAdd = 'device:add';
  static const String deviceEdit = 'device:edit';
  static const String deviceDelete = 'device:delete';
  static const String deviceControl = 'device:control';
  
  // 图片管理权限
  static const String imageView = 'image:view';
  static const String imageUpload = 'image:upload';
  static const String imageEdit = 'image:edit';
  static const String imageDelete = 'image:delete';
  static const String imageDownload = 'image:download';
  
  // 系统管理权限
  static const String systemSettings = 'system:settings';
  static const String systemLogs = 'system:logs';
  static const String systemBackup = 'system:backup';
  
  // 用户管理权限
  static const String userView = 'user:view';
  static const String userAdd = 'user:add';
  static const String userEdit = 'user:edit';
  static const String userDelete = 'user:delete';
  
  // 获取所有权限列表
  static List<String> get allPermissions => [
        deviceView, deviceAdd, deviceEdit, deviceDelete, deviceControl,
        imageView, imageUpload, imageEdit, imageDelete, imageDownload,
        systemSettings, systemLogs, systemBackup,
        userView, userAdd, userEdit, userDelete,
      ];
  
  // 获取默认用户权限
  static List<String> get defaultUserPermissions => [
        deviceView, deviceControl,
        imageView, imageUpload, imageDownload,
      ];
  
  // 获取管理员权限
  static List<String> get adminPermissions => allPermissions;
}