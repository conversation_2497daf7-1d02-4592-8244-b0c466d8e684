import 'package:equatable/equatable.dart';

class DeviceInfo extends Equatable {
  final String id;
  final String name;
  final String type;
  final String ipAddress;
  final int port;
  final String status;
  final String? description;
  final String? location;
  final String? version;
  final String? platform;
  final Map<String, dynamic>? capabilities;
  final DateTime lastSeen;
  final DateTime? connectedAt;
  final DateTime? disconnectedAt;
  final bool isOnline;
  final double? batteryLevel;
  final Map<String, dynamic>? systemInfo;
  final List<String>? supportedFormats;
  final Map<String, dynamic>? settings;

  const DeviceInfo({
    required this.id,
    required this.name,
    required this.type,
    required this.ipAddress,
    required this.port,
    required this.status,
    this.description,
    this.location,
    this.version,
    this.platform,
    this.capabilities,
    required this.lastSeen,
    this.connectedAt,
    this.disconnectedAt,
    required this.isOnline,
    this.batteryLevel,
    this.systemInfo,
    this.supportedFormats,
    this.settings,
  });

  // 从JSON创建DeviceInfo对象
  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      ipAddress: json['ip_address'] as String,
      port: json['port'] as int,
      status: json['status'] as String,
      description: json['description'] as String?,
      location: json['location'] as String?,
      version: json['version'] as String?,
      platform: json['platform'] as String?,
      capabilities: json['capabilities'] as Map<String, dynamic>?,
      lastSeen: DateTime.parse(json['last_seen'] as String),
      connectedAt: json['connected_at'] != null
          ? DateTime.parse(json['connected_at'] as String)
          : null,
      disconnectedAt: json['disconnected_at'] != null
          ? DateTime.parse(json['disconnected_at'] as String)
          : null,
      isOnline: json['is_online'] as bool? ?? false,
      batteryLevel: (json['battery_level'] as num?)?.toDouble(),
      systemInfo: json['system_info'] as Map<String, dynamic>?,
      supportedFormats: json['supported_formats'] != null
          ? List<String>.from(json['supported_formats'] as List)
          : null,
      settings: json['settings'] as Map<String, dynamic>?,
    );
  }

  // 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'ip_address': ipAddress,
      'port': port,
      'status': status,
      'description': description,
      'location': location,
      'version': version,
      'platform': platform,
      'capabilities': capabilities,
      'last_seen': lastSeen.toIso8601String(),
      'connected_at': connectedAt?.toIso8601String(),
      'disconnected_at': disconnectedAt?.toIso8601String(),
      'is_online': isOnline,
      'battery_level': batteryLevel,
      'system_info': systemInfo,
      'supported_formats': supportedFormats,
      'settings': settings,
    };
  }

  // 复制并修改部分属性
  DeviceInfo copyWith({
    String? id,
    String? name,
    String? type,
    String? ipAddress,
    int? port,
    String? status,
    String? description,
    String? location,
    String? version,
    String? platform,
    Map<String, dynamic>? capabilities,
    DateTime? lastSeen,
    DateTime? connectedAt,
    DateTime? disconnectedAt,
    bool? isOnline,
    double? batteryLevel,
    Map<String, dynamic>? systemInfo,
    List<String>? supportedFormats,
    Map<String, dynamic>? settings,
  }) {
    return DeviceInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      status: status ?? this.status,
      description: description ?? this.description,
      location: location ?? this.location,
      version: version ?? this.version,
      platform: platform ?? this.platform,
      capabilities: capabilities ?? this.capabilities,
      lastSeen: lastSeen ?? this.lastSeen,
      connectedAt: connectedAt ?? this.connectedAt,
      disconnectedAt: disconnectedAt ?? this.disconnectedAt,
      isOnline: isOnline ?? this.isOnline,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      systemInfo: systemInfo ?? this.systemInfo,
      supportedFormats: supportedFormats ?? this.supportedFormats,
      settings: settings ?? this.settings,
    );
  }

  // 获取设备完整地址
  String get fullAddress => '$ipAddress:$port';

  // 获取设备显示名称
  String get displayName => name.isNotEmpty ? name : fullAddress;

  // 获取设备状态颜色
  String get statusColor {
    switch (status.toLowerCase()) {
      case 'online':
      case 'connected':
        return 'green';
      case 'offline':
      case 'disconnected':
        return 'red';
      case 'connecting':
      case 'reconnecting':
        return 'orange';
      case 'error':
        return 'red';
      default:
        return 'grey';
    }
  }

  // 获取设备类型图标
  String get typeIcon {
    switch (type.toLowerCase()) {
      case 'terminal':
      case 'computer':
        return 'computer';
      case 'display':
      case 'monitor':
        return 'tv';
      case 'mobile':
      case 'phone':
        return 'phone';
      case 'tablet':
        return 'tablet';
      case 'camera':
        return 'camera';
      case 'projector':
        return 'videocam';
      default:
        return 'device_unknown';
    }
  }

  // 检查设备是否支持特定功能
  bool hasCapability(String capability) {
    if (capabilities == null) return false;
    return capabilities![capability] == true;
  }

  // 获取设备能力值
  T? getCapability<T>(String capability, [T? defaultValue]) {
    if (capabilities == null) return defaultValue;
    return capabilities![capability] as T? ?? defaultValue;
  }

  // 检查设备是否支持格式
  bool supportsFormat(String format) {
    if (supportedFormats == null) return false;
    return supportedFormats!.contains(format.toLowerCase());
  }

  // 获取设备设置
  T? getSetting<T>(String key, [T? defaultValue]) {
    if (settings == null) return defaultValue;
    return settings![key] as T? ?? defaultValue;
  }

  // 设置设备配置
  DeviceInfo setSetting(String key, dynamic value) {
    final newSettings = Map<String, dynamic>.from(settings ?? {});
    newSettings[key] = value;
    return copyWith(settings: newSettings);
  }

  // 获取系统信息
  T? getSystemInfo<T>(String key, [T? defaultValue]) {
    if (systemInfo == null) return defaultValue;
    return systemInfo![key] as T? ?? defaultValue;
  }

  // 检查设备是否需要更新
  bool get needsUpdate {
    final latestVersion = getSystemInfo<String>('latest_version');
    if (latestVersion == null || version == null) return false;
    return version != latestVersion;
  }

  // 获取设备运行时间
  Duration? get uptime {
    if (connectedAt == null) return null;
    return DateTime.now().difference(connectedAt!);
  }

  // 获取离线时间
  Duration? get downtime {
    if (disconnectedAt == null || isOnline) return null;
    return DateTime.now().difference(disconnectedAt!);
  }

  // 获取最后活动时间描述
  String get lastSeenDescription {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }

  // 获取电池状态描述
  String get batteryDescription {
    if (batteryLevel == null) return '未知';
    
    if (batteryLevel! >= 80) {
      return '电量充足 (${batteryLevel!.toInt()}%)';
    } else if (batteryLevel! >= 50) {
      return '电量正常 (${batteryLevel!.toInt()}%)';
    } else if (batteryLevel! >= 20) {
      return '电量偏低 (${batteryLevel!.toInt()}%)';
    } else {
      return '电量不足 (${batteryLevel!.toInt()}%)';
    }
  }

  // 检查设备是否健康
  bool get isHealthy {
    if (!isOnline) return false;
    if (batteryLevel != null && batteryLevel! < 10) return false;
    if (status.toLowerCase() == 'error') return false;
    return true;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        ipAddress,
        port,
        status,
        description,
        location,
        version,
        platform,
        capabilities,
        lastSeen,
        connectedAt,
        disconnectedAt,
        isOnline,
        batteryLevel,
        systemInfo,
        supportedFormats,
        settings,
      ];

  @override
  String toString() {
    return 'DeviceInfo(id: $id, name: $name, type: $type, address: $fullAddress, status: $status, online: $isOnline)';
  }
}

// 设备类型枚举
enum DeviceType {
  terminal('terminal', '终端设备'),
  display('display', '显示设备'),
  mobile('mobile', '移动设备'),
  tablet('tablet', '平板设备'),
  camera('camera', '摄像设备'),
  projector('projector', '投影设备'),
  unknown('unknown', '未知设备');

  const DeviceType(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static DeviceType fromString(String value) {
    return DeviceType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => DeviceType.unknown,
    );
  }
}

// 设备状态枚举
enum DeviceStatus {
  unregistered,
  registering,
  registered,
  offline,
  online,
  connecting,
  disconnecting,
  capturing,
  error,
  maintenance,
}

// 设备能力常量
class DeviceCapabilities {
  static const String screenshot = 'screenshot';
  static const String remoteControl = 'remote_control';
  static const String fileTransfer = 'file_transfer';
  static const String audioCapture = 'audio_capture';
  static const String videoCapture = 'video_capture';
  static const String displayControl = 'display_control';
  static const String systemInfo = 'system_info';
  static const String powerControl = 'power_control';
  static const String networkInfo = 'network_info';
  static const String storageInfo = 'storage_info';
  
  static List<String> get allCapabilities => [
        screenshot,
        remoteControl,
        fileTransfer,
        audioCapture,
        videoCapture,
        displayControl,
        systemInfo,
        powerControl,
        networkInfo,
        storageInfo,
      ];
}

// 支持的文件格式
class SupportedFormats {
  static const List<String> imageFormats = [
    'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'
  ];
  
  static const List<String> videoFormats = [
    'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'
  ];
  
  static const List<String> audioFormats = [
    'mp3', 'wav', 'flac', 'aac', 'ogg', 'wma'
  ];
  
  static List<String> get allFormats => [
        ...imageFormats,
        ...videoFormats,
        ...audioFormats,
      ];
}