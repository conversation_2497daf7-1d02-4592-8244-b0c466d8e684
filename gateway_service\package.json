{"name": "flutter-imgct-gateway", "version": "1.0.0", "description": "Flutter图片切换系统 - 网关服务", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "cross-env NODE_ENV=development ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "prebuild": "npm run clean", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["flutter", "websocket", "gateway", "image", "screenshot", "device-management"], "author": "Flutter ImgCT Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dgram": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "rate-limiter-flexible": "^4.0.1", "sharp": "^0.32.6", "socket.io": "^3.1.2", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.18.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.8.10", "@types/uuid": "^9.0.7", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "cross-env": "^10.0.0", "eslint": "^8.53.0", "jest": "^29.7.0", "nodemon": "^3.0.1", "prettier": "^3.0.3", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/flutter-imgct/gateway-service.git"}, "bugs": {"url": "https://github.com/flutter-imgct/gateway-service/issues"}, "homepage": "https://github.com/flutter-imgct/gateway-service#readme"}