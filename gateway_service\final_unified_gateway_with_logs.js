/**
 * Flutter图片切换系统 - 统一网关服务 (增强版 - 实时日志)
 *
 * 版本: v1.1.0 (实时日志增强版)
 * 端口: 7777
 * 架构: 单端口多服务路由 + 实时日志
 *
 * 日志功能:
 * - 分级日志输出: DEBUG、INFO、WARN、ERROR
 * - 实时控制台日志: 彩色输出，易于观察
 * - 文件日志持久化: 按日期自动轮转
 * - 连接状态跟踪: 详细的WebSocket连接监控
 * - 性能监控: 请求响应时间、连接数等
 * - 错误详细记录: 完整的错误堆栈和上下文
 *
 * <AUTHOR> Assistant
 * @date 2025-08-24
 * @version 1.1.0
 */

const { createServer } = require('http');
const WebSocket = require('ws');
const fs = require('fs');
const path = require('path');

// ==================== 日志系统配置 ====================

// 创建日志目录
const logsDir = path.join(__dirname, 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// 日志级别定义
const LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

// 当前日志级别（可配置）
const currentLogLevel = LogLevel.DEBUG;

// 颜色配置
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  gray: '\x1b[90m'
};

// 日志工具类
class Logger {
  constructor() {
    this.logFile = path.join(logsDir, `gateway-${this.getDateString()}.log`);
    this.errorFile = path.join(logsDir, `error-${this.getDateString()}.log`);
    this.startTime = Date.now();
  }

  getDateString() {
    const date = new Date();
    return date.toISOString().split('T')[0];
  }

  getTimestamp() {
    return new Date().toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: 'Asia/Shanghai'
    });
  }

  formatMessage(level, category, message, data = null) {
    const timestamp = this.getTimestamp();
    const uptime = ((Date.now() - this.startTime) / 1000).toFixed(1);
    
    let formattedMessage = `[${timestamp}] [${uptime}s] [${level}] [${category}] ${message}`;
    
    if (data) {
      formattedMessage += ` | Data: ${JSON.stringify(data)}`;
    }
    
    return formattedMessage;
  }

  writeToFile(message, isError = false) {
    const file = isError ? this.errorFile : this.logFile;
    const logEntry = message + '\n';
    
    try {
      fs.appendFileSync(file, logEntry, 'utf8');
    } catch (error) {
      console.error('写入日志文件失败:', error.message);
    }
  }

  log(level, category, message, data = null, color = colors.white) {
    if (LogLevel[level] < currentLogLevel) return;

    const formattedMessage = this.formatMessage(level, category, message, data);
    
    // 控制台输出（带颜色）
    const coloredOutput = `${color}${formattedMessage}${colors.reset}`;
    console.log(coloredOutput);
    
    // 文件输出
    this.writeToFile(formattedMessage, level === 'ERROR');
  }

  debug(category, message, data = null) {
    this.log('DEBUG', category, message, data, colors.gray);
  }

  info(category, message, data = null) {
    this.log('INFO', category, message, data, colors.cyan);
  }

  warn(category, message, data = null) {
    this.log('WARN', category, message, data, colors.yellow);
  }

  error(category, message, data = null) {
    this.log('ERROR', category, message, data, colors.red);
  }

  success(category, message, data = null) {
    this.log('INFO', category, message, data, colors.green);
  }

  request(method, url, duration = null) {
    const msg = duration ? `${method} ${url} (${duration}ms)` : `${method} ${url}`;
    this.log('INFO', 'HTTP', msg, null, colors.blue);
  }

  websocket(action, clientType, clientId, details = null) {
    this.log('INFO', 'WebSocket', `${action} - ${clientType}:${clientId}`, details, colors.magenta);
  }
}

// 创建全局日志实例
const logger = new Logger();

// ==================== 服务器配置 ====================

logger.info('SYSTEM', '🚀 启动统一网关服务 v1.1.0 (实时日志增强版)');
logger.info('SYSTEM', `📁 日志目录: ${logsDir}`);
logger.info('SYSTEM', `📄 运行日志: ${logger.logFile}`);
logger.info('SYSTEM', `❌ 错误日志: ${logger.errorFile}`);

// 存储连接的设备
const connectedDevices = new Map();
const connectedControllers = new Map();

// 连接统计
let totalConnections = 0;
let totalRequests = 0;

// ==================== HTTP服务器 ====================

const server = createServer((req, res) => {
  const startTime = Date.now();
  totalRequests++;
  
  logger.debug('HTTP', `收到请求: ${req.method} ${req.url}`, {
    userAgent: req.headers['user-agent'],
    remoteAddress: req.connection.remoteAddress
  });

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  // 路由处理
  const handleRoute = () => {
    if (req.url === '/health') {
      const healthData = { 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        connectedDevices: connectedDevices.size,
        connectedControllers: connectedControllers.size,
        totalConnections,
        totalRequests,
        uptime: ((Date.now() - logger.startTime) / 1000).toFixed(1)
      };
      
      res.writeHead(200);
      res.end(JSON.stringify(healthData, null, 2));
      
      logger.success('HTTP', '健康检查响应', healthData);
      
    } else if (req.url === '/terminal/status') {
      const terminalData = {
        service: 'terminal',
        connectedDevices: connectedDevices.size,
        devices: Array.from(connectedDevices.entries()).map(([id, info]) => ({
          clientId: id,
          deviceId: info.deviceId || 'unknown',
          connectedAt: info.connectedAt,
          lastHeartbeat: info.lastHeartbeat,
          status: info.status || 'online'
        }))
      };
      
      res.writeHead(200);
      res.end(JSON.stringify(terminalData, null, 2));
      
      logger.info('HTTP', '终端状态查询', { deviceCount: connectedDevices.size });
      
    } else if (req.url === '/controller/status') {
      const controllerData = {
        service: 'controller',
        connectedControllers: connectedControllers.size,
        controllers: Array.from(connectedControllers.keys())
      };
      
      res.writeHead(200);
      res.end(JSON.stringify(controllerData, null, 2));
      
      logger.info('HTTP', '控制端状态查询', { controllerCount: connectedControllers.size });
      
    } else {
      const serviceInfo = {
        name: 'Flutter Image Control Gateway (实时日志增强版)',
        version: '1.1.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        uptime: ((Date.now() - logger.startTime) / 1000).toFixed(1),
        statistics: {
          totalConnections,
          totalRequests,
          connectedDevices: connectedDevices.size,
          connectedControllers: connectedControllers.size
        },
        services: {
          terminal: {
            path: '/terminal',
            websocket: '/terminal/ws',
            connectedDevices: connectedDevices.size
          },
          controller: {
            path: '/controller', 
            websocket: '/controller/ws',
            connectedControllers: connectedControllers.size
          }
        }
      };
      
      res.writeHead(200);
      res.end(JSON.stringify(serviceInfo, null, 2));
      
      logger.info('HTTP', '服务信息查询');
    }
  };

  try {
    handleRoute();
  } catch (error) {
    logger.error('HTTP', '请求处理错误', {
      url: req.url,
      method: req.method,
      error: error.message,
      stack: error.stack
    });
    
    res.writeHead(500);
    res.end(JSON.stringify({
      error: '内部服务器错误',
      message: error.message,
      timestamp: new Date().toISOString()
    }));
  }

  // 记录请求完成
  const duration = Date.now() - startTime;
  logger.request(req.method, req.url, duration);
});

// ==================== WebSocket服务器 ====================

const wss = new WebSocket.Server({ 
  server: server
});

logger.success('WebSocket', '📡 WebSocket服务器已创建');

// WebSocket连接处理
wss.on('connection', (ws, req) => {
  totalConnections++;
  
  const fullUrl = req.url;
  const url = new URL(fullUrl, `http://${req.headers.host}`);
  const pathname = url.pathname;
  
  logger.websocket('连接建立', 'Unknown', 'Pending', {
    fullUrl,
    pathname,
    remoteAddress: req.connection.remoteAddress,
    userAgent: req.headers['user-agent']
  });
  
  if (pathname === '/terminal/ws') {
    handleTerminalConnection(ws, req);
  } else if (pathname === '/controller/ws') {
    handleControllerConnection(ws, req);
  } else {
    logger.warn('WebSocket', '未知路径连接', { pathname });
    
    ws.send(JSON.stringify({
      type: 'error',
      message: '未知路径，请使用 /terminal/ws 或 /controller/ws',
      timestamp: new Date().toISOString()
    }));
    
    ws.close(1000, '未知路径');
  }
});

// ==================== 终端设备连接处理 ====================

function handleTerminalConnection(ws, req) {
  const clientId = `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  logger.success('Terminal', '📱 终端设备连接', { clientId });
  
  // 存储连接信息
  const deviceInfo = {
    ws: ws,
    connectedAt: new Date().toISOString(),
    lastHeartbeat: new Date().toISOString(),
    type: 'terminal',
    remoteAddress: req.connection.remoteAddress
  };
  
  connectedDevices.set(clientId, deviceInfo);
  
  // 发送欢迎消息
  const welcomeMsg = {
    type: 'welcome',
    message: '欢迎连接到终端设备服务',
    clientId: clientId,
    service: 'terminal',
    timestamp: new Date().toISOString()
  };
  
  ws.send(JSON.stringify(welcomeMsg));
  logger.debug('Terminal', '发送欢迎消息', { clientId, message: welcomeMsg });
  
  // 处理消息
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      logger.debug('Terminal', '收到消息', { clientId, type: message.type, data: message });
      
      switch (message.type) {
        case 'device_register':
          handleDeviceRegister(ws, clientId, message);
          break;
          
        case 'heartbeat':
          handleHeartbeat(ws, clientId, 'terminal');
          break;
          
        case 'ping':
          handlePing(ws, clientId);
          break;

        case 'device_status':
          handleDeviceStatus(ws, clientId, message);
          break;

        default:
          logger.warn('Terminal', '未知消息类型', { clientId, type: message.type });
          sendError(ws, '未知消息类型', message.type);
      }
    } catch (error) {
      logger.error('Terminal', '消息解析错误', {
        clientId,
        error: error.message,
        data: data.toString(),
        stack: error.stack
      });
      
      sendError(ws, '消息格式错误', error.message);
    }
  });
  
  // 处理连接错误
  ws.on('error', (error) => {
    logger.error('Terminal', 'WebSocket连接错误', {
      clientId,
      error: error.message,
      stack: error.stack
    });
  });
  
  // 处理断开连接
  ws.on('close', (code, reason) => {
    logger.warn('Terminal', '设备断开连接', {
      clientId,
      code,
      reason: reason.toString(),
      duration: getDuration(deviceInfo.connectedAt)
    });
    
    connectedDevices.delete(clientId);
  });
}

// ==================== 控制端连接处理 ====================

function handleControllerConnection(ws, req) {
  const clientId = `controller_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  logger.success('Controller', '🎮 控制端连接', { clientId });
  
  // 存储连接信息
  const controllerInfo = {
    ws: ws,
    connectedAt: new Date().toISOString(),
    lastHeartbeat: new Date().toISOString(),
    type: 'controller',
    remoteAddress: req.connection.remoteAddress
  };
  
  connectedControllers.set(clientId, controllerInfo);
  
  // 发送欢迎消息
  const welcomeMsg = {
    type: 'welcome',
    message: '欢迎连接到控制端服务',
    clientId: clientId,
    service: 'controller',
    timestamp: new Date().toISOString()
  };
  
  ws.send(JSON.stringify(welcomeMsg));
  logger.debug('Controller', '发送欢迎消息', { clientId, message: welcomeMsg });
  
  // 处理消息
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      logger.debug('Controller', '收到消息', { clientId, type: message.type, data: message });
      
      switch (message.type) {
        case 'device_register':
          handleControllerRegister(ws, clientId, message);
          break;
          
        case 'get_devices':
          handleGetDevices(ws, clientId);
          break;
          
        case 'send_command':
          handleSendCommand(ws, clientId, message);
          break;
          
        case 'heartbeat':
          handleHeartbeat(ws, clientId, 'controller');
          break;
          
        default:
          logger.warn('Controller', '未知消息类型', { clientId, type: message.type });
          sendError(ws, '未知消息类型', message.type);
      }
    } catch (error) {
      logger.error('Controller', '消息解析错误', {
        clientId,
        error: error.message,
        data: data.toString(),
        stack: error.stack
      });
      
      sendError(ws, '消息格式错误', error.message);
    }
  });
  
  // 处理连接错误
  ws.on('error', (error) => {
    logger.error('Controller', 'WebSocket连接错误', {
      clientId,
      error: error.message,
      stack: error.stack
    });
  });
  
  // 处理断开连接
  ws.on('close', (code, reason) => {
    logger.warn('Controller', '控制端断开连接', {
      clientId,
      code,
      reason: reason.toString(),
      duration: getDuration(controllerInfo.connectedAt)
    });
    
    connectedControllers.delete(clientId);
  });
}

// ==================== 消息处理函数 ====================

function handleDeviceRegister(ws, clientId, message) {
  const deviceId = message.data?.id || message.data?.deviceId || 'unknown';
  
  logger.success('Terminal', '设备注册', { clientId, deviceId, deviceData: message.data });
  
  // 更新连接设备信息
  if (connectedDevices.has(clientId)) {
    const deviceInfo = connectedDevices.get(clientId);
    deviceInfo.deviceId = deviceId;
    deviceInfo.deviceData = message.data;
    deviceInfo.registeredAt = new Date().toISOString();
    connectedDevices.set(clientId, deviceInfo);
  }

  const response = {
    type: 'device_registered',
    data: {
      success: true,
      deviceId: deviceId,
      clientId: clientId,
      service: 'terminal',
      timestamp: new Date().toISOString()
    }
  };
  
  ws.send(JSON.stringify(response));
  logger.debug('Terminal', '发送注册确认', { clientId, response });
}

function handleControllerRegister(ws, clientId, message) {
  logger.success('Controller', '控制端注册', { clientId, data: message.payload || message.data });
  
  // 更新控制端连接信息
  if (connectedControllers.has(clientId)) {
    const controllerInfo = connectedControllers.get(clientId);
    controllerInfo.deviceData = message.payload || message.data;
    controllerInfo.registeredAt = new Date().toISOString();
    connectedControllers.set(clientId, controllerInfo);
  }

  const response = {
    type: 'device_registered',
    data: {
      success: true,
      clientId: clientId,
      service: 'controller',
      timestamp: new Date().toISOString()
    }
  };
  
  ws.send(JSON.stringify(response));
  logger.debug('Controller', '发送注册确认', { clientId, response });
}

function handleGetDevices(ws, clientId) {
  const devices = Array.from(connectedDevices.entries()).map(([id, info]) => ({
    clientId: id,
    deviceId: info.deviceId || 'unknown',
    type: info.type,
    connectedAt: info.connectedAt,
    lastHeartbeat: info.lastHeartbeat
  }));
  
  const response = {
    type: 'devices_list',
    data: {
      devices: devices,
      count: connectedDevices.size,
      timestamp: new Date().toISOString()
    }
  };
  
  ws.send(JSON.stringify(response));
  logger.info('Controller', '返回设备列表', { clientId, deviceCount: devices.length });
}

function handleSendCommand(ws, clientId, message) {
  const targetDevice = message.data?.targetDevice;
  const command = message.data?.command;
  
  logger.info('Controller', '发送命令到终端', { 
    from: clientId, 
    to: targetDevice, 
    command: command 
  });
  
  if (connectedDevices.has(targetDevice)) {
    const deviceConnection = connectedDevices.get(targetDevice);
    const commandMsg = {
      type: 'command',
      data: command,
      from: clientId,
      timestamp: new Date().toISOString()
    };
    
    deviceConnection.ws.send(JSON.stringify(commandMsg));
    
    const response = {
      type: 'command_sent',
      data: {
        success: true,
        targetDevice: targetDevice,
        command: command,
        timestamp: new Date().toISOString()
      }
    };
    
    ws.send(JSON.stringify(response));
    logger.success('Controller', '命令发送成功', { from: clientId, to: targetDevice });
    
  } else {
    const errorResponse = {
      type: 'error',
      data: {
        message: '目标设备未连接',
        targetDevice: targetDevice
      }
    };
    
    ws.send(JSON.stringify(errorResponse));
    logger.warn('Controller', '目标设备未连接', { from: clientId, to: targetDevice });
  }
}

function handleHeartbeat(ws, clientId, clientType) {
  const connections = clientType === 'terminal' ? connectedDevices : connectedControllers;
  
  if (connections.has(clientId)) {
    connections.get(clientId).lastHeartbeat = new Date().toISOString();
  }
  
  const response = {
    type: 'heartbeat_ack',
    data: {
      timestamp: new Date().toISOString()
    }
  };
  
  ws.send(JSON.stringify(response));
  logger.debug(clientType === 'terminal' ? 'Terminal' : 'Controller', '心跳响应', { clientId });
}

function handlePing(ws, clientId) {
  const response = {
    type: 'pong',
    data: {
      timestamp: new Date().toISOString()
    }
  };
  
  ws.send(JSON.stringify(response));
  logger.debug('Terminal', 'Ping-Pong响应', { clientId });
}

function handleDeviceStatus(ws, clientId, message) {
  logger.info('Terminal', '设备状态更新', { clientId, status: message.data });
  
  // 更新设备状态信息
  if (connectedDevices.has(clientId)) {
    const deviceInfo = connectedDevices.get(clientId);
    deviceInfo.status = message.data?.status || 'online';
    deviceInfo.lastStatusUpdate = new Date().toISOString();
    connectedDevices.set(clientId, deviceInfo);
  }

  const response = {
    type: 'status_ack',
    data: {
      received: true,
      timestamp: new Date().toISOString()
    }
  };
  
  ws.send(JSON.stringify(response));
  logger.debug('Terminal', '状态更新确认', { clientId });
}

function sendError(ws, message, details = null) {
  const errorResponse = {
    type: 'error',
    data: {
      message: message,
      details: details,
      timestamp: new Date().toISOString()
    }
  };
  
  ws.send(JSON.stringify(errorResponse));
}

// ==================== 工具函数 ====================

function getDuration(startTime) {
  const start = new Date(startTime);
  const duration = Math.round((Date.now() - start.getTime()) / 1000);
  return `${duration}秒`;
}

// ==================== WebSocket服务器事件 ====================

wss.on('error', (error) => {
  logger.error('WebSocket', '服务器错误', {
    error: error.message,
    stack: error.stack
  });
});

wss.on('listening', () => {
  logger.success('WebSocket', '👂 WebSocket服务器开始监听');
});

// ==================== 启动服务器 ====================

const PORT = process.env.PORT || 7777;
server.listen(PORT, () => {
  logger.info('SYSTEM', '\n╔══════════════════════════════════════════════════════════════╗');
  logger.info('SYSTEM', '║                                                              ║');
  logger.info('SYSTEM', '║    Flutter Image Control Gateway (实时日志增强版)          ║');
  logger.info('SYSTEM', '║    Version: 1.1.0                                           ║');
  logger.info('SYSTEM', '║                                                              ║');
  logger.info('SYSTEM', '╚══════════════════════════════════════════════════════════════╝\n');
  
  logger.success('SYSTEM', `🌐 服务器运行在: http://localhost:${PORT}`);
  logger.success('SYSTEM', `📱 终端设备WebSocket: ws://localhost:${PORT}/terminal/ws`);
  logger.success('SYSTEM', `🎮 控制端WebSocket: ws://localhost:${PORT}/controller/ws`);
  logger.success('SYSTEM', `🏥 健康检查: http://localhost:${PORT}/health`);
  logger.info('SYSTEM', `⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
  logger.info('SYSTEM', '');
  logger.info('SYSTEM', '📋 可用路由:');
  logger.info('SYSTEM', `   GET  /                    - 服务信息`);
  logger.info('SYSTEM', `   GET  /health              - 健康检查`);
  logger.info('SYSTEM', `   GET  /terminal/status     - 终端设备状态`);
  logger.info('SYSTEM', `   GET  /controller/status   - 控制端状态`);
  logger.info('SYSTEM', '');
  logger.info('SYSTEM', '📁 日志功能:');
  logger.info('SYSTEM', `   📄 运行日志: ${logger.logFile}`);
  logger.info('SYSTEM', `   ❌ 错误日志: ${logger.errorFile}`);
  logger.info('SYSTEM', `   🖥️  控制台: 实时彩色输出`);
  logger.info('SYSTEM', '');
});

// ==================== 错误处理 ====================

server.on('error', (error) => {
  logger.error('SYSTEM', 'HTTP服务器错误', {
    error: error.message,
    stack: error.stack
  });
});

// ==================== 定期状态报告 ====================

setInterval(() => {
  const stats = {
    connectedDevices: connectedDevices.size,
    connectedControllers: connectedControllers.size,
    totalConnections,
    totalRequests,
    uptime: ((Date.now() - logger.startTime) / 1000).toFixed(1),
    memoryUsage: process.memoryUsage()
  };
  
  logger.info('STATISTICS', '系统状态报告', stats);
}, 60000); // 每分钟报告一次

// ==================== 优雅关闭 ====================

process.on('SIGINT', () => {
  logger.warn('SYSTEM', '\n🛑 收到SIGINT信号，正在关闭服务器...');
  shutdown();
});

process.on('SIGTERM', () => {
  logger.warn('SYSTEM', '\n🛑 收到SIGTERM信号，正在关闭服务器...');
  shutdown();
});

function shutdown() {
  logger.info('SYSTEM', '正在关闭所有WebSocket连接...');
  
  // 关闭所有WebSocket连接
  wss.clients.forEach((ws) => {
    ws.close(1001, '服务器关闭');
  });
  
  server.close(() => {
    const finalStats = {
      totalConnections,
      totalRequests,
      uptime: ((Date.now() - logger.startTime) / 1000).toFixed(1)
    };
    
    logger.success('SYSTEM', '✅ 服务器已关闭', finalStats);
    process.exit(0);
  });
}

// ==================== 未处理的异常 ====================

process.on('uncaughtException', (error) => {
  logger.error('SYSTEM', '未捕获的异常', {
    error: error.message,
    stack: error.stack
  });
  
  // 给一点时间让日志写入文件
  setTimeout(() => {
    process.exit(1);
  }, 1000);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('SYSTEM', '未处理的Promise拒绝', {
    reason: reason,
    promise: promise
  });
});