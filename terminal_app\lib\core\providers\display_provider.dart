import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:typed_data';

/// 显示模式
enum DisplayMode {
  idle,
  displaying,
  slideshow,
  fullscreen,
}

/// 显示状态数据
class DisplayState {
  final DisplayMode mode;
  final Uint8List? currentImage;
  final String? currentImagePath;
  final int currentIndex;
  final List<String> imageList;
  final bool isLoading;
  final String? errorMessage;
  final DateTime lastUpdated;
  final Duration slideshowInterval;
  final bool autoFit;

  const DisplayState({
    required this.mode,
    this.currentImage,
    this.currentImagePath,
    this.currentIndex = 0,
    this.imageList = const [],
    this.isLoading = false,
    this.errorMessage,
    required this.lastUpdated,
    this.slideshowInterval = const Duration(seconds: 5),
    this.autoFit = true,
  });

  /// 是否为全屏模式
  bool get isFullscreen => mode == DisplayMode.fullscreen;

  DisplayState copyWith({
    DisplayMode? mode,
    Uint8List? currentImage,
    String? currentImagePath,
    int? currentIndex,
    List<String>? imageList,
    bool? isLoading,
    String? errorMessage,
    DateTime? lastUpdated,
    Duration? slideshowInterval,
    bool? autoFit,
  }) {
    return DisplayState(
      mode: mode ?? this.mode,
      currentImage: currentImage ?? this.currentImage,
      currentImagePath: currentImagePath ?? this.currentImagePath,
      currentIndex: currentIndex ?? this.currentIndex,
      imageList: imageList ?? this.imageList,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      slideshowInterval: slideshowInterval ?? this.slideshowInterval,
      autoFit: autoFit ?? this.autoFit,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DisplayState &&
        other.mode == mode &&
        other.currentImagePath == currentImagePath &&
        other.currentIndex == currentIndex &&
        other.isLoading == isLoading &&
        other.errorMessage == errorMessage &&
        other.slideshowInterval == slideshowInterval &&
        other.autoFit == autoFit;
  }

  @override
  int get hashCode {
    return mode.hashCode ^
        currentImagePath.hashCode ^
        currentIndex.hashCode ^
        isLoading.hashCode ^
        errorMessage.hashCode ^
        slideshowInterval.hashCode ^
        autoFit.hashCode;
  }
}

/// 显示状态通知器
class DisplayStateNotifier extends StateNotifier<DisplayState> {
  DisplayStateNotifier()
      : super(DisplayState(
          mode: DisplayMode.idle,
          lastUpdated: DateTime.now(),
        ));

  /// 设置显示模式
  void setMode(DisplayMode mode) {
    state = state.copyWith(
      mode: mode,
      lastUpdated: DateTime.now(),
    );
  }

  /// 设置当前图片
  void setCurrentImage(Uint8List? imageData, {String? imagePath}) {
    state = state.copyWith(
      currentImage: imageData,
      currentImagePath: imagePath,
      lastUpdated: DateTime.now(),
    );
  }

  /// 设置图片列表
  void setImageList(List<String> imageList, {int? currentIndex}) {
    state = state.copyWith(
      imageList: imageList,
      currentIndex: currentIndex ?? 0,
      lastUpdated: DateTime.now(),
    );
  }

  /// 下一张图片
  void nextImage() {
    if (state.imageList.isNotEmpty) {
      final nextIndex = (state.currentIndex + 1) % state.imageList.length;
      state = state.copyWith(
        currentIndex: nextIndex,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// 上一张图片
  void previousImage() {
    if (state.imageList.isNotEmpty) {
      final prevIndex = state.currentIndex > 0
          ? state.currentIndex - 1
          : state.imageList.length - 1;
      state = state.copyWith(
        currentIndex: prevIndex,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// 跳转到指定图片
  void jumpToImage(int index) {
    if (index >= 0 && index < state.imageList.length) {
      state = state.copyWith(
        currentIndex: index,
        lastUpdated: DateTime.now(),
      );
    }
  }

  /// 设置加载状态
  void setLoading(bool isLoading) {
    state = state.copyWith(
      isLoading: isLoading,
      lastUpdated: DateTime.now(),
    );
  }

  /// 设置错误信息
  void setError(String? errorMessage) {
    state = state.copyWith(
      errorMessage: errorMessage,
      lastUpdated: DateTime.now(),
    );
  }

  /// 设置幻灯片间隔
  void setSlideshowInterval(Duration interval) {
    state = state.copyWith(
      slideshowInterval: interval,
      lastUpdated: DateTime.now(),
    );
  }

  /// 设置自动适应
  void setAutoFit(bool autoFit) {
    state = state.copyWith(
      autoFit: autoFit,
      lastUpdated: DateTime.now(),
    );
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(
      errorMessage: null,
      lastUpdated: DateTime.now(),
    );
  }

  /// 重置状态
  void reset() {
    state = DisplayState(
      mode: DisplayMode.idle,
      lastUpdated: DateTime.now(),
    );
  }
}

/// 显示状态Provider
final displayProvider = StateNotifierProvider<DisplayStateNotifier, DisplayState>(
  (ref) => DisplayStateNotifier(),
);