import 'storage_service.dart';
import 'logger_service.dart';
import '../models/screenshot_folder_config.dart';
import 'dart:math' as math;

/// 截图配置模型
class ScreenshotSettings {
  final String? customSavePath;
  final bool enableCompression;
  final int compressionQuality;
  final String imageFormat;
  final bool autoOpenFolder;
  final bool showPreview;
  final bool saveMetadata;
  final ScreenshotFolderConfig? _folderConfig;

  /// 获取文件夹配置，确保永远不返回null
  ScreenshotFolderConfig get folderConfig {
    return _folderConfig ?? const ScreenshotFolderConfig();
  }

  const ScreenshotSettings({
    this.customSavePath,
    this.enableCompression = false,
    this.compressionQuality = 90,
    this.imageFormat = 'png',
    this.autoOpenFolder = false,
    this.showPreview = true,
    this.saveMetadata = true,
    ScreenshotFolderConfig? folderConfig,
  }) : _folderConfig = folderConfig;

  /// 从JSON创建配置
  factory ScreenshotSettings.fromJson(Map<String, dynamic> json) {
    ScreenshotFolderConfig folderConfig;
    try {
      if (json['folderConfig'] != null) {
        final folderConfigData = json['folderConfig'];
        if (folderConfigData is Map<String, dynamic>) {
          folderConfig = ScreenshotFolderConfig.fromJson(folderConfigData);
        } else {
          LoggerService.warning(
              'Invalid folderConfig data type, using default');
          folderConfig = const ScreenshotFolderConfig();
        }
      } else {
        folderConfig = const ScreenshotFolderConfig();
      }
    } catch (error) {
      LoggerService.error(
          'Failed to parse folderConfig from JSON, using default', error);
      folderConfig = const ScreenshotFolderConfig();
    }

    return ScreenshotSettings(
      customSavePath: json['customSavePath'] as String?,
      enableCompression: json['enableCompression'] as bool? ?? false,
      compressionQuality: json['compressionQuality'] as int? ?? 90,
      imageFormat: json['imageFormat'] as String? ?? 'png',
      autoOpenFolder: json['autoOpenFolder'] as bool? ?? false,
      showPreview: json['showPreview'] as bool? ?? true,
      saveMetadata: json['saveMetadata'] as bool? ?? true,
      folderConfig: folderConfig,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'customSavePath': customSavePath,
      'enableCompression': enableCompression,
      'compressionQuality': compressionQuality,
      'imageFormat': imageFormat,
      'autoOpenFolder': autoOpenFolder,
      'showPreview': showPreview,
      'saveMetadata': saveMetadata,
      'folderConfig': folderConfig.toJson(),
    };
  }

  /// 创建配置副本
  ScreenshotSettings copyWith({
    String? customSavePath,
    bool? enableCompression,
    int? compressionQuality,
    String? imageFormat,
    bool? autoOpenFolder,
    bool? showPreview,
    bool? saveMetadata,
    ScreenshotFolderConfig? folderConfig,
  }) {
    return ScreenshotSettings(
      customSavePath: customSavePath ?? this.customSavePath,
      enableCompression: enableCompression ?? this.enableCompression,
      compressionQuality: compressionQuality ?? this.compressionQuality,
      imageFormat: imageFormat ?? this.imageFormat,
      autoOpenFolder: autoOpenFolder ?? this.autoOpenFolder,
      showPreview: showPreview ?? this.showPreview,
      saveMetadata: saveMetadata ?? this.saveMetadata,
      folderConfig: folderConfig ?? this.folderConfig,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScreenshotSettings &&
        other.customSavePath == customSavePath &&
        other.enableCompression == enableCompression &&
        other.compressionQuality == compressionQuality &&
        other.imageFormat == imageFormat &&
        other.autoOpenFolder == autoOpenFolder &&
        other.showPreview == showPreview &&
        other.saveMetadata == saveMetadata &&
        other.folderConfig == folderConfig;
  }

  @override
  int get hashCode {
    return Object.hash(
      customSavePath,
      enableCompression,
      compressionQuality,
      imageFormat,
      autoOpenFolder,
      showPreview,
      saveMetadata,
      folderConfig,
    );
  }

  @override
  String toString() {
    return 'ScreenshotSettings('
        'customSavePath: $customSavePath, '
        'enableCompression: $enableCompression, '
        'compressionQuality: $compressionQuality, '
        'imageFormat: $imageFormat, '
        'autoOpenFolder: $autoOpenFolder, '
        'showPreview: $showPreview, '
        'saveMetadata: $saveMetadata, '
        'folderConfig: ${folderConfig.toString()}'
        ')';
  }
}

/// 截图配置服务
/// 负责截图相关配置的持久化存储和管理
class ScreenshotConfigService {
  static final ScreenshotConfigService _instance =
      ScreenshotConfigService._internal();
  factory ScreenshotConfigService() => _instance;
  ScreenshotConfigService._internal();

  /// 获取单例实例
  static ScreenshotConfigService get instance => _instance;

  static const String _configKey = 'screenshot_settings';

  final StorageService _storageService = StorageService.instance;
  ScreenshotSettings _currentSettings = const ScreenshotSettings();
  bool _isInitialized = false;

  /// 获取当前截图设置
  ScreenshotSettings get currentSettings => _currentSettings;

  /// 初始化配置服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _storageService.initialize();
      await _loadSettings();
      _isInitialized = true;

      LoggerService.info(
          'ScreenshotConfigService initialized with settings: $_currentSettings');
    } catch (error) {
      LoggerService.error(
          'Failed to initialize ScreenshotConfigService', error);
      // 使用默认设置
      _currentSettings = const ScreenshotSettings();
      _isInitialized = true;
    }
  }

  /// 加载保存的设置
  Future<void> _loadSettings() async {
    try {
      final settingsMap = await _storageService.getMap(_configKey);

      if (settingsMap != null) {
        _currentSettings = ScreenshotSettings.fromJson(settingsMap);
        LoggerService.debug(
            'Loaded screenshot settings from storage: $settingsMap');
      } else {
        _currentSettings = const ScreenshotSettings();
        LoggerService.debug('No saved settings found, using defaults');
      }
    } catch (error) {
      LoggerService.error('Failed to load screenshot settings', error);
      _currentSettings = const ScreenshotSettings();
    }
  }

  /// 保存设置
  Future<bool> _saveSettings() async {
    try {
      final success =
          await _storageService.setMap(_configKey, _currentSettings.toJson());

      if (success) {
        LoggerService.debug(
            'Screenshot settings saved successfully: ${_currentSettings.toJson()}');
      } else {
        LoggerService.error('Failed to save screenshot settings');
      }

      return success;
    } catch (error) {
      LoggerService.error('Error saving screenshot settings', error);
      return false;
    }
  }

  /// 更新自定义保存路径
  Future<bool> updateCustomSavePath(String? path) async {
    await _ensureInitialized();

    final newSettings = _currentSettings.copyWith(customSavePath: path);
    return await _updateSettings(newSettings);
  }

  // === 多文件夹管理方法 ===

  /// 添加截图文件夹
  Future<bool> addScreenshotFolder({
    required String name,
    required String path,
    bool isEnabled = true,
    bool setAsDefault = false,
  }) async {
    await _ensureInitialized();

    // 生成唯一ID
    final id = _generateFolderId();

    final newFolder = ScreenshotFolderItem(
      id: id,
      name: name,
      path: path,
      isEnabled: isEnabled,
      isDefault: false, // 默认先设为false，然后通过setDefaultFolder设置
      createdAt: DateTime.now(),
    );

    var newFolderConfig = _getSafeFolderConfig().addFolder(newFolder);

    // 如果设置为默认文件夹
    if (setAsDefault) {
      newFolderConfig = newFolderConfig.setDefaultFolder(id);
    }

    final newSettings =
        _currentSettings.copyWith(folderConfig: newFolderConfig);
    final success = await _updateSettings(newSettings);

    if (success) {
      LoggerService.info('Added screenshot folder: $name at $path');
    }

    return success;
  }

  /// 更新截图文件夹
  Future<bool> updateScreenshotFolder({
    required String folderId,
    String? name,
    String? path,
    bool? isEnabled,
  }) async {
    await _ensureInitialized();

    final currentFolder = _getSafeFolderConfig()
        .folders
        .where((f) => f.id == folderId)
        .firstOrNull;

    if (currentFolder == null) {
      LoggerService.warning('Screenshot folder not found: $folderId');
      return false;
    }

    final updatedFolder = currentFolder.copyWith(
      name: name,
      path: path,
      isEnabled: isEnabled,
      lastUsedAt: DateTime.now(),
    );

    final newFolderConfig =
        _getSafeFolderConfig().updateFolder(folderId, updatedFolder);
    final newSettings =
        _currentSettings.copyWith(folderConfig: newFolderConfig);

    final success = await _updateSettings(newSettings);

    if (success) {
      LoggerService.info('Updated screenshot folder: $folderId');
    }

    return success;
  }

  /// 删除截图文件夹
  Future<bool> removeScreenshotFolder(String folderId) async {
    await _ensureInitialized();

    final currentFolder = _getSafeFolderConfig()
        .folders
        .where((f) => f.id == folderId)
        .firstOrNull;

    if (currentFolder == null) {
      LoggerService.warning('Screenshot folder not found: $folderId');
      return false;
    }

    final newFolderConfig = _getSafeFolderConfig().removeFolder(folderId);
    final newSettings =
        _currentSettings.copyWith(folderConfig: newFolderConfig);

    final success = await _updateSettings(newSettings);

    if (success) {
      LoggerService.info('Removed screenshot folder: $folderId');
    }

    return success;
  }

  /// 设置默认截图文件夹
  Future<bool> setDefaultScreenshotFolder(String folderId) async {
    await _ensureInitialized();

    final newFolderConfig = _getSafeFolderConfig().setDefaultFolder(folderId);
    final newSettings =
        _currentSettings.copyWith(folderConfig: newFolderConfig);

    final success = await _updateSettings(newSettings);

    if (success) {
      LoggerService.info('Set default screenshot folder: $folderId');
    }

    return success;
  }

  /// 更新截图保存策略
  Future<bool> updateSaveStrategy({
    required ScreenshotSaveStrategy strategy,
    String? selectedFolderId,
    int? rotationIntervalMinutes,
  }) async {
    await _ensureInitialized();

    final newFolderConfig = _getSafeFolderConfig().copyWith(
      saveStrategy: strategy,
      selectedFolderId: selectedFolderId,
      rotationIntervalMinutes: rotationIntervalMinutes,
    );

    final newSettings =
        _currentSettings.copyWith(folderConfig: newFolderConfig);

    final success = await _updateSettings(newSettings);

    if (success) {
      LoggerService.info('Updated save strategy: ${strategy.name}');
    }

    return success;
  }

  /// 获取截图目标文件夹列表
  List<ScreenshotFolderItem> getTargetFolders({DateTime? timestamp}) {
    final targetFolders =
        _getSafeFolderConfig().getTargetFolders(timestamp: timestamp);
    LoggerService.debug(
        'getTargetFolders: strategy=${_getSafeFolderConfig().saveStrategy}, count=${targetFolders.length}');
    return targetFolders;
  }

  /// 获取配置调试信息
  Map<String, dynamic> getConfigDebugInfo() {
    final config = _getSafeFolderConfig();
    return {
      'isInitialized': _isInitialized,
      'saveStrategy': config.saveStrategy.name,
      'totalFolders': config.folders.length,
      'enabledFolders': config.enabledFolders.length,
      'defaultFolder': config.defaultFolder?.name,
      'selectedFolderId': config.selectedFolderId,
      'folders': config.folders
          .map((f) => {
                'id': f.id,
                'name': f.name,
                'path': f.path,
                'isEnabled': f.isEnabled,
                'isDefault': f.isDefault,
              })
          .toList(),
    };
  }

  /// 获取所有截图文件夹
  List<ScreenshotFolderItem> getAllFolders() {
    return _getSafeFolderConfig().folders;
  }

  /// 获取启用的截图文件夹
  List<ScreenshotFolderItem> getEnabledFolders() {
    return _getSafeFolderConfig().enabledFolders;
  }

  /// 获取默认截图文件夹
  ScreenshotFolderItem? getDefaultFolder() {
    return _getSafeFolderConfig().defaultFolder;
  }

  /// 生成文件夹ID
  String _generateFolderId() {
    const chars =
        'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = math.Random();
    return String.fromCharCodes(Iterable.generate(
        8, (_) => chars.codeUnitAt(random.nextInt(chars.length))));
  }

  /// 更新压缩设置
  Future<bool> updateCompressionSettings({
    bool? enableCompression,
    int? compressionQuality,
  }) async {
    await _ensureInitialized();

    final newSettings = _currentSettings.copyWith(
      enableCompression: enableCompression,
      compressionQuality: compressionQuality,
    );
    return await _updateSettings(newSettings);
  }

  /// 更新图片格式
  Future<bool> updateImageFormat(String format) async {
    await _ensureInitialized();

    final newSettings = _currentSettings.copyWith(imageFormat: format);
    return await _updateSettings(newSettings);
  }

  /// 更新自动操作设置
  Future<bool> updateAutoSettings({
    bool? autoOpenFolder,
    bool? showPreview,
  }) async {
    await _ensureInitialized();

    final newSettings = _currentSettings.copyWith(
      autoOpenFolder: autoOpenFolder,
      showPreview: showPreview,
    );
    return await _updateSettings(newSettings);
  }

  /// 更新元数据设置
  Future<bool> updateMetadataSettings(bool saveMetadata) async {
    await _ensureInitialized();

    final newSettings = _currentSettings.copyWith(saveMetadata: saveMetadata);
    return await _updateSettings(newSettings);
  }

  /// 批量更新设置
  Future<bool> updateSettings(ScreenshotSettings settings) async {
    await _ensureInitialized();
    return await _updateSettings(settings);
  }

  /// 内部更新设置方法
  Future<bool> _updateSettings(ScreenshotSettings newSettings) async {
    if (_currentSettings == newSettings) {
      return true; // 设置没有变化
    }

    _currentSettings = newSettings;
    return await _saveSettings();
  }

  /// 重置为默认设置
  Future<bool> resetToDefaults() async {
    await _ensureInitialized();

    const defaultSettings = ScreenshotSettings();
    return await _updateSettings(defaultSettings);
  }

  /// 导出设置
  Future<Map<String, dynamic>> exportSettings() async {
    await _ensureInitialized();
    return _currentSettings.toJson();
  }

  /// 导入设置
  Future<bool> importSettings(Map<String, dynamic> settingsMap) async {
    await _ensureInitialized();

    try {
      final newSettings = ScreenshotSettings.fromJson(settingsMap);
      return await _updateSettings(newSettings);
    } catch (error) {
      LoggerService.error('Failed to import screenshot settings', error);
      return false;
    }
  }

  /// 清除所有设置
  Future<bool> clearSettings() async {
    await _ensureInitialized();

    try {
      final success = await _storageService.remove(_configKey);
      if (success) {
        _currentSettings = const ScreenshotSettings();
        LoggerService.info('Screenshot settings cleared successfully');
      }
      return success;
    } catch (error) {
      LoggerService.error('Failed to clear screenshot settings', error);
      return false;
    }
  }

  /// 确保服务已初始化
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// 安全获取folderConfig，确保不为null
  ScreenshotFolderConfig _getSafeFolderConfig() {
    try {
      final config = _currentSettings.folderConfig;
      if (config.folders.isEmpty) {
        LoggerService.debug('FolderConfig is empty, but valid');
      }
      return config;
    } catch (error) {
      LoggerService.error(
          'Failed to access folderConfig, using default', error);
      return const ScreenshotFolderConfig();
    }
  }

  /// 获取设置摘要
  Map<String, dynamic> getSettingsSummary() {
    return {
      'hasCustomPath': _currentSettings.customSavePath != null,
      'customPath': _currentSettings.customSavePath,
      'compressionEnabled': _currentSettings.enableCompression,
      'compressionQuality': _currentSettings.compressionQuality,
      'imageFormat': _currentSettings.imageFormat,
      'autoOpenFolder': _currentSettings.autoOpenFolder,
      'showPreview': _currentSettings.showPreview,
      'saveMetadata': _currentSettings.saveMetadata,
    };
  }
}
