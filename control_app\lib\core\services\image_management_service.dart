import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

import '../models/websocket_message.dart';
import '../models/image_info.dart';
import 'logger_service.dart';
import 'storage_service.dart';
import 'websocket_service.dart';
import 'image_compression_service.dart';

/// 图片分组信息
class ImageGroup {
  final String id;
  final String name;
  final String description;
  final List<String> imageIds;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic> metadata;

  const ImageGroup({
    required this.id,
    required this.name,
    this.description = '',
    this.imageIds = const [],
    required this.createdAt,
    required this.updatedAt,
    this.metadata = const {},
  });

  factory ImageGroup.fromJson(Map<String, dynamic> json) {
    return ImageGroup(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      imageIds: List<String>.from(json['imageIds'] as List? ?? []),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'imageIds': imageIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  ImageGroup copyWith({
    String? name,
    String? description,
    List<String>? imageIds,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return ImageGroup(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageIds: imageIds ?? this.imageIds,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      metadata: metadata ?? this.metadata,
    );
  }
}

/// 图片管理服务
class ImageManagementService {
  static ImageManagementService? _instance;
  static ImageManagementService get instance =>
      _instance ??= ImageManagementService._();

  ImageManagementService._();

  final Map<String, AppImageInfo> _images = {};
  final Map<String, ImageGroup> _groups = {};
  final StreamController<List<AppImageInfo>> _imagesController =
      StreamController<List<AppImageInfo>>.broadcast();
  final StreamController<List<ImageGroup>> _groupsController =
      StreamController<List<ImageGroup>>.broadcast();

  /// 图片列表流
  Stream<List<AppImageInfo>> get imagesStream => _imagesController.stream;

  /// 分组列表流
  Stream<List<ImageGroup>> get groupsStream => _groupsController.stream;

  /// 获取所有图片
  List<AppImageInfo> get images => _images.values.toList();

  /// 获取所有图片（方法形式）
  List<AppImageInfo> getAllImages() {
    return _images.values.toList();
  }

  /// 获取所有分组
  List<ImageGroup> get groups => _groups.values.toList();

  /// 根据分组ID获取图片列表
  List<AppImageInfo> getImagesByGroup(String groupId) {
    final group = _groups[groupId];
    if (group == null) {
      LoggerService.warning('Group not found: $groupId');
      return [];
    }

    return group.imageIds
        .map((imageId) => _images[imageId])
        .where((image) => image != null)
        .cast<AppImageInfo>()
        .toList();
  }

  /// 搜索图片
  List<AppImageInfo> searchImages({
    String? nameContains,
    List<String>? tagsContain,
    String? deviceId,
    DateTime? createdAfter,
    DateTime? createdBefore,
  }) {
    return _images.values.where((image) {
      // 按名称搜索
      if (nameContains != null && nameContains.isNotEmpty) {
        if (!image.name.toLowerCase().contains(nameContains.toLowerCase())) {
          return false;
        }
      }

      // 按标签搜索
      if (tagsContain != null && tagsContain.isNotEmpty) {
        final imageTags = image.metadata['tags'] as List<String>? ?? [];
        final hasMatchingTag = tagsContain.any((tag) => imageTags.any(
            (imageTag) => imageTag.toLowerCase().contains(tag.toLowerCase())));
        if (!hasMatchingTag) {
          return false;
        }
      }

      // 按设备ID搜索
      if (deviceId != null && deviceId.isNotEmpty) {
        final sourceDevice = image.metadata['sourceDevice'] as String?;
        if (sourceDevice != deviceId) {
          return false;
        }
      }

      // 按创建时间搜索
      if (createdAfter != null && image.createdAt.isBefore(createdAfter)) {
        return false;
      }

      if (createdBefore != null && image.createdAt.isAfter(createdBefore)) {
        return false;
      }

      return true;
    }).toList();
  }

  /// 初始化图片管理服务
  Future<void> initialize() async {
    try {
      // 创建图片存储目录
      await _ensureImageDirectories();

      // 加载本地图片数据
      await _loadLocalImages();
      await _loadLocalGroups();

      // 监听WebSocket消息
      _listenToWebSocketMessages();

      LoggerService.info('Image management service initialized');
    } catch (error) {
      LoggerService.error(
          'Failed to initialize image management service', error);
      rethrow;
    }
  }

  /// 监听WebSocket消息
  void _listenToWebSocketMessages() {
    WebSocketService.instance.messageStream.listen((message) {
      if (message.isImageMessage) {
        _handleImageMessage(message);
      }
    });
  }

  /// 处理图片相关消息
  Future<void> _handleImageMessage(WebSocketMessage message) async {
    try {
      switch (message.action) {
        case WebSocketMessageAction.imageReceived:
          await _handleImageReceived(message);
          break;
        case WebSocketMessageAction.imageRequest:
          await _handleImageRequest(message);
          break;
        case WebSocketMessageAction.imageSync:
          await _handleImageSync(message);
          break;
        default:
          LoggerService.warning('Unknown image action: ${message.action}');
      }
    } catch (error) {
      LoggerService.error('Failed to handle image message', error);
    }
  }

  /// 处理接收到的图片
  Future<void> _handleImageReceived(WebSocketMessage message) async {
    final payload = message.payload ?? {};
    final imageData = payload['imageData'] as String?;
    final metadata = payload['metadata'] as Map<String, dynamic>? ?? {};

    if (imageData != null) {
      final bytes = base64Decode(imageData);
      await addImageFromBytes(
        bytes,
        metadata: metadata,
        deviceId: message.deviceId,
      );
    }
  }

  /// 处理图片请求
  Future<void> _handleImageRequest(WebSocketMessage message) async {
    final payload = message.payload ?? {};
    final imageId = payload['imageId'] as String?;

    if (imageId != null &&
        _images.containsKey(imageId) &&
        message.deviceId != null) {
      final imageInfo = _images[imageId]!;
      await _sendImageToDevice(imageInfo, message.deviceId!);
    }
  }

  /// 处理图片同步
  Future<void> _handleImageSync(WebSocketMessage message) async {
    final payload = message.payload ?? {};
    final groupId = payload['groupId'] as String?;

    if (groupId != null &&
        _groups.containsKey(groupId) &&
        message.deviceId != null) {
      final group = _groups[groupId]!;
      await syncGroupToDevices(group.id, [message.deviceId!]);
    }
  }

  /// 添加图片（通用方法）
  Future<AppImageInfo> addImage(String fileName, Uint8List bytes) async {
    return await addImageFromBytes(
      bytes,
      name: fileName,
      metadata: {},
    );
  }

  /// 从字节数据添加图片
  Future<AppImageInfo> addImageFromBytes(
    Uint8List bytes, {
    String? name,
    Map<String, dynamic> metadata = const {},
    String? deviceId,
  }) async {
    try {
      // 生成图片ID和哈希
      final hash = sha256.convert(bytes).toString();
      final imageId =
          'img_${DateTime.now().millisecondsSinceEpoch}_${hash.substring(0, 8)}';

      // 检查是否已存在相同图片
      final existingImage = _images.values.firstWhere(
        (img) => img.hash == hash,
        orElse: () => AppImageInfo.empty(),
      );

      if (existingImage.id.isNotEmpty) {
        LoggerService.info('Image already exists: ${existingImage.id}');
        return existingImage;
      }

      // 保存图片文件
      final filePath = await _saveImageFile(imageId, bytes);

      // 生成缩略图
      final thumbnailPath = await _generateThumbnail(imageId, bytes);

      // 创建图片信息
      final imageInfo = AppImageInfo(
        id: imageId,
        name: name ?? 'Image_${DateTime.now().millisecondsSinceEpoch}',
        filePath: filePath,
        thumbnailPath: thumbnailPath,
        size: bytes.length,
        hash: hash,
        createdAt: DateTime.now(),
        metadata: {
          ...metadata,
          if (deviceId != null) 'sourceDevice': deviceId,
        },
      );

      // 添加到内存缓存
      _images[imageId] = imageInfo;

      // 保存到本地存储
      await _saveImageInfo(imageInfo);

      // 通知更新
      _notifyImagesChanged();

      LoggerService.info('Image added: $imageId, size: ${bytes.length} bytes');
      return imageInfo;
    } catch (error) {
      LoggerService.error('Failed to add image from bytes', error);
      rethrow;
    }
  }

  /// 从文件添加图片
  Future<AppImageInfo> addImageFromFile(
    File file, {
    Map<String, dynamic> metadata = const {},
    bool enableCompression = true,
  }) async {
    final originalBytes = await file.readAsBytes();
    final name = file.path.split('/').last.split('\\').last;
    Uint8List bytes = originalBytes;

    // 压缩图片（如果启用）
    if (enableCompression) {
      try {
        final compressionResult =
            await ImageCompressionService.instance.compressFile(
          file.path,
          config: ImageCompressionService.getRecommendedConfig(
            imageWidth: 0, // 将在压缩服务中自动检测
            imageHeight: 0,
            fileSize: originalBytes.length,
            purpose: 'storage',
          ),
        );
        bytes = compressionResult.data;

        LoggerService.info('Image compressed: '
            '${compressionResult.originalSize} -> ${compressionResult.compressedSize} bytes '
            '(${compressionResult.compressionPercentage.toStringAsFixed(1)}% reduction)');
      } catch (error) {
        LoggerService.warning(
            'Failed to compress image, using original', error);
      }
    }

    return await addImageFromBytes(
      bytes,
      name: name,
      metadata: metadata,
    );
  }

  /// 更新图片信息
  Future<AppImageInfo?> updateImage(
    String imageId, {
    String? name,
    Map<String, dynamic>? metadata,
    String? deviceId,
  }) async {
    try {
      final imageInfo = _images[imageId];
      if (imageInfo == null) {
        LoggerService.warning('Image not found: $imageId');
        return null;
      }

      final updatedImageInfo = imageInfo.copyWith(
        name: name,
        metadata:
            metadata != null ? {...imageInfo.metadata, ...metadata} : null,
        updatedAt: DateTime.now(),
      );

      _images[imageId] = updatedImageInfo;
      await _saveImageInfo(updatedImageInfo);

      _notifyImagesChanged();

      LoggerService.info('Image updated: $imageId');
      return updatedImageInfo;
    } catch (error) {
      LoggerService.error('Failed to update image: $imageId', error);
      return null;
    }
  }

  /// 移除图片（别名方法）
  Future<bool> removeImage(String imageId) async {
    return await deleteImage(imageId);
  }

  /// 重命名图片
  Future<bool> renameImage(String imageId, String newName) async {
    try {
      final imageInfo = _images[imageId];
      if (imageInfo == null) {
        LoggerService.warning('Image not found: $imageId');
        return false;
      }

      // 更新图片信息
      final updatedInfo = imageInfo.copyWith(
        name: newName,
        updatedAt: DateTime.now(),
      );

      _images[imageId] = updatedInfo;

      // 保存到本地存储
      await _saveImageInfo(updatedInfo);

      // 通知更新
      _notifyImagesChanged();

      LoggerService.info('Image renamed: $imageId -> $newName');
      return true;
    } catch (e) {
      LoggerService.error('Failed to rename image: $e');
      return false;
    }
  }

  /// 生成缩略图
  Future<String?> generateThumbnail(String imageId) async {
    try {
      final imageInfo = _images[imageId];
      if (imageInfo == null) {
        LoggerService.warning('Image not found: $imageId');
        return null;
      }

      // 如果已有缩略图，直接返回
      if (imageInfo.thumbnailPath.isNotEmpty) {
        final thumbnailFile = File(imageInfo.thumbnailPath);
        if (await thumbnailFile.exists()) {
          return imageInfo.thumbnailPath;
        }
      }

      // 读取原图数据
      final imageFile = File(imageInfo.filePath);
      if (!await imageFile.exists()) {
        LoggerService.warning('Image file not found: ${imageInfo.filePath}');
        return null;
      }

      final bytes = await imageFile.readAsBytes();
      final thumbnailPath = await _generateThumbnail(imageId, bytes);

      // 更新图片信息
      final updatedImageInfo = imageInfo.copyWith(
        thumbnailPath: thumbnailPath,
        updatedAt: DateTime.now(),
      );

      _images[imageId] = updatedImageInfo;
      await _saveImageInfo(updatedImageInfo);

      LoggerService.info('Thumbnail generated for image: $imageId');
      return thumbnailPath;
    } catch (error) {
      LoggerService.error(
          'Failed to generate thumbnail for image: $imageId', error);
      return null;
    }
  }

  /// 删除图片
  Future<bool> deleteImage(String imageId) async {
    try {
      final imageInfo = _images[imageId];
      if (imageInfo == null) {
        LoggerService.warning('Image not found: $imageId');
        return false;
      }

      // 删除文件
      await _deleteImageFiles(imageInfo);

      // 从分组中移除
      for (final group in _groups.values) {
        if (group.imageIds.contains(imageId)) {
          await updateGroup(
            group.id,
            imageIds: group.imageIds.where((id) => id != imageId).toList(),
          );
        }
      }

      // 从内存中移除
      _images.remove(imageId);

      // 删除本地存储
      await _deleteImageInfo(imageId);

      // 通知更新
      _notifyImagesChanged();

      LoggerService.info('Image deleted: $imageId');
      return true;
    } catch (error) {
      LoggerService.error('Failed to delete image: $imageId', error);
      return false;
    }
  }

  /// 创建图片分组
  Future<ImageGroup> createGroup(
    String name, {
    String description = '',
    List<String> imageIds = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final groupId = 'group_${DateTime.now().millisecondsSinceEpoch}';
      final now = DateTime.now();

      final group = ImageGroup(
        id: groupId,
        name: name,
        description: description,
        imageIds: imageIds,
        createdAt: now,
        updatedAt: now,
        metadata: metadata,
      );

      _groups[groupId] = group;
      await _saveGroupInfo(group);

      _notifyGroupsChanged();

      LoggerService.info('Group created: $groupId');
      return group;
    } catch (error) {
      LoggerService.error('Failed to create group', error);
      rethrow;
    }
  }

  /// 更新图片分组
  Future<ImageGroup?> updateGroup(
    String groupId, {
    String? name,
    String? description,
    List<String>? imageIds,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      final group = _groups[groupId];
      if (group == null) {
        LoggerService.warning('Group not found: $groupId');
        return null;
      }

      final updatedGroup = group.copyWith(
        name: name,
        description: description,
        imageIds: imageIds,
        metadata: metadata,
      );

      _groups[groupId] = updatedGroup;
      await _saveGroupInfo(updatedGroup);

      _notifyGroupsChanged();

      LoggerService.info('Group updated: $groupId');
      return updatedGroup;
    } catch (error) {
      LoggerService.error('Failed to update group: $groupId', error);
      return null;
    }
  }

  /// 删除图片分组
  Future<bool> deleteGroup(String groupId) async {
    try {
      if (!_groups.containsKey(groupId)) {
        LoggerService.warning('Group not found: $groupId');
        return false;
      }

      _groups.remove(groupId);
      await _deleteGroupInfo(groupId);

      _notifyGroupsChanged();

      LoggerService.info('Group deleted: $groupId');
      return true;
    } catch (error) {
      LoggerService.error('Failed to delete group: $groupId', error);
      return false;
    }
  }

  /// 同步分组到设备
  Future<void> syncGroupToDevices(
      String groupId, List<String> deviceIds) async {
    try {
      final group = _groups[groupId];
      if (group == null) {
        LoggerService.warning('Group not found: $groupId');
        return;
      }

      for (final deviceId in deviceIds) {
        for (final imageId in group.imageIds) {
          final imageInfo = _images[imageId];
          if (imageInfo != null) {
            await _sendImageToDevice(imageInfo, deviceId);
          }
        }
      }

      LoggerService.info('Group synced to devices: $groupId -> $deviceIds');
    } catch (error) {
      LoggerService.error('Failed to sync group to devices', error);
    }
  }

  /// 发送图片到设备
  Future<void> _sendImageToDevice(
      AppImageInfo imageInfo, String deviceId) async {
    try {
      final file = File(imageInfo.filePath);
      if (!await file.exists()) {
        LoggerService.warning('Image file not found: ${imageInfo.filePath}');
        return;
      }

      final bytes = await file.readAsBytes();
      final imageData = base64Encode(bytes);

      final message = WebSocketMessage.imageSync(
        deviceId: deviceId,
        imageId: imageInfo.id,
        imageData: imageData,
        metadata: imageInfo.metadata,
      );

      WebSocketService.instance.sendMessage(message);
      LoggerService.debug('Image message sent to WebSocket');

      LoggerService.info('Image sent to device: ${imageInfo.id} -> $deviceId');
    } catch (error) {
      LoggerService.error('Failed to send image to device', error);
    }
  }

  /// 保存图片文件
  Future<String> _saveImageFile(String imageId, Uint8List bytes) async {
    final directory = await _getImagesDirectory();
    final filePath = '${directory.path}/$imageId.png';

    final file = File(filePath);
    await file.writeAsBytes(bytes);

    return filePath;
  }

  /// 生成缩略图
  Future<String> _generateThumbnail(String imageId, Uint8List bytes) async {
    try {
      final directory = await _getThumbnailsDirectory();
      final thumbnailPath = '${directory.path}/${imageId}_thumb.jpg';

      // 压缩图片作为缩略图
      final compressedBytes = await FlutterImageCompress.compressWithList(
        bytes,
        minHeight: 200,
        minWidth: 200,
        quality: 70,
      );

      final file = File(thumbnailPath);
      await file.writeAsBytes(compressedBytes);

      return thumbnailPath;
    } catch (error) {
      LoggerService.error('Failed to generate thumbnail', error);
      return '';
    }
  }

  /// 删除图片文件
  Future<void> _deleteImageFiles(AppImageInfo imageInfo) async {
    try {
      // 删除原图
      final imageFile = File(imageInfo.filePath);
      if (await imageFile.exists()) {
        await imageFile.delete();
      }

      // 删除缩略图
      if (imageInfo.thumbnailPath.isNotEmpty) {
        final thumbnailFile = File(imageInfo.thumbnailPath);
        if (await thumbnailFile.exists()) {
          await thumbnailFile.delete();
        }
      }
    } catch (error) {
      LoggerService.error('Failed to delete image files', error);
    }
  }

  /// 确保图片目录存在
  Future<void> _ensureImageDirectories() async {
    final imagesDir = await _getImagesDirectory();
    final thumbnailsDir = await _getThumbnailsDirectory();

    if (!await imagesDir.exists()) {
      await imagesDir.create(recursive: true);
    }

    if (!await thumbnailsDir.exists()) {
      await thumbnailsDir.create(recursive: true);
    }
  }

  /// 获取图片目录
  Future<Directory> _getImagesDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory('${appDir.path}/images');
  }

  /// 获取缩略图目录
  Future<Directory> _getThumbnailsDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory('${appDir.path}/thumbnails');
  }

  /// 加载本地图片数据
  Future<void> _loadLocalImages() async {
    try {
      final imagesList =
          await StorageService.instance.getStringList('images') ?? [];

      for (final imageJson in imagesList) {
        try {
          final imageData = jsonDecode(imageJson) as Map<String, dynamic>;
          final imageInfo = AppImageInfo.fromJson(imageData);
          _images[imageInfo.id] = imageInfo;
        } catch (error) {
          LoggerService.error('Failed to parse image data', error);
        }
      }

      _notifyImagesChanged();
      LoggerService.info('Loaded ${_images.length} images from local storage');
    } catch (error) {
      LoggerService.error('Failed to load local images', error);
    }
  }

  /// 加载本地分组数据
  Future<void> _loadLocalGroups() async {
    try {
      final groupsList =
          await StorageService.instance.getStringList('groups') ?? [];

      for (final groupJson in groupsList) {
        try {
          final groupData = jsonDecode(groupJson) as Map<String, dynamic>;
          final group = ImageGroup.fromJson(groupData);
          _groups[group.id] = group;
        } catch (error) {
          LoggerService.error('Failed to parse group data', error);
        }
      }

      _notifyGroupsChanged();
      LoggerService.info('Loaded ${_groups.length} groups from local storage');
    } catch (error) {
      LoggerService.error('Failed to load local groups', error);
    }
  }

  /// 保存图片信息
  Future<void> _saveImageInfo(AppImageInfo imageInfo) async {
    try {
      final imagesList =
          await StorageService.instance.getStringList('images') ?? [];

      // 移除旧数据
      imagesList.removeWhere((json) {
        try {
          final data = jsonDecode(json) as Map<String, dynamic>;
          return data['id'] == imageInfo.id;
        } catch (_) {
          return false;
        }
      });

      // 添加新数据
      imagesList.add(jsonEncode(imageInfo.toJson()));

      await StorageService.instance.setStringList('images', imagesList);
    } catch (error) {
      LoggerService.error('Failed to save image info', error);
    }
  }

  /// 删除图片信息
  Future<void> _deleteImageInfo(String imageId) async {
    try {
      final imagesList =
          await StorageService.instance.getStringList('images') ?? [];

      imagesList.removeWhere((json) {
        try {
          final data = jsonDecode(json) as Map<String, dynamic>;
          return data['id'] == imageId;
        } catch (_) {
          return false;
        }
      });

      await StorageService.instance.setStringList('images', imagesList);
    } catch (error) {
      LoggerService.error('Failed to delete image info', error);
    }
  }

  /// 保存分组信息
  Future<void> _saveGroupInfo(ImageGroup group) async {
    try {
      final groupsList =
          await StorageService.instance.getStringList('groups') ?? [];

      // 移除旧数据
      groupsList.removeWhere((json) {
        try {
          final data = jsonDecode(json) as Map<String, dynamic>;
          return data['id'] == group.id;
        } catch (_) {
          return false;
        }
      });

      // 添加新数据
      groupsList.add(jsonEncode(group.toJson()));

      await StorageService.instance.setStringList('groups', groupsList);
    } catch (error) {
      LoggerService.error('Failed to save group info', error);
    }
  }

  /// 删除分组信息
  Future<void> _deleteGroupInfo(String groupId) async {
    try {
      final groupsList =
          await StorageService.instance.getStringList('groups') ?? [];

      groupsList.removeWhere((json) {
        try {
          final data = jsonDecode(json) as Map<String, dynamic>;
          return data['id'] == groupId;
        } catch (_) {
          return false;
        }
      });

      await StorageService.instance.setStringList('groups', groupsList);
    } catch (error) {
      LoggerService.error('Failed to delete group info', error);
    }
  }

  /// 通知图片列表变化
  void _notifyImagesChanged() {
    _imagesController.add(images);
  }

  /// 通知分组列表变化
  void _notifyGroupsChanged() {
    _groupsController.add(groups);
  }

  /// 释放资源
  void dispose() {
    _imagesController.close();
    _groupsController.close();
  }
}

/// 图片管理服务提供者
final imageManagementServiceProvider = Provider<ImageManagementService>((ref) {
  return ImageManagementService.instance;
});

/// 图片列表流提供者
final imagesStreamProvider = StreamProvider<List<AppImageInfo>>((ref) {
  final service = ref.watch(imageManagementServiceProvider);
  return service.imagesStream;
});

/// 分组列表流提供者
final groupsStreamProvider = StreamProvider<List<ImageGroup>>((ref) {
  final service = ref.watch(imageManagementServiceProvider);
  return service.groupsStream;
});
