import 'dart:convert';
import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../../../../core/services/storage_service.dart';
import '../../../../core/services/logger_service.dart';

/// 认证仓库实现类
class AuthRepositoryImpl implements AuthRepository {
  final String _baseUrl;
  final StorageService _storageService;

  static const String _userKey = 'current_user';
  static const String _tokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';

  AuthRepositoryImpl({
    String? baseUrl,
    StorageService? storageService,
  })  : _baseUrl = baseUrl ?? 'http://localhost:3001',
        _storageService = storageService ?? StorageService.instance;

  @override
  Future<User> login(String username, String password) async {
    try {
      LoggerService.info('Attempting login for user: $username');

      // 模拟网络请求延迟
      await Future.delayed(const Duration(seconds: 1));

      // 模拟登录验证（用于开发测试）
      if (username == 'terminal' && password == 'terminal123') {
        final user = User(
          id: 'terminal_user_1',
          username: 'terminal',
          email: '<EMAIL>',
          displayName: '终端用户',
          roles: ['terminal'],
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          lastLoginAt: DateTime.now(),
        );

        final token = 'terminal_token_${DateTime.now().millisecondsSinceEpoch}';
        final refreshToken =
            'terminal_refresh_${DateTime.now().millisecondsSinceEpoch}';

        // 保存用户信息和令牌
        await _storageService.setMap(_userKey, user.toJson());
        await _storageService.setString(_tokenKey, token);
        await _storageService.setString(_refreshTokenKey, refreshToken);

        LoggerService.info('Login successful for terminal user');
        return user;
      } else if (username == 'admin' && password == 'admin123') {
        final user = User(
          id: 'admin_user_1',
          username: 'admin',
          email: '<EMAIL>',
          displayName: '管理员',
          roles: ['admin'],
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 60)),
          lastLoginAt: DateTime.now(),
        );

        final token = 'admin_token_${DateTime.now().millisecondsSinceEpoch}';
        final refreshToken =
            'admin_refresh_${DateTime.now().millisecondsSinceEpoch}';

        // 保存用户信息和令牌
        await _storageService.setMap(_userKey, user.toJson());
        await _storageService.setString(_tokenKey, token);
        await _storageService.setString(_refreshTokenKey, refreshToken);

        LoggerService.info('Login successful for admin user');
        return user;
      }

      throw Exception('用户名或密码错误');
    } catch (e) {
      LoggerService.error('Login failed', e);
      rethrow;
    }
  }

  @override
  Future<User> register(String username, String password,
      {String? email}) async {
    try {
      LoggerService.info('Attempting registration for user: $username');

      // 模拟网络请求延迟
      await Future.delayed(const Duration(seconds: 1));

      // 模拟注册验证（用于开发测试）
      if (username.isNotEmpty && password.length >= 6) {
        final user = User(
          id: 'user_${DateTime.now().millisecondsSinceEpoch}',
          username: username,
          email: email ?? '$<EMAIL>',
          displayName: username,
          roles: ['user'],
          isActive: true,
          createdAt: DateTime.now(),
          lastLoginAt: DateTime.now(),
        );

        final token = 'token_${DateTime.now().millisecondsSinceEpoch}';
        final refreshToken = 'refresh_${DateTime.now().millisecondsSinceEpoch}';

        // 保存用户信息和令牌
        await _storageService.setMap(_userKey, user.toJson());
        await _storageService.setString(_tokenKey, token);
        await _storageService.setString(_refreshTokenKey, refreshToken);

        LoggerService.info('Registration successful for user: $username');
        return user;
      } else {
        throw Exception('用户名不能为空且密码长度至少6位');
      }
    } catch (e) {
      LoggerService.error('Registration failed for user: $username', e);
      rethrow;
    }
  }

  @override
  Future<void> logout() async {
    try {
      LoggerService.info('Logging out user');

      // 清除本地存储的用户信息
      await _storageService.remove(_userKey);
      await _storageService.remove(_tokenKey);
      await _storageService.remove(_refreshTokenKey);

      LoggerService.info('Logout successful');
    } catch (e) {
      LoggerService.error('Logout failed', e);
      rethrow;
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    try {
      final userJson = await _storageService.getMap(_userKey);
      if (userJson != null) {
        return User.fromJson(userJson);
      }
      return null;
    } catch (e) {
      LoggerService.error('Failed to get current user', e);
      return null;
    }
  }

  @override
  Future<bool> isLoggedIn() async {
    try {
      final token = await _storageService.getString(_tokenKey);
      final user = await getCurrentUser();
      return token != null && user != null;
    } catch (e) {
      LoggerService.error('Failed to check login status', e);
      return false;
    }
  }

  @override
  Future<User> refreshToken() async {
    try {
      LoggerService.info('Refreshing token');

      final refreshToken = await _storageService.getString(_refreshTokenKey);
      if (refreshToken == null) {
        throw Exception('No refresh token available');
      }

      // 模拟刷新令牌请求
      await Future.delayed(const Duration(milliseconds: 500));

      final newToken =
          'refreshed_token_${DateTime.now().millisecondsSinceEpoch}';
      await _storageService.setString(_tokenKey, newToken);

      final user = await getCurrentUser();
      if (user == null) {
        throw Exception('User not found after token refresh');
      }

      LoggerService.info('Token refreshed successfully');
      return user;
    } catch (e) {
      LoggerService.error('Token refresh failed', e);
      rethrow;
    }
  }

  @override
  Future<bool> validateToken(String token) async {
    try {
      // 模拟令牌验证
      await Future.delayed(const Duration(milliseconds: 200));

      // 简单的令牌格式验证
      return token.contains('token_') || token.contains('refresh_');
    } catch (e) {
      LoggerService.error('Token validation failed', e);
      return false;
    }
  }

  @override
  Future<String?> getAccessToken() async {
    try {
      return await _storageService.getString(_tokenKey);
    } catch (e) {
      LoggerService.error('Failed to get access token', e);
      return null;
    }
  }

  @override
  Future<String?> getRefreshToken() async {
    try {
      return await _storageService.getString(_refreshTokenKey);
    } catch (e) {
      LoggerService.error('Failed to get refresh token', e);
      return null;
    }
  }

  @override
  Future<void> clearAuthData() async {
    try {
      LoggerService.info('Clearing authentication data');

      await _storageService.remove(_userKey);
      await _storageService.remove(_tokenKey);
      await _storageService.remove(_refreshTokenKey);

      LoggerService.info('Authentication data cleared');
    } catch (e) {
      LoggerService.error('Failed to clear auth data', e);
      rethrow;
    }
  }

  @override
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      LoggerService.info('Changing password');

      final token = await getAccessToken();
      if (token == null) {
        throw Exception('No access token available');
      }

      // 模拟密码修改请求
      await Future.delayed(const Duration(seconds: 1));

      // 在实际实现中，这里会调用后端API
      // final response = await http.post(
      //   Uri.parse('$_baseUrl/api/auth/change-password'),
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'Authorization': 'Bearer $token',
      //   },
      //   body: jsonEncode({
      //     'oldPassword': oldPassword,
      //     'newPassword': newPassword,
      //   }),
      // );
      //
      // return response.statusCode == 200;

      // 模拟成功
      LoggerService.info('Password changed successfully');
      return true;
    } catch (e) {
      LoggerService.error('Password change failed', e);
      return false;
    }
  }

  @override
  Future<User> updateProfile({
    String? username,
    String? email,
    String? avatar,
  }) async {
    try {
      LoggerService.info('Updating user profile');

      // 获取当前用户
      final currentUser = await getCurrentUser();
      if (currentUser == null) {
        throw Exception('No user logged in');
      }

      // 创建更新后的用户对象
      final updatedUser = User(
        id: currentUser.id,
        username: username ?? currentUser.username,
        email: email ?? currentUser.email,
        displayName: currentUser.displayName,
        avatar: avatar ?? currentUser.avatar,
        createdAt: currentUser.createdAt,
        lastLoginAt: currentUser.lastLoginAt,
        isActive: currentUser.isActive,
        roles: currentUser.roles,
      );

      // 保存到本地存储
      await _storageService.setString(
          _userKey, jsonEncode(updatedUser.toJson()));

      LoggerService.info('Profile updated successfully');
      return updatedUser;
    } catch (e) {
      LoggerService.error('Profile update failed', e);
      rethrow;
    }
  }
}
