import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Import routes
import deviceRoutes from './routes/devices';
import imageRoutes from './routes/images';
import authRoutes from './routes/auth';

// Import services
import { DeviceDiscoveryService } from './services/DeviceDiscoveryService';
import { WebSocketService } from './services/WebSocketService';
import { databaseService } from './services/DatabaseService';

// Load environment variables
dotenv.config();

const app = express();
const server = createServer(app);
const io = new SocketIOServer(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  },
  pingTimeout: 60000,
  pingInterval: 25000
});

// Configuration
const PORT = process.env.PORT || 8080;
const WS_PORT = process.env.WS_PORT || 8080;
const UPLOAD_DIR = process.env.UPLOAD_DIR || './uploads';

// Ensure upload directories exist
const ensureDirectories = () => {
  const dirs = [
    path.join(process.cwd(), 'uploads', 'images'),
    path.join(process.cwd(), 'uploads', 'thumbnails'),
    path.join(process.cwd(), 'logs'),
    path.join(process.cwd(), 'data')
  ];
  
  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    }
  });
};

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});

// Middleware
app.use(helmet());
app.use(cors());
app.use(limiter);
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Static file serving
app.use('/images', express.static(path.join(process.cwd(), 'uploads', 'images')));
app.use('/thumbnails', express.static(path.join(process.cwd(), 'uploads', 'thumbnails')));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    services: {
      database: databaseService.isConnected(),
      websocket: WebSocketService.isRunning(),
      discovery: DeviceDiscoveryService.isRunning()
    }
  });
});

// Root route - API documentation and service status
app.get('/', (req, res) => {
  res.json({
    name: 'Flutter Image Control Gateway',
    version: '1.0.0',
    description: 'Multi-device screenshot capture and display control system',
    status: 'running',
    timestamp: new Date().toISOString(),
    endpoints: {
      health: '/api/health',
      auth: '/api/auth',
      devices: '/api/devices',
      images: '/api/images'
    },
    services: {
      database: databaseService.isConnected(),
      websocket: WebSocketService.isRunning(),
      discovery: DeviceDiscoveryService.isRunning()
    },
    features: [
      '🖼️ Multi-device screenshot capture',
      '🔄 Real-time image synchronization', 
      '📱 Flutter mobile control interface',
      '🌐 WebSocket-based communication'
    ]
  });
});

// API Routes
app.use('/api/auth', authRoutes);
app.use('/api/devices', deviceRoutes);
app.use('/api/images', imageRoutes);

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(err.status || 500).json({
    error: {
      message: err.message || 'Internal Server Error',
      status: err.status || 500
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: {
      message: 'Route not found',
      status: 404
    }
  });
});

// Initialize services
const initializeServices = async () => {
  try {
    console.log('Initializing Flutter ImgCT Gateway Service...');
    
    // Ensure directories exist
    ensureDirectories();
    
    // Initialize database
    await databaseService.initialize();
    console.log('✓ Database service initialized');
    
    // Initialize WebSocket service
    const webSocketService = WebSocketService.getInstance();
    webSocketService.initialize(io);
    console.log('✓ WebSocket service initialized');
    
    // Initialize device discovery service
    await DeviceDiscoveryService.initialize();
    console.log('✓ Device discovery service initialized');
    
    console.log('All services initialized successfully!');
  } catch (error) {
    console.error('Failed to initialize services:', error);
    process.exit(1);
  }
};

// Start server
const startServer = async () => {
  await initializeServices();
  
  server.listen(PORT, () => {
    console.log(`\n🚀 Flutter ImgCT Gateway Server running on port ${PORT}`);
    console.log(`📡 WebSocket server running on port ${WS_PORT}`);
    console.log(`🔍 Device discovery service active`);
    console.log(`📁 Upload directory: ${UPLOAD_DIR}`);
    console.log(`🌐 Health check: http://localhost:${PORT}/api/health\n`);
  });
};

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    databaseService.close();
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully...');
  server.close(() => {
    console.log('Server closed');
    databaseService.close();
    process.exit(0);
  });
});

// Start the server
if (import.meta.url === `file://${process.argv[1]}`) {
  startServer().catch(console.error);
}

export default app;
export { io, server };