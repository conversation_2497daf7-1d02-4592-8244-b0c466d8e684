import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';

import 'logger_service.dart';

/// 截图日志条目
class ScreenshotLogEntry {
  final String id;
  final DateTime timestamp;
  final String action;
  final String? filePath;
  final bool success;
  final String? error;
  final Map<String, dynamic> metadata;

  const ScreenshotLogEntry({
    required this.id,
    required this.timestamp,
    required this.action,
    this.filePath,
    required this.success,
    this.error,
    this.metadata = const {},
  });

  factory ScreenshotLogEntry.fromJson(Map<String, dynamic> json) {
    return ScreenshotLogEntry(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      action: json['action'] as String,
      filePath: json['filePath'] as String?,
      success: json['success'] as bool,
      error: json['error'] as String?,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'action': action,
      'filePath': filePath,
      'success': success,
      'error': error,
      'metadata': metadata,
    };
  }

  /// 获取格式化的时间字符串
  String get formattedTime {
    return DateFormat('HH:mm:ss').format(timestamp);
  }

  /// 获取格式化的日期字符串
  String get formattedDate {
    return DateFormat('yyyy-MM-dd').format(timestamp);
  }

  /// 获取状态图标
  String get statusIcon {
    return success ? '✅' : '❌';
  }

  /// 获取状态文字
  String get statusText {
    return success ? '成功' : '失败';
  }
}

/// 截图日志服务
class ScreenshotLogService {
  static ScreenshotLogService? _instance;
  static ScreenshotLogService get instance =>
      _instance ??= ScreenshotLogService._();

  ScreenshotLogService._();

  Directory? _logDirectory;
  final StreamController<ScreenshotLogEntry> _logStreamController =
      StreamController<ScreenshotLogEntry>.broadcast();

  // 内存缓存
  final Map<String, List<ScreenshotLogEntry>> _memoryCache = {};
  DateTime? _lastCacheUpdate;

  /// 日志条目流
  Stream<ScreenshotLogEntry> get logStream => _logStreamController.stream;

  /// 初始化日志服务
  Future<void> initialize() async {
    try {
      // 获取应用文档目录
      final appDocDir = await getApplicationDocumentsDirectory();
      _logDirectory = Directory('${appDocDir.path}/screenshot_logs');

      // 确保日志目录存在
      if (!await _logDirectory!.exists()) {
        await _logDirectory!.create(recursive: true);
      }

      LoggerService.info('Screenshot log service initialized');
      LoggerService.info('Log directory: ${_logDirectory!.path}');
    } catch (error) {
      LoggerService.error('Failed to initialize screenshot log service', error);
      rethrow;
    }
  }

  /// 记录截图操作
  Future<void> logScreenshot({
    required String id,
    required String action,
    String? filePath,
    required bool success,
    String? error,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      final entry = ScreenshotLogEntry(
        id: id,
        timestamp: DateTime.now(),
        action: action,
        filePath: filePath,
        success: success,
        error: error,
        metadata: metadata,
      );

      // 写入日志文件
      await _writeToLogFile(entry);

      // 清理对应日期的缓存
      final dateStr = DateFormat('yyyy-MM-dd').format(entry.timestamp);
      _memoryCache.remove(dateStr);

      // 发送到流
      _logStreamController.add(entry);

      LoggerService.info('Screenshot operation logged: $id');
    } catch (logError) {
      LoggerService.error('Failed to log screenshot operation', logError);
    }
  }

  /// 写入日志文件
  Future<void> _writeToLogFile(ScreenshotLogEntry entry) async {
    if (_logDirectory == null) {
      throw Exception('Log service not initialized');
    }

    // 按日期创建日志文件
    final dateStr = DateFormat('yyyy-MM-dd').format(entry.timestamp);
    final logFile = File('${_logDirectory!.path}/screenshot_$dateStr.log');

    // 创建日志内容
    final logLine = jsonEncode(entry.toJson());

    // 追加到文件
    await logFile.writeAsString('$logLine\n', mode: FileMode.append);
  }

  /// 获取指定日期的日志条目
  Future<List<ScreenshotLogEntry>> getLogsByDate(DateTime date) async {
    if (_logDirectory == null) {
      throw Exception('Log service not initialized');
    }

    final dateStr = DateFormat('yyyy-MM-dd').format(date);

    // 检查缓存
    if (_memoryCache.containsKey(dateStr) &&
        _lastCacheUpdate != null &&
        DateTime.now().difference(_lastCacheUpdate!).inMinutes < 5) {
      return _memoryCache[dateStr]!;
    }

    final logFile = File('${_logDirectory!.path}/screenshot_$dateStr.log');

    if (!await logFile.exists()) {
      _memoryCache[dateStr] = [];
      return [];
    }

    try {
      final lines = await logFile.readAsLines();
      final entries = <ScreenshotLogEntry>[];

      for (final line in lines) {
        if (line.trim().isNotEmpty) {
          try {
            final json = jsonDecode(line) as Map<String, dynamic>;
            entries.add(ScreenshotLogEntry.fromJson(json));
          } catch (e) {
            LoggerService.warning('Failed to parse log line: $line');
          }
        }
      }

      // 按时间倒序排列
      entries.sort((a, b) => b.timestamp.compareTo(a.timestamp));

      // 更新缓存
      _memoryCache[dateStr] = entries;
      _lastCacheUpdate = DateTime.now();

      return entries;
    } catch (error) {
      LoggerService.error('Failed to read log file: ${logFile.path}', error);
      return [];
    }
  }

  /// 获取最近N天的日志条目
  Future<List<ScreenshotLogEntry>> getRecentLogs({int days = 7}) async {
    final allEntries = <ScreenshotLogEntry>[];
    final now = DateTime.now();

    for (int i = 0; i < days; i++) {
      final date = now.subtract(Duration(days: i));
      final entries = await getLogsByDate(date);
      allEntries.addAll(entries);
    }

    // 按时间倒序排列
    allEntries.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    return allEntries;
  }

  /// 获取可用的日志日期列表
  Future<List<DateTime>> getAvailableLogDates() async {
    if (_logDirectory == null) {
      throw Exception('Log service not initialized');
    }

    final dates = <DateTime>[];

    try {
      final files = await _logDirectory!.list().toList();

      for (final file in files) {
        if (file is File && file.path.endsWith('.log')) {
          final fileName = file.path.split('/').last;
          // 文件名格式: screenshot_yyyy-MM-dd.log
          final match = RegExp(r'screenshot_(\d{4}-\d{2}-\d{2})\.log')
              .firstMatch(fileName);

          if (match != null) {
            try {
              final dateStr = match.group(1)!;
              final date = DateTime.parse(dateStr);
              dates.add(date);
            } catch (e) {
              LoggerService.warning(
                  'Failed to parse date from filename: $fileName');
            }
          }
        }
      }

      // 按日期倒序排列
      dates.sort((a, b) => b.compareTo(a));
      return dates;
    } catch (error) {
      LoggerService.error('Failed to get available log dates', error);
      return [];
    }
  }

  /// 清理过期日志文件
  Future<void> cleanupOldLogs({int keepDays = 30}) async {
    if (_logDirectory == null) {
      throw Exception('Log service not initialized');
    }

    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: keepDays));
      final files = await _logDirectory!.list().toList();

      for (final file in files) {
        if (file is File && file.path.endsWith('.log')) {
          final fileName = file.path.split('/').last;
          final match = RegExp(r'screenshot_(\d{4}-\d{2}-\d{2})\.log')
              .firstMatch(fileName);

          if (match != null) {
            try {
              final dateStr = match.group(1)!;
              final date = DateTime.parse(dateStr);

              if (date.isBefore(cutoffDate)) {
                await file.delete();
                LoggerService.info('Deleted old log file: $fileName');
              }
            } catch (e) {
              LoggerService.warning('Failed to process log file: $fileName');
            }
          }
        }
      }
    } catch (error) {
      LoggerService.error('Failed to cleanup old logs', error);
    }
  }

  /// 获取日志统计信息
  Future<Map<String, dynamic>> getLogStats({int days = 7}) async {
    final entries = await getRecentLogs(days: days);

    final totalCount = entries.length;
    final successCount = entries.where((e) => e.success).length;
    final failureCount = totalCount - successCount;

    final today = DateTime.now();
    final todayEntries = entries
        .where((e) =>
            e.timestamp.year == today.year &&
            e.timestamp.month == today.month &&
            e.timestamp.day == today.day)
        .length;

    return {
      'totalCount': totalCount,
      'successCount': successCount,
      'failureCount': failureCount,
      'successRate': totalCount > 0
          ? (successCount / totalCount * 100).toStringAsFixed(1)
          : '0.0',
      'todayCount': todayEntries,
      'days': days,
    };
  }

  /// 释放资源
  void dispose() {
    _logStreamController.close();
  }
}
