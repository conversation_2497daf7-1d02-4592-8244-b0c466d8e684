import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:getwidget/getwidget.dart'; // 移除GetWidget导入

import '../../../../core/models/image_info.dart';
import '../../../../core/models/device_model.dart';
import '../../../../core/providers/service_providers.dart';
import '../../../../core/services/display_control_service.dart';
import '../../../../core/services/image_management_service.dart';
import '../../../../core/services/logger_service.dart';

/// 显示控制组件
class DisplayControlWidget extends ConsumerStatefulWidget {
  final AppImageInfo? selectedImage;

  const DisplayControlWidget({
    super.key,
    this.selectedImage,
  });

  @override
  ConsumerState<DisplayControlWidget> createState() =>
      _DisplayControlWidgetState();
}

class _DisplayControlWidgetState extends ConsumerState<DisplayControlWidget> {
  List<String> _selectedDevices = [];
  DisplayMode _displayMode = DisplayMode.single;
  int _slideshowInterval = 5;
  bool _isPlaying = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDeviceSelection(),
          const SizedBox(height: 24),
          _buildDisplayModeSelection(),
          const SizedBox(height: 24),
          _buildSlideshowSettings(),
          const SizedBox(height: 24),
          _buildControlButtons(),
          const SizedBox(height: 24),
          Expanded(
            child: _buildDeviceStatus(),
          ),
        ],
      ),
    );
  }

  /// 构建设备选择
  Widget _buildDeviceSelection() {
    final deviceService = ref.watch(deviceManagementServiceProvider);
    final devices = deviceService.getOnlineDevices();

    return Card(
      color: Theme.of(context).colorScheme.surface,
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.devices, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  '目标设备',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    setState(() {
                      if (_selectedDevices.length == devices.length) {
                        _selectedDevices.clear();
                      } else {
                        _selectedDevices = devices.map((d) => d.id).toList();
                      }
                    });
                  },
                  child: Text(
                    _selectedDevices.length == devices.length ? '取消全选' : '全选',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (devices.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6)),
                    const SizedBox(width: 8),
                    Text(
                      '暂无在线设备',
                      style: TextStyle(
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6)),
                    ),
                  ],
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: devices.map<Widget>((device) {
                  final isSelected = _selectedDevices.contains(device.id);
                  return Container(
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Theme.of(context)
                              .colorScheme
                              .primary
                              .withOpacity(0.1)
                          : Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                    ),
                    child: CheckboxListTile(
                      title: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getDeviceIcon(device),
                            size: 16,
                            color: isSelected
                                ? Theme.of(context).colorScheme.onSurface
                                : Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            device.name,
                            style: TextStyle(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.onSurface
                                  : Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.7),
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                      value: isSelected,
                      onChanged: (selected) {
                        setState(() {
                          if (selected == true) {
                            _selectedDevices.add(device.id);
                          } else {
                            _selectedDevices.remove(device.id);
                          }
                        });
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      checkColor: Theme.of(context).colorScheme.onPrimary,
                      fillColor: WidgetStateProperty.resolveWith<Color?>(
                        (Set<WidgetState> states) {
                          if (states.contains(WidgetState.selected)) {
                            return Theme.of(context).colorScheme.primary;
                          }
                          return Colors.transparent;
                        },
                      ),
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  /// 构建显示模式选择
  Widget _buildDisplayModeSelection() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.tv, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '显示模式',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: _displayMode == DisplayMode.single
                          ? Theme.of(context)
                              .colorScheme
                              .primary
                              .withOpacity(0.1)
                          : Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _displayMode == DisplayMode.single
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                    ),
                    child: RadioListTile<DisplayMode>(
                      title: Text(
                        '单张显示',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 14),
                      ),
                      subtitle: Text(
                        '显示单张图片',
                        style: TextStyle(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                            fontSize: 12),
                      ),
                      value: DisplayMode.single,
                      groupValue: _displayMode,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _displayMode = value;
                          });
                        }
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      fillColor: WidgetStateProperty.resolveWith<Color?>(
                        (Set<WidgetState> states) {
                          if (states.contains(WidgetState.selected)) {
                            return Theme.of(context).colorScheme.primary;
                          }
                          return Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6);
                        },
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: _displayMode == DisplayMode.slideshow
                          ? Theme.of(context)
                              .colorScheme
                              .primary
                              .withOpacity(0.1)
                          : Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _displayMode == DisplayMode.slideshow
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                    ),
                    child: RadioListTile<DisplayMode>(
                      title: Text(
                        '轮播显示',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 14),
                      ),
                      subtitle: Text(
                        '自动切换图片',
                        style: TextStyle(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                            fontSize: 12),
                      ),
                      value: DisplayMode.slideshow,
                      groupValue: _displayMode,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _displayMode = value;
                          });
                        }
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      fillColor: WidgetStateProperty.resolveWith<Color?>(
                        (Set<WidgetState> states) {
                          if (states.contains(WidgetState.selected)) {
                            return Theme.of(context).colorScheme.primary;
                          }
                          return Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6);
                        },
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: _displayMode == DisplayMode.synchronized
                          ? Theme.of(context)
                              .colorScheme
                              .primary
                              .withOpacity(0.1)
                          : Theme.of(context)
                              .colorScheme
                              .surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _displayMode == DisplayMode.synchronized
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.outline,
                        width: 1,
                      ),
                    ),
                    child: RadioListTile<DisplayMode>(
                      title: Text(
                        '同步显示',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface,
                            fontSize: 14),
                      ),
                      subtitle: Text(
                        '多设备同步',
                        style: TextStyle(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.7),
                            fontSize: 12),
                      ),
                      value: DisplayMode.synchronized,
                      groupValue: _displayMode,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _displayMode = value;
                          });
                        }
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      fillColor: WidgetStateProperty.resolveWith<Color?>(
                        (Set<WidgetState> states) {
                          if (states.contains(WidgetState.selected)) {
                            return Theme.of(context).colorScheme.primary;
                          }
                          return Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.6);
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建轮播设置
  Widget _buildSlideshowSettings() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timer, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '轮播设置',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Text(
                  '切换间隔:',
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.onSurface),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: Theme.of(context).colorScheme.primary,
                      inactiveTrackColor: Theme.of(context).colorScheme.outline,
                      thumbColor: Theme.of(context).colorScheme.primary,
                      overlayColor: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.2),
                      valueIndicatorColor:
                          Theme.of(context).colorScheme.primary,
                      valueIndicatorTextStyle: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                    child: Slider(
                      value: _slideshowInterval.toDouble(),
                      min: 1,
                      max: 30,
                      divisions: 29,
                      label: '$_slideshowInterval秒',
                      onChanged: (value) {
                        setState(() {
                          _slideshowInterval = value.round();
                        });
                      },
                    ),
                  ),
                ),
                Text(
                  '$_slideshowInterval秒',
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.onSurface),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建控制按钮
  Widget _buildControlButtons() {
    final hasSelectedDevices = _selectedDevices.isNotEmpty;
    final hasSelectedImage = widget.selectedImage != null;

    return Card(
      color: Theme.of(context).colorScheme.surface,
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.control_camera,
                    color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '显示控制',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 基本控制按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: hasSelectedDevices && hasSelectedImage
                        ? _showImage
                        : null,
                    icon: Icon(Icons.image,
                        color: Theme.of(context).colorScheme.onPrimary),
                    label: Text(
                      '显示图片',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      disabledBackgroundColor: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.3),
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: hasSelectedDevices ? _startSlideshow : null,
                    icon: Icon(
                      _isPlaying ? Icons.pause : Icons.play_arrow,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                    label: Text(
                      _isPlaying ? '暂停轮播' : '开始轮播',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      disabledBackgroundColor: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.3),
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: hasSelectedDevices ? _stopDisplay : null,
                    icon: Icon(Icons.stop,
                        color: Theme.of(context).colorScheme.onError),
                    label: Text(
                      '停止显示',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onError,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.error,
                      disabledBackgroundColor: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.3),
                      foregroundColor: Theme.of(context).colorScheme.onError,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // 导航控制按钮
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: hasSelectedDevices ? _previousImage : null,
                    icon: Icon(Icons.skip_previous,
                        color: Theme.of(context).colorScheme.primary),
                    label: Text(
                      '上一张',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                          color: Theme.of(context).colorScheme.primary),
                      foregroundColor: Theme.of(context).colorScheme.primary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: hasSelectedDevices ? _nextImage : null,
                    icon: Icon(Icons.skip_next,
                        color: Theme.of(context).colorScheme.primary),
                    label: Text(
                      '下一张',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                          color: Theme.of(context).colorScheme.primary),
                      foregroundColor: Theme.of(context).colorScheme.primary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: hasSelectedDevices ? _requestScreenshot : null,
                    icon: const Icon(Icons.screenshot),
                    label: const Text('截图'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: hasSelectedDevices
                          ? Colors.orange
                          : Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.3),
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      textStyle: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建设备状态
  Widget _buildDeviceStatus() {
    final deviceService = ref.watch(deviceManagementServiceProvider);
    final displayService = ref.watch(displayControlServiceProvider);

    return Card(
      color: Theme.of(context).colorScheme.surface,
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  '设备状态',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_selectedDevices.isEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Center(
                  child: Text(
                    '请先选择设备',
                    style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6)),
                  ),
                ),
              )
            else
              Expanded(
                child: ListView.builder(
                  itemCount: _selectedDevices.length,
                  itemBuilder: (context, index) {
                    final deviceId = _selectedDevices[index];
                    final device = deviceService.getDevice(deviceId);
                    final displayState =
                        displayService.getDeviceState(deviceId);

                    if (device == null) {
                      return const SizedBox.shrink();
                    }

                    return ListTile(
                      leading: Icon(
                        _getDeviceIcon(device),
                        color: _getStatusColor(device.status),
                      ),
                      title: Text(
                        device.name,
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '状态: ${_getStatusText(device.status)}',
                            style: TextStyle(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6)),
                          ),
                          if (displayState != null)
                            Text(
                              '显示: ${displayState.isDisplaying ? "播放中" : "空闲"}',
                              style: TextStyle(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurface
                                      .withOpacity(0.6)),
                            ),
                        ],
                      ),
                      trailing: displayState?.isDisplaying == true
                          ? const Icon(Icons.play_circle, color: Colors.green)
                          : null,
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  /// 显示图片
  Future<void> _showImage() async {
    if (widget.selectedImage == null || _selectedDevices.isEmpty) return;

    try {
      final displayService = ref.read(displayControlServiceProvider);

      await displayService.showImage(
        widget.selectedImage!.id,
        deviceIds: _selectedDevices,
      );

      _showSuccessSnackBar('图片显示命令已发送');
    } catch (error) {
      LoggerService.error('Failed to show image', error);
      _showErrorSnackBar('显示图片失败');
    }
  }

  /// 开始轮播
  Future<void> _startSlideshow() async {
    if (_selectedDevices.isEmpty) return;

    try {
      final displayService = ref.read(displayControlServiceProvider);
      final imageService = ref.read(imageManagementServiceProvider);

      final images = imageService.getAllImages();
      if (images.isEmpty) {
        _showErrorSnackBar('没有可用的图片');
        return;
      }

      if (_isPlaying) {
        await displayService.stopDisplay(_selectedDevices);
        setState(() {
          _isPlaying = false;
        });
        _showSuccessSnackBar('轮播已暂停');
      } else {
        await displayService.startSlideshowDisplay(
          images.map((img) => img.id).toList(),
          _selectedDevices,
          config: DisplayConfig(
            mode: DisplayMode.slideshow,
            duration: Duration(seconds: _slideshowInterval),
            autoPlay: true,
          ),
        );
        setState(() {
          _isPlaying = true;
        });
        _showSuccessSnackBar('轮播已开始');
      }
    } catch (error) {
      LoggerService.error('Failed to control slideshow', error);
      _showErrorSnackBar('轮播控制失败');
    }
  }

  /// 停止显示
  Future<void> _stopDisplay() async {
    if (_selectedDevices.isEmpty) return;

    try {
      final displayService = ref.read(displayControlServiceProvider);

      await displayService.stopDisplay(_selectedDevices);

      setState(() {
        _isPlaying = false;
      });

      _showSuccessSnackBar('显示已停止');
    } catch (error) {
      LoggerService.error('Failed to stop display', error);
      _showErrorSnackBar('停止显示失败');
    }
  }

  /// 上一张图片
  Future<void> _previousImage() async {
    if (_selectedDevices.isEmpty) return;

    try {
      final displayService = ref.read(displayControlServiceProvider);

      await displayService.previousImage(_selectedDevices);

      _showSuccessSnackBar('切换到上一张');
    } catch (error) {
      LoggerService.error('Failed to show previous image', error);
      _showErrorSnackBar('切换失败');
    }
  }

  /// 下一张图片
  Future<void> _nextImage() async {
    if (_selectedDevices.isEmpty) return;

    try {
      final displayService = ref.read(displayControlServiceProvider);

      await displayService.nextImage(_selectedDevices);

      _showSuccessSnackBar('切换到下一张');
    } catch (error) {
      LoggerService.error('Failed to show next image', error);
      _showErrorSnackBar('切换失败');
    }
  }

  /// 请求截图
  Future<void> _requestScreenshot() async {
    if (_selectedDevices.isEmpty) return;

    try {
      final deviceService = ref.read(deviceManagementServiceProvider);

      for (final deviceId in _selectedDevices) {
        await deviceService.requestScreenshot(deviceId);
      }

      _showSuccessSnackBar('截图请求已发送');
    } catch (error) {
      LoggerService.error('Failed to request screenshot', error);
      _showErrorSnackBar('截图请求失败');
    }
  }

  /// 获取设备图标
  IconData _getDeviceIcon(Device device) {
    switch (device.platform.toLowerCase()) {
      case 'windows':
        return Icons.desktop_windows;
      case 'macos':
        return Icons.desktop_mac;
      case 'linux':
        return Icons.computer;
      case 'android':
        return Icons.phone_android;
      case 'ios':
        return Icons.phone_iphone;
      default:
        return Icons.device_unknown;
    }
  }

  /// 获取状态颜色
  Color _getStatusColor(DeviceStatus status) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (status) {
      case DeviceStatus.online:
        return Colors.green;
      case DeviceStatus.offline:
        return colorScheme.onSurface.withOpacity(0.5);
      case DeviceStatus.connecting:
        return Colors.orange;
      case DeviceStatus.disconnecting:
        return Colors.orange;
      case DeviceStatus.capturing:
        return Colors.purple;
      case DeviceStatus.error:
        return colorScheme.error;
      case DeviceStatus.maintenance:
        return colorScheme.primary;
      case DeviceStatus.unregistered:
        return colorScheme.onSurface.withOpacity(0.4);
      case DeviceStatus.registering:
        return colorScheme.primary.withOpacity(0.7);
      case DeviceStatus.registered:
        return Colors.green.withOpacity(0.7);
    }
  }

  /// 获取状态文本
  String _getStatusText(DeviceStatus status) {
    switch (status) {
      case DeviceStatus.online:
        return '在线';
      case DeviceStatus.offline:
        return '离线';
      case DeviceStatus.connecting:
        return '连接中';
      case DeviceStatus.disconnecting:
        return '断开中';
      case DeviceStatus.capturing:
        return '截图中';
      case DeviceStatus.error:
        return '错误';
      case DeviceStatus.maintenance:
        return '维护中';
      case DeviceStatus.unregistered:
        return '未注册';
      case DeviceStatus.registering:
        return '注册中';
      case DeviceStatus.registered:
        return '已注册';
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
