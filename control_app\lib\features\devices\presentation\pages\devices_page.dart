import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:getwidget/getwidget.dart';
import '../../../../core/services/device_management_service.dart';
import '../../../../core/models/device_info.dart';
import '../../../../core/models/device_model.dart';
import '../../../../core/config/app_constants.dart';
import '../../../../core/providers/service_providers.dart';

class DevicesPage extends ConsumerStatefulWidget {
  const DevicesPage({super.key});

  @override
  ConsumerState<DevicesPage> createState() => _DevicesPageState();
}

class _DevicesPageState extends ConsumerState<DevicesPage> {
  bool _isScanning = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initializeDevices();
  }

  Future<void> _initializeDevices() async {
    final deviceService = ref.read(deviceManagementServiceProvider);
    await deviceService.initialize();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: const Text(
          '设备管理',
          style: TextStyle(color: Colors.white),
        ),
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: Icon(
              _isScanning ? Icons.stop : Icons.search,
              color: Colors.white,
            ),
            onPressed: _isScanning ? _stopScanning : _startScanning,
          ),
          IconButton(
            icon: const Icon(
              Icons.refresh,
              color: Colors.white,
            ),
            onPressed: _refreshDevices,
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: GFCard(
              color: Theme.of(context).colorScheme.surface,
              elevation: 4,
              content: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: TextField(
                  controller: _searchController,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: '搜索设备...',
                    hintStyle: const TextStyle(color: Colors.grey),
                    prefixIcon: const Icon(Icons.search, color: Colors.grey),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear, color: Colors.grey),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: InputBorder.none,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ),
          ),
          // 扫描状态
          if (_isScanning)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                  SizedBox(width: 16),
                  Text(
                    '正在扫描设备...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          // 设备列表
          Expanded(
            child: Builder(
              builder: (context) {
                final deviceService = ref.watch(deviceManagementServiceProvider);
                final filteredDevices = _filterDevices(deviceService.devices);
                
                if (filteredDevices.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.devices_other,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _searchQuery.isNotEmpty ? '未找到匹配的设备' : '暂无设备',
                          style: TextStyle(
                            fontSize: 18,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '点击扫描按钮搜索设备',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[500],
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: filteredDevices.length,
                  itemBuilder: (context, index) {
                    final device = filteredDevices[index];
                    return _buildDeviceCard(device);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addDeviceManually,
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  List<Device> _filterDevices(List<Device> devices) {
    if (_searchQuery.isEmpty) {
      return devices;
    }
    
    return devices.where((device) {
      return device.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
             (device.ipAddress?.contains(_searchQuery) ?? false) ||
             device.id.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();
  }

  Widget _buildDeviceCard(Device device) {
    return GFCard(
      margin: const EdgeInsets.only(bottom: 8),
      color: Theme.of(context).colorScheme.surface,
      elevation: 4,
      content: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getStatusColor(device.status.toString().split('.').last),
          child: Icon(
            _getDeviceIcon(device.type.toString().split('.').last),
            color: Colors.white,
          ),
        ),
        title: Text(
          device.name,
          style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'IP: ${device.ipAddress ?? 'N/A'}',
              style: const TextStyle(color: Colors.grey),
            ),
            Text(
              '状态: ${_getStatusText(device.status.toString().split('.').last)}',
              style: const TextStyle(color: Colors.grey),
            ),
            Text(
              '最后连接: ${_formatDateTime(device.lastSeen)}',
              style: const TextStyle(color: Colors.grey),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          onSelected: (value) => _handleDeviceAction(device, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'connect',
              child: ListTile(
                leading: Icon(Icons.link),
                title: Text('连接'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'disconnect',
              child: ListTile(
                leading: Icon(Icons.link_off),
                title: Text('断开'),
                contentPadding: const EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('编辑'),
                contentPadding: const EdgeInsets.zero,
               ),
             ),
             const PopupMenuItem(
               value: 'test',
               child: ListTile(
                 leading: Icon(Icons.speed),
                 title: Text('测试'),
                 contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'remove',
              child: ListTile(
                 leading: Icon(Icons.delete, color: Colors.red),
                 title: Text('删除', style: TextStyle(color: Colors.red)),
                 contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        onTap: () => _showDeviceDetails(device),
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return Colors.green;
      case 'offline':
        return Colors.red;
      case 'connecting':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getDeviceIcon(String type) {
    switch (type.toLowerCase()) {
      case 'terminal':
        return Icons.computer;
      case 'display':
        return Icons.tv;
      default:
        return Icons.device_unknown;
    }
  }

  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'connecting':
        return '连接中';
      default:
        return '未知';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }

  Future<void> _startScanning() async {
    setState(() {
      _isScanning = true;
    });
    
    try {
      final deviceService = ref.read(deviceManagementServiceProvider);
      await deviceService.scanForDevices();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('扫描失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
    }
  }

  void _stopScanning() {
    setState(() {
      _isScanning = false;
    });
    
    final deviceService = ref.read(deviceManagementServiceProvider);
    deviceService.stopScanning();
  }

  Future<void> _refreshDevices() async {
    final deviceService = ref.read(deviceManagementServiceProvider);
    await deviceService.refreshDevices();
  }

  Future<void> _handleDeviceAction(Device device, String action) async {
    final deviceService = ref.read(deviceManagementServiceProvider);
    
    try {
      switch (action) {
        case 'connect':
          await deviceService.connectToDevice(device.id);
          break;
        case 'disconnect':
          await deviceService.disconnectDevice(device.id);
          break;
        case 'test':
          await deviceService.testConnection(device.id);
          break;
        case 'remove':
          await _confirmRemoveDevice(device);
          break;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _confirmRemoveDevice(Device device) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认移除'),
        content: Text('确定要移除设备 "${device.name}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
    
    if (confirmed == true) {
      final deviceService = ref.read(deviceManagementServiceProvider);
      await deviceService.removeDevice(device.id);
    }
  }

  void _showDeviceDetails(Device device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text(
          device.name,
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDetailRow('设备ID', device.id),
            _buildDetailRow('IP地址', device.ipAddress ?? '未知'),
            _buildDetailRow('端口', device.port.toString()),
            _buildDetailRow('类型', device.type.name),
            _buildDetailRow('状态', device.statusText),
            if (device.lastSeen != null)
              _buildDetailRow('最后连接', _formatDateTime(device.lastSeen!)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '关闭',
              style: TextStyle(color: Colors.blue),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(color: Colors.grey),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _addDeviceManually() async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _AddDeviceDialog(),
    );
    
    if (result != null) {
      try {
        final deviceService = ref.read(deviceManagementServiceProvider);
        await deviceService.addDevice(
          result['name']!,
          result['ip']!,
          int.parse(result['port']!),
        );
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('设备添加成功'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('添加设备失败: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

class _AddDeviceDialog extends StatefulWidget {
  @override
  State<_AddDeviceDialog> createState() => _AddDeviceDialogState();
}

class _AddDeviceDialogState extends State<_AddDeviceDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ipController = TextEditingController();
  final _portController = TextEditingController(text: '8080');

  @override
  void dispose() {
    _nameController.dispose();
    _ipController.dispose();
    _portController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      title: const Text(
        '添加设备',
        style: TextStyle(color: Colors.white),
      ),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: '设备名称',
                labelStyle: TextStyle(color: Colors.grey),
                hintText: '输入设备名称',
                hintStyle: TextStyle(color: Colors.grey),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入设备名称';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _ipController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: 'IP地址',
                labelStyle: TextStyle(color: Colors.grey),
                hintText: '*************',
                hintStyle: TextStyle(color: Colors.grey),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入IP地址';
                }
                // 简单的IP地址验证
                final ipRegex = RegExp(r'^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$');
                if (!ipRegex.hasMatch(value)) {
                  return '请输入有效的IP地址';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _portController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                labelText: '端口',
                labelStyle: TextStyle(color: Colors.grey),
                hintText: '8080',
                hintStyle: TextStyle(color: Colors.grey),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.grey),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入端口号';
                }
                final port = int.tryParse(value);
                if (port == null || port < 1 || port > 65535) {
                  return '请输入有效的端口号 (1-65535)';
                }
                return null;
              },
            ),
          ],
        ),
      ),
      actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '取消',
              style: TextStyle(color: Colors.grey),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              if (_formKey.currentState!.validate()) {
                Navigator.of(context).pop({
                  'name': _nameController.text,
                  'ip': _ipController.text,
                  'port': _portController.text,
                });
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: const Text('添加'),
          ),
        ],
    );
  }
}