import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'logger_service.dart';

/// 网关发现服务
class DiscoveryService {
  static DiscoveryService? _instance;
  static DiscoveryService get instance => _instance ??= DiscoveryService._();
  
  DiscoveryService._();

  final List<String> _discoveredGateways = [];
  Timer? _discoveryTimer;
  bool _isDiscovering = false;

  /// 已发现的网关列表
  List<String> get discoveredGateways => List.unmodifiable(_discoveredGateways);

  /// 是否正在发现
  bool get isDiscovering => _isDiscovering;

  /// 开始发现网关
  Future<void> startDiscovery() async {
    if (_isDiscovering) return;
    
    _isDiscovering = true;
    LoggerService.info('开始发现网关服务...');
    
    // 立即执行一次发现
    await _performDiscovery();
    
    // 定期发现
    _discoveryTimer = Timer.periodic(
      const Duration(seconds: 30),
      (_) => _performDiscovery(),
    );
  }

  /// 停止发现
  void stopDiscovery() {
    if (!_isDiscovering) return;
    
    _isDiscovering = false;
    _discoveryTimer?.cancel();
    _discoveryTimer = null;
    LoggerService.info('停止发现网关服务');
  }

  /// 执行发现
  Future<void> _performDiscovery() async {
    try {
      // 获取本地网络接口
      final interfaces = await NetworkInterface.list();
      final Set<String> newGateways = {};
      
      for (final interface in interfaces) {
        if (interface.addresses.isEmpty) continue;
        
        for (final address in interface.addresses) {
          if (address.type != InternetAddressType.IPv4) continue;
          if (address.isLoopback) continue;
          
          // 扫描同网段的常用端口
          final subnet = _getSubnet(address.address);
          final gateways = await _scanSubnet(subnet);
          newGateways.addAll(gateways);
        }
      }
      
      // 更新发现的网关列表
      _discoveredGateways.clear();
      _discoveredGateways.addAll(newGateways);
      
      if (_discoveredGateways.isNotEmpty) {
        LoggerService.info('发现 ${_discoveredGateways.length} 个网关: ${_discoveredGateways.join(", ")}');
      }
    } catch (e) {
      LoggerService.error('网关发现失败: $e');
    }
  }

  /// 获取子网
  String _getSubnet(String ipAddress) {
    final parts = ipAddress.split('.');
    if (parts.length != 4) return '';
    return '${parts[0]}.${parts[1]}.${parts[2]}';
  }

  /// 扫描子网
  Future<Set<String>> _scanSubnet(String subnet) async {
    if (subnet.isEmpty) return {};
    
    final Set<String> gateways = {};
    final List<Future<void>> scanTasks = [];
    
    // 扫描常用的网关端口
    final ports = [3000, 3001, 8080, 8081, 9000, 9001];
    
    for (int i = 1; i <= 254; i++) {
      final ip = '$subnet.$i';
      
      for (final port in ports) {
        scanTasks.add(_checkGateway(ip, port).then((url) {
          if (url != null) {
            gateways.add(url);
          }
        }));
      }
    }
    
    // 等待所有扫描任务完成，但设置超时
    try {
      await Future.wait(scanTasks).timeout(const Duration(seconds: 10));
    } catch (e) {
      LoggerService.warning('子网扫描超时: $e');
    }
    
    return gateways;
  }

  /// 检查是否为网关
  Future<String?> _checkGateway(String ip, int port) async {
    try {
      final url = 'http://$ip:$port';
      final response = await http.get(
        Uri.parse('$url/api/health'),
        headers: {'User-Agent': 'TerminalApp/1.0'},
      ).timeout(const Duration(seconds: 2));
      
      if (response.statusCode == 200) {
        try {
          final data = json.decode(response.body);
          if (data['service'] == 'gateway' || data['type'] == 'gateway') {
            return url;
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      }
    } catch (e) {
      // 忽略连接错误
    }
    
    return null;
  }

  /// 手动添加网关
  void addGateway(String gatewayUrl) {
    if (!_discoveredGateways.contains(gatewayUrl)) {
      _discoveredGateways.add(gatewayUrl);
      LoggerService.info('手动添加网关: $gatewayUrl');
    }
  }

  /// 移除网关
  void removeGateway(String gatewayUrl) {
    if (_discoveredGateways.remove(gatewayUrl)) {
      LoggerService.info('移除网关: $gatewayUrl');
    }
  }

  /// 清除所有网关
  void clearGateways() {
    _discoveredGateways.clear();
    LoggerService.info('清除所有网关');
  }

  /// 获取最佳网关
  String? getBestGateway() {
    if (_discoveredGateways.isEmpty) return null;
    
    // 简单返回第一个，后续可以添加延迟测试等逻辑
    return _discoveredGateways.first;
  }

  /// 释放资源
  void dispose() {
    stopDiscovery();
    _discoveredGateways.clear();
  }
}