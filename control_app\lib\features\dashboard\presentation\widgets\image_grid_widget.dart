import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../core/models/image_info.dart';
import '../../../../core/services/image_management_service.dart';
import '../../../../core/services/logger_service.dart';

/// 图片网格组件
class ImageGridWidget extends ConsumerStatefulWidget {
  final Function(AppImageInfo) onImageSelected;
  final AppImageInfo? selectedImage;
  final String? groupId;

  const ImageGridWidget({
    super.key,
    required this.onImageSelected,
    this.selectedImage,
    this.groupId,
  });

  @override
  ConsumerState<ImageGridWidget> createState() => _ImageGridWidgetState();
}

class _ImageGridWidgetState extends ConsumerState<ImageGridWidget> {
  final TextEditingController _searchController = TextEditingController();
  ImageSortBy _sortBy = ImageSortBy.createdAt;
  SortDirection _sortDirection = SortDirection.descending;
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildToolbar(),
        Expanded(
          child: _buildImageGrid(),
        ),
      ],
    );
  }

  /// 构建工具栏
  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // 搜索框
          Expanded(
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                hintText: '搜索图片...',
                hintStyle: TextStyle(color: Colors.white54),
                prefixIcon: Icon(Icons.search, color: Colors.white54),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.white24),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: Colors.blue),
                ),
                filled: true,
                fillColor: Color(0xFF3A3A3A),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),

          const SizedBox(width: 16),

          // 排序选择
          DropdownButton<ImageSortBy>(
            value: _sortBy,
            dropdownColor: const Color(0xFF2A2A2A),
            style: const TextStyle(color: Colors.white),
            items: const [
              DropdownMenuItem(
                value: ImageSortBy.name,
                child: Text('按名称', style: TextStyle(color: Colors.white)),
              ),
              DropdownMenuItem(
                value: ImageSortBy.createdAt,
                child: Text('按时间', style: TextStyle(color: Colors.white)),
              ),
              DropdownMenuItem(
                value: ImageSortBy.size,
                child: Text('按大小', style: TextStyle(color: Colors.white)),
              ),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _sortBy = value;
                });
              }
            },
          ),

          const SizedBox(width: 8),

          // 排序方向
          IconButton(
            icon: Icon(
              _sortDirection == SortDirection.ascending
                  ? Icons.arrow_upward
                  : Icons.arrow_downward,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                _sortDirection = _sortDirection == SortDirection.ascending
                    ? SortDirection.descending
                    : SortDirection.ascending;
              });
            },
            tooltip: _sortDirection == SortDirection.ascending ? '升序' : '降序',
          ),
        ],
      ),
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid() {
    final imageService = ref.watch(imageManagementServiceProvider);

    // 获取图片列表
    List<AppImageInfo> images;
    if (widget.groupId != null) {
      images = imageService.getImagesByGroup(widget.groupId!);
    } else {
      images = imageService.getAllImages();
    }

    // 应用搜索过滤
    if (_searchQuery.isNotEmpty) {
      images = imageService.searchImages(
        nameContains: _searchQuery,
        tagsContain: [_searchQuery],
      );
    }

    // 应用排序
    final sorter = ImageSorter(
      sortBy: _sortBy,
      direction: _sortDirection,
    );
    images = sorter.sort(images);

    if (images.isEmpty) {
      return _buildEmptyState();
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 4,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.0,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final image = images[index];
        return _buildImageCard(image);
      },
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: Colors.white54,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty ? '未找到匹配的图片' : '暂无图片',
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty ? '尝试使用不同的搜索关键词' : '点击导入按钮添加图片',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white54,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片卡片
  Widget _buildImageCard(AppImageInfo image) {
    final isSelected = widget.selectedImage?.id == image.id;

    return GestureDetector(
      onTap: () => widget.onImageSelected(image),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.white24,
            width: isSelected ? 3 : 1,
          ),
          boxShadow: [
            if (isSelected)
              BoxShadow(
                color: Colors.blue.withValues(alpha: 0.3),
                spreadRadius: 2,
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(7),
          child: Stack(
            children: [
              // 图片
              _buildImageWidget(image),

              // 覆盖层
              _buildOverlay(image),

              // 选中指示器
              if (isSelected)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(4),
                    decoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),

              // 收藏指示器
              if (image.isFavorite)
                const Positioned(
                  top: 8,
                  left: 8,
                  child: Icon(
                    Icons.favorite,
                    color: Colors.red,
                    size: 20,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建图片组件
  Widget _buildImageWidget(AppImageInfo image) {
    // 检查是否有缩略图
    final thumbnailPath = image.getThumbnailPath();
    if (File(thumbnailPath).existsSync()) {
      return Image.file(
        File(thumbnailPath),
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        errorBuilder: (context, error, stackTrace) {
          LoggerService.warning('Failed to load thumbnail: $thumbnailPath');
          return _buildOriginalImage(image);
        },
      );
    }

    return _buildOriginalImage(image);
  }

  /// 构建原始图片
  Widget _buildOriginalImage(AppImageInfo image) {
    if (image.path.startsWith('http')) {
      // 网络图片
      return CachedNetworkImage(
        imageUrl: image.path,
        fit: BoxFit.cover,
        width: double.infinity,
        height: double.infinity,
        placeholder: (context, url) => Container(
          color: const Color(0xFF3A3A3A),
          child: const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
        ),
        errorWidget: (context, url, error) {
          LoggerService.warning('Failed to load network image: ${image.path}');
          return _buildErrorWidget();
        },
      );
    } else {
      // 本地图片
      final file = File(image.path);
      if (file.existsSync()) {
        return Image.file(
          file,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            LoggerService.warning('Failed to load local image: ${image.path}');
            return _buildErrorWidget();
          },
        );
      } else {
        LoggerService.warning('Image file not found: ${image.path}');
        return _buildErrorWidget();
      }
    }
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    return Container(
      color: const Color(0xFF3A3A3A),
      child: const Center(
        child: Icon(
          Icons.broken_image,
          color: Colors.white54,
          size: 32,
        ),
      ),
    );
  }

  /// 构建覆盖层
  Widget _buildOverlay(AppImageInfo image) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Color.fromRGBO(0, 0, 0, 0.7),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              image.name,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              image.getFormattedSize(),
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
