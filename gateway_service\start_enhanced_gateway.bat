@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                                                               ║
echo ║    Flutter 图片切换系统 - 增强版网关服务启动器               ║
echo ║    Version: 1.1.0 (实时日志增强版)                           ║
echo ║                                                               ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

echo [INFO] 正在启动增强版网关服务 (实时日志)...
echo.

REM 检查当前目录
echo [1/6] 检查目录结构...
if not exist "final_unified_gateway_with_logs.js" (
    echo [ERROR] 未找到增强版网关服务文件
    echo [HINT] 请确保在 gateway_service 目录下运行此脚本
    echo 当前目录: %CD%
    pause
    exit /b 1
)
echo [SUCCESS] 增强版网关服务文件存在

REM 检查Node.js
echo [2/6] 检查Node.js环境...
node --version >nul 2>&1
set node_result=!errorlevel!
if !node_result! neq 0 (
    echo [ERROR] Node.js 未安装或未配置在 PATH 中
    echo [HINT] 请安装 Node.js 14.0+ 版本
    pause
    exit /b 1
)
node --version
echo [SUCCESS] Node.js 环境检查通过

REM 检查端口占用
echo [3/6] 检查端口7777占用情况...
netstat -an | findstr ":7777 " >nul 2>&1
set port_result=!errorlevel!
if !port_result! equ 0 (
    echo [WARNING] 端口7777已被占用
    echo [INFO] 正在尝试终止占用进程...
    
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr ":7777 "') do (
        echo 终止进程 PID: %%a
        taskkill /PID %%a /F >nul 2>&1
    )
    
    timeout /t 2 >nul
    netstat -an | findstr ":7777 " >nul 2>&1
    set port_check_result=!errorlevel!
    if !port_check_result! equ 0 (
        echo [ERROR] 无法释放端口7777
        echo [HINT] 请手动检查端口占用情况
        pause
        exit /b 1
    )
)
echo [SUCCESS] 端口7777可用

REM 创建日志目录
echo [4/6] 准备日志目录...
if not exist "logs" (
    mkdir logs
    echo [INFO] 创建日志目录: logs/
)
echo [SUCCESS] 日志目录准备完成

REM 备份旧日志
echo [5/6] 备份旧日志文件...
set today=%date:~0,4%-%date:~5,2%-%date:~8,2%
if exist "logs\gateway-%today%.log" (
    echo [INFO] 发现今日日志文件，将继续追加
) else (
    echo [INFO] 创建新的日志文件
)

if exist "logs\error-%today%.log" (
    echo [INFO] 发现今日错误日志文件，将继续追加
)
echo [SUCCESS] 日志备份完成

REM 启动增强版网关服务
echo [6/6] 启动增强版网关服务...
echo.
echo ═══════════════════════════════════════════════════════════════
echo              增强版网关服务 (实时日志) 启动中...              
echo ═══════════════════════════════════════════════════════════════
echo.
echo [INFO] 服务地址: http://localhost:7777
echo [INFO] 终端WebSocket: ws://localhost:7777/terminal/ws
echo [INFO] 控制端WebSocket: ws://localhost:7777/controller/ws
echo [INFO] 健康检查: http://localhost:7777/health
echo [INFO] 日志目录: %CD%\logs\
echo [INFO] 实时日志: 控制台彩色输出
echo.
echo [HINT] 按 Ctrl+C 可以优雅关闭服务
echo [HINT] 日志文件按日期自动分割存储
echo.
echo ═══════════════════════════════════════════════════════════════
echo.

REM 启动服务
node final_unified_gateway_with_logs.js

REM 处理退出
echo.
echo [INFO] 增强版网关服务已停止
echo [INFO] 日志文件保存在: %CD%\logs\
echo.

REM 显示最近的日志统计
if exist "logs" (
    echo [STATISTICS] 日志文件统计:
    dir logs\*.log /B 2>nul | find /C ".log" >temp_count.txt
    set /p log_count=<temp_count.txt
    del temp_count.txt >nul 2>&1
    echo   总日志文件数: !log_count!
    
    echo   最新日志文件:
    dir logs\*.log /O-D /B 2>nul | head -3
    echo.
)

echo [SUCCESS] 增强版网关服务启动器执行完成
pause