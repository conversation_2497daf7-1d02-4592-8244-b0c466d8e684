^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-MKDIR.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/tmp/nuget-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-DOWNLOAD.RULE
setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/download-nuget-populate.cmake
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/verify-nuget-populate.cmake
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-UPDATE.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-update
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-PATCH.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-COPYFILE.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_if_different E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget.exe E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-src
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-copyfile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-CONFIGURE.RULE
setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-BUILD.RULE
setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-INSTALL.RULE
setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\30D0F54B5CF991FCD0DF5E84963AD0CD\NUGET-POPULATE-TEST.RULE
setlocal
cd E:\AI_codeE\imgCT\terminal_app\build\windows\x64\_deps\nuget-build
if %errorlevel% neq 0 goto :cmEnd
E:
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\7F191F373C3A1F1BE09CF6BD33745AD7\NUGET-POPULATE-COMPLETE.RULE
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug/nuget-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\7302C58A2FE102E75E2A16B8C730C9D4\NUGET-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^E:\AI_CODEE\IMGCT\TERMINAL_APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKELISTS.TXT
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild -BE:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild --check-stamp-file E:/AI_codeE/imgCT/terminal_app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
