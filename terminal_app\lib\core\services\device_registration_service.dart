import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:network_info_plus/network_info_plus.dart';
import '../models/device_model.dart';
import '../config/app_config.dart';
import 'websocket_service.dart';
import 'storage_service.dart';

/// 设备注册服务
/// 负责设备信息收集、注册和状态监控
class DeviceRegistrationService {
  static final DeviceRegistrationService _instance =
      DeviceRegistrationService._internal();
  factory DeviceRegistrationService() => _instance;
  DeviceRegistrationService._internal();

  // 服务依赖
  final WebSocketService _webSocketService = WebSocketService.instance;
  final StorageService _storageService = StorageService();
  final DeviceInfoPlugin _deviceInfoPlugin = DeviceInfoPlugin();
  final NetworkInfo _networkInfo = NetworkInfo();

  // 设备信息
  Device? _currentDevice;
  Timer? _statusMonitorTimer;
  Timer? _networkMonitorTimer;

  // 流控制器
  final StreamController<Device> _deviceInfoController =
      StreamController<Device>.broadcast();
  final StreamController<DeviceStatus> _statusController =
      StreamController<DeviceStatus>.broadcast();
  final StreamController<Map<String, dynamic>> _networkInfoController =
      StreamController<Map<String, dynamic>>.broadcast();
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  // 配置
  Duration _statusMonitorInterval = const Duration(seconds: 30);
  Duration _networkMonitorInterval = const Duration(minutes: 1);
  bool _autoRegister = true;

  // Getters
  Device? get currentDevice => _currentDevice;
  bool get isRegistered => _currentDevice != null;
  Stream<Device> get deviceInfoStream => _deviceInfoController.stream;
  Stream<DeviceStatus> get statusStream => _statusController.stream;
  Stream<Map<String, dynamic>> get networkInfoStream =>
      _networkInfoController.stream;
  Stream<String> get errorStream => _errorController.stream;

  /// 初始化设备注册服务
  Future<void> initialize() async {
    try {
      // 加载本地存储的设备信息
      await _loadDeviceFromStorage();

      // 如果没有设备信息，创建新的设备信息
      if (_currentDevice == null) {
        await _createDeviceInfo();
      } else {
        // 更新设备信息
        await _updateDeviceInfo();
      }

      // 设置WebSocket监听器
      _setupWebSocketListeners();

      // 启动监控任务
      _startMonitoring();

      // 自动注册设备
      if (_autoRegister) {
        await registerDevice();
      }

      debugPrint('DeviceRegistrationService initialized');
    } catch (e) {
      debugPrint('Error initializing DeviceRegistrationService: $e');
      _errorController.add('Initialization failed: $e');
    }
  }

  /// 创建设备信息
  Future<void> _createDeviceInfo() async {
    try {
      final deviceId = await _generateDeviceId();
      final deviceName = await _getDeviceName();
      final platform = Platform.operatingSystem;
      final version = await _getDeviceVersion();
      final capabilities = await _getDeviceCapabilities();
      final config = _getDefaultDeviceConfig();
      final networkInfo = await _getNetworkInfo();

      _currentDevice = Device(
        id: deviceId,
        name: deviceName,
        type: DeviceType.terminal,
        platform: platform,
        version: version,
        status: DeviceStatus.offline,
        capabilities: capabilities,
        config: config,
        ipAddress: networkInfo['ipAddress'],
        port: networkInfo['port'],
        lastSeen: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _saveDeviceToStorage();
      _deviceInfoController.add(_currentDevice!);

      debugPrint(
          'Device info created: ${_currentDevice!.name} (${_currentDevice!.id})');
    } catch (e) {
      debugPrint('Error creating device info: $e');
      _errorController.add('Device creation failed: $e');
      rethrow;
    }
  }

  /// 更新设备信息
  Future<void> _updateDeviceInfo() async {
    if (_currentDevice == null) return;

    try {
      final networkInfo = await _getNetworkInfo();
      final capabilities = await _getDeviceCapabilities();

      _currentDevice = _currentDevice!.copyWith(
        capabilities: capabilities,
        ipAddress: networkInfo['ipAddress'],
        port: networkInfo['port'],
        updatedAt: DateTime.now(),
      );

      await _saveDeviceToStorage();
      _deviceInfoController.add(_currentDevice!);

      debugPrint('Device info updated: ${_currentDevice!.name}');
    } catch (e) {
      debugPrint('Error updating device info: $e');
      _errorController.add('Device update failed: $e');
    }
  }

  /// 生成设备ID
  Future<String> _generateDeviceId() async {
    try {
      // 尝试从存储获取已有的设备ID
      final existingId = await _storageService.getString('device_id');
      if (existingId != null && existingId.isNotEmpty) {
        return existingId;
      }

      // 基于设备信息生成唯一ID
      String deviceId;

      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        deviceId =
            'android_${androidInfo.id}_${DateTime.now().millisecondsSinceEpoch}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        deviceId =
            'ios_${iosInfo.identifierForVendor}_${DateTime.now().millisecondsSinceEpoch}';
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfoPlugin.windowsInfo;
        deviceId =
            'windows_${windowsInfo.computerName}_${DateTime.now().millisecondsSinceEpoch}';
      } else if (Platform.isMacOS) {
        final macInfo = await _deviceInfoPlugin.macOsInfo;
        deviceId =
            'macos_${macInfo.computerName}_${DateTime.now().millisecondsSinceEpoch}';
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfoPlugin.linuxInfo;
        deviceId =
            'linux_${linuxInfo.machineId ?? 'unknown'}_${DateTime.now().millisecondsSinceEpoch}';
      } else {
        deviceId = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
      }

      // 保存设备ID
      await _storageService.setString('device_id', deviceId);
      return deviceId;
    } catch (e) {
      debugPrint('Error generating device ID: $e');
      // 如果获取设备信息失败，使用时间戳作为备用ID
      final fallbackId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      await _storageService.setString('device_id', fallbackId);
      return fallbackId;
    }
  }

  /// 获取设备名称
  Future<String> _getDeviceName() async {
    try {
      // 尝试从存储获取自定义名称
      final customName = await _storageService.getString('device_name');
      if (customName != null && customName.isNotEmpty) {
        return customName;
      }

      // 根据平台获取默认名称
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        return '${iosInfo.name} (${iosInfo.model})';
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfoPlugin.windowsInfo;
        return windowsInfo.computerName;
      } else if (Platform.isMacOS) {
        final macInfo = await _deviceInfoPlugin.macOsInfo;
        return macInfo.computerName;
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfoPlugin.linuxInfo;
        return linuxInfo.name;
      } else {
        return 'Unknown Device';
      }
    } catch (e) {
      debugPrint('Error getting device name: $e');
      return 'Terminal Device';
    }
  }

  /// 获取设备版本
  Future<String> _getDeviceVersion() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfoPlugin.androidInfo;
        return 'Android ${androidInfo.version.release}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfoPlugin.iosInfo;
        return 'iOS ${iosInfo.systemVersion}';
      } else if (Platform.isWindows) {
        final windowsInfo = await _deviceInfoPlugin.windowsInfo;
        return 'Windows ${windowsInfo.displayVersion}';
      } else if (Platform.isMacOS) {
        final macInfo = await _deviceInfoPlugin.macOsInfo;
        return 'macOS ${macInfo.osRelease}';
      } else if (Platform.isLinux) {
        final linuxInfo = await _deviceInfoPlugin.linuxInfo;
        return 'Linux ${linuxInfo.version ?? 'Unknown'}';
      } else {
        return 'Unknown';
      }
    } catch (e) {
      debugPrint('Error getting device version: $e');
      return 'Unknown';
    }
  }

  /// 获取设备能力
  Future<DeviceCapabilities> _getDeviceCapabilities() async {
    // 根据平台和设备类型确定能力
    return DeviceCapabilities(
      canCapture: true, // 终端设备支持截图
      canDisplay: true, // 终端设备支持显示
      canRecord: false, // 暂不支持录制
      canStream: false, // 暂不支持流媒体
      supportedFormats: ['png', 'jpg', 'jpeg'],
      maxResolution: '1920x1080',
      hasCamera: Platform.isAndroid || Platform.isIOS,
      hasMicrophone: Platform.isAndroid || Platform.isIOS,
      hasStorage: true,
      hasNetwork: true,
    );
  }

  /// 获取默认设备配置
  DeviceConfig _getDefaultDeviceConfig() {
    return DeviceConfig(
      autoCapture: false,
      captureInterval: 30,
      captureQuality: 80,
      captureFormat: 'png',
      displayTimeout: 0,
      enableNotifications: true,
      enableSound: true,
      enableVibration: Platform.isAndroid || Platform.isIOS,
      theme: 'system',
      language: 'zh-CN',
    );
  }

  /// 获取网络信息
  Future<Map<String, dynamic>> _getNetworkInfo() async {
    try {
      final wifiIP = await _networkInfo.getWifiIP();
      return {
        'ipAddress': wifiIP,
        'port': 0, // 终端设备不需要监听端口
      };
    } catch (e) {
      debugPrint('Error getting network info: $e');
      return {
        'ipAddress': null,
        'port': 0,
      };
    }
  }

  /// 设置WebSocket监听器
  void _setupWebSocketListeners() {
    // 监听连接状态变化
    _webSocketService.statusStream.listen(
      (state) {
        _handleConnectionStateChange(state);
      },
      onError: (error) {
        debugPrint('Connection state stream error: $error');
        _errorController.add('Connection state error: $error');
      },
    );

    // 监听WebSocket消息
    _webSocketService.messageStream.listen(
      (message) {
        _handleWebSocketMessage(message);
      },
      onError: (error) {
        debugPrint('WebSocket message stream error: $error');
        _errorController.add('Message handling error: $error');
      },
    );
  }

  /// 处理连接状态变化
  void _handleConnectionStateChange(WebSocketConnectionState state) {
    if (_currentDevice == null) return;

    DeviceStatus newStatus;
    switch (state) {
      case WebSocketConnectionState.connected:
        newStatus = DeviceStatus.online;
        break;
      case WebSocketConnectionState.connecting:
      case WebSocketConnectionState.reconnecting:
        newStatus = DeviceStatus.connecting;
        break;
      case WebSocketConnectionState.error:
        newStatus = DeviceStatus.error;
        break;
      case WebSocketConnectionState.disconnected:
      default:
        newStatus = DeviceStatus.offline;
        break;
    }

    _updateDeviceStatus(newStatus);
  }

  /// 处理WebSocket消息
  void _handleWebSocketMessage(Map<String, dynamic> message) {
    // 处理与设备注册相关的消息
    final messageType = message['type'] as String?;
    if (messageType == 'device') {
      final action = message['action'] as String?;
      switch (action) {
        case 'register':
          debugPrint('Device registration acknowledged');
          break;
        case 'update':
          debugPrint('Device update acknowledged');
          break;
        default:
          break;
      }
    }
  }

  /// 注册设备
  Future<void> registerDevice() async {
    if (_currentDevice == null) {
      throw Exception('Device info not available');
    }

    try {
      // 更新设备信息
      await _updateDeviceInfo();

      // 初始化WebSocket服务
      await _webSocketService.initialize();

      // 连接到网关
      await _webSocketService.connect(await _getGatewayUrl());

      debugPrint('Device registration initiated: ${_currentDevice!.name}');
    } catch (e) {
      debugPrint('Error registering device: $e');
      _errorController.add('Device registration failed: $e');
      rethrow;
    }
  }

  /// 获取网关URL
  Future<String> _getGatewayUrl() async {
    // 清除可能存在的错误配置，强制使用默认网关
    await _storageService.remove('gateway_url');
    final defaultUrl = AppConstants.defaultGatewayUrl;
    debugPrint('使用网关URL: $defaultUrl');
    return defaultUrl;
  }

  /// 更新设备状态
  void _updateDeviceStatus(DeviceStatus status) {
    if (_currentDevice == null) return;

    _currentDevice = _currentDevice!.copyWith(
      status: status,
      lastSeen: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _statusController.add(status);
    _deviceInfoController.add(_currentDevice!);
    _saveDeviceToStorage();

    // 发送状态更新到网关
    _webSocketService.sendDeviceStatus({
      'status': status.name,
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// 更新设备配置
  Future<void> updateDeviceConfig(DeviceConfig config) async {
    if (_currentDevice == null) return;

    _currentDevice = _currentDevice!.copyWith(
      config: config,
      updatedAt: DateTime.now(),
    );

    await _saveDeviceToStorage();
    _deviceInfoController.add(_currentDevice!);

    // 发送配置更新到网关
    _webSocketService.sendDeviceConfig(config.toJson());
  }

  /// 更新设备能力
  Future<void> updateDeviceCapabilities(DeviceCapabilities capabilities) async {
    if (_currentDevice == null) return;

    _currentDevice = _currentDevice!.copyWith(
      capabilities: capabilities,
      updatedAt: DateTime.now(),
    );

    await _saveDeviceToStorage();
    _deviceInfoController.add(_currentDevice!);

    // 发送能力更新到网关
    _webSocketService.sendDeviceCapabilities(capabilities.toJson());
  }

  /// 更新设备名称
  Future<void> updateDeviceName(String name) async {
    if (_currentDevice == null) return;

    _currentDevice = _currentDevice!.copyWith(
      name: name,
      updatedAt: DateTime.now(),
    );

    await _storageService.setString('device_name', name);
    await _saveDeviceToStorage();
    _deviceInfoController.add(_currentDevice!);

    // 发送设备信息更新到网关
    _webSocketService.updateDeviceInfo(_currentDevice!);
  }

  /// 启动监控
  void _startMonitoring() {
    // 状态监控定时器
    _statusMonitorTimer = Timer.periodic(_statusMonitorInterval, (_) {
      _monitorDeviceStatus();
    });

    // 网络监控定时器
    _networkMonitorTimer = Timer.periodic(_networkMonitorInterval, (_) {
      _monitorNetworkInfo();
    });
  }

  /// 停止监控
  void _stopMonitoring() {
    _statusMonitorTimer?.cancel();
    _networkMonitorTimer?.cancel();
  }

  /// 监控设备状态
  void _monitorDeviceStatus() {
    // 检查设备状态并更新
    if (_webSocketService.isConnected) {
      if (_currentDevice?.status != DeviceStatus.online) {
        _updateDeviceStatus(DeviceStatus.online);
      }
    } else {
      if (_currentDevice?.status != DeviceStatus.offline) {
        _updateDeviceStatus(DeviceStatus.offline);
      }
    }
  }

  /// 监控网络信息
  Future<void> _monitorNetworkInfo() async {
    try {
      final networkInfo = await _getNetworkInfo();
      _networkInfoController.add(networkInfo);

      // 如果IP地址发生变化，更新设备信息
      if (_currentDevice != null &&
          _currentDevice!.ipAddress != networkInfo['ipAddress']) {
        _currentDevice = _currentDevice!.copyWith(
          ipAddress: networkInfo['ipAddress'],
          updatedAt: DateTime.now(),
        );
        await _saveDeviceToStorage();
        _deviceInfoController.add(_currentDevice!);

        // 发送更新到网关
        _webSocketService.updateDeviceInfo(_currentDevice!);
      }
    } catch (e) {
      debugPrint('Error monitoring network info: $e');
    }
  }

  /// 从存储加载设备信息
  Future<void> _loadDeviceFromStorage() async {
    try {
      final deviceJson = await _storageService.getString('device_info');
      if (deviceJson != null) {
        final deviceData = Map<String, dynamic>.from(
            await _storageService.getMap('device_info') ?? {});
        _currentDevice = Device.fromJson(deviceData);
        _deviceInfoController.add(_currentDevice!);
        debugPrint('Device info loaded from storage: ${_currentDevice!.name}');
      }
    } catch (e) {
      debugPrint('Error loading device from storage: $e');
    }
  }

  /// 保存设备信息到存储
  Future<void> _saveDeviceToStorage() async {
    if (_currentDevice == null) return;

    try {
      await _storageService.setMap('device_info', _currentDevice!.toJson());
    } catch (e) {
      debugPrint('Error saving device to storage: $e');
    }
  }

  /// 获取设备统计信息
  Map<String, dynamic> getDeviceStats() {
    if (_currentDevice == null) {
      return {'error': 'Device not initialized'};
    }

    return {
      'deviceId': _currentDevice!.id,
      'deviceName': _currentDevice!.name,
      'platform': _currentDevice!.platform,
      'version': _currentDevice!.version,
      'status': _currentDevice!.status.name,
      'ipAddress': _currentDevice!.ipAddress,
      'isConnected': _webSocketService.isConnected,
      'lastSeen': _currentDevice!.lastSeen.toIso8601String(),
      'createdAt': _currentDevice!.createdAt.toIso8601String(),
      'updatedAt': _currentDevice!.updatedAt.toIso8601String(),
    };
  }

  /// 重置设备信息
  Future<void> resetDevice() async {
    try {
      // 断开WebSocket连接
      await _webSocketService.disconnect();

      // 清除存储的设备信息
      await _storageService.remove('device_id');
      await _storageService.remove('device_info');
      await _storageService.remove('device_name');

      // 重置当前设备
      _currentDevice = null;

      // 重新创建设备信息
      await _createDeviceInfo();

      debugPrint('Device reset completed');
    } catch (e) {
      debugPrint('Error resetting device: $e');
      _errorController.add('Device reset failed: $e');
      rethrow;
    }
  }

  /// 更新配置
  void updateConfig({
    Duration? statusMonitorInterval,
    Duration? networkMonitorInterval,
    bool? autoRegister,
  }) {
    if (statusMonitorInterval != null) {
      _statusMonitorInterval = statusMonitorInterval;
    }
    if (networkMonitorInterval != null) {
      _networkMonitorInterval = networkMonitorInterval;
    }
    if (autoRegister != null) {
      _autoRegister = autoRegister;
    }

    // 重启监控任务
    _stopMonitoring();
    _startMonitoring();
  }

  /// 释放资源
  void dispose() {
    _stopMonitoring();
    _deviceInfoController.close();
    _statusController.close();
    _networkInfoController.close();
    _errorController.close();
  }
}
