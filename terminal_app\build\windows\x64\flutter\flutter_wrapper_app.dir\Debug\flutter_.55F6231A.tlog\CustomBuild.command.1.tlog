^E:\AI_CODEE\IMGCT\TERMINAL_APP\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/terminal_app/windows -BE:/AI_codeE/imgCT/terminal_app/build/windows/x64 --check-stamp-file E:/AI_codeE/imgCT/terminal_app/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
