@echo off
title Gateway Service - Port 7777

echo.
echo ================================================================
echo.
echo    Flutter Image System - Gateway Service v2.0.0
echo.
echo ================================================================
echo.

:: Clean port
echo [INFO] Cleaning port 7777...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :7777 2^>nul') do (
    echo [INFO] Terminating process %%a...
    taskkill /PID %%a /F >nul 2>&1
)

:: Wait for port to be released
timeout /t 1 /nobreak >nul

echo [SUCCESS] Port cleanup completed
echo.
echo [INFO] Starting Gateway service...
echo.

:: Start service
node final_unified_gateway.js

:: If service exits
echo.
echo [WARN] Gateway service has stopped
pause
