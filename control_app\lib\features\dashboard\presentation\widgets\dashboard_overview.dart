import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:getwidget/getwidget.dart';

/// 仪表板概览组件
class DashboardPage extends ConsumerStatefulWidget {
  const DashboardPage({super.key});

  @override
  ConsumerState<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends ConsumerState<DashboardPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 欢迎信息
            Text(
              '欢迎回来！',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              '今天是 ${_formatDate(DateTime.now())}',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 8),

            // 系统状态卡片
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    '在线设备',
                    '8',
                    '/ 10',
                    Icons.devices,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusCard(
                    '活跃连接',
                    '24',
                    '',
                    Icons.wifi,
                    Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    '图片总数',
                    '1,234',
                    '',
                    Icons.photo_library,
                    Colors.purple,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatusCard(
                    '今日传输',
                    '156',
                    '张',
                    Icons.sync,
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // 快速操作
            Text(
              '快速操作',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    '批量传输',
                    '向所有设备发送图片',
                    Icons.send,
                    Colors.blue,
                    () => _onQuickAction('批量传输'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildQuickActionCard(
                    '设备扫描',
                    '扫描网络中的设备',
                    Icons.search,
                    Colors.green,
                    () => _onQuickAction('设备扫描'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildQuickActionCard(
                    '系统设置',
                    '配置系统参数',
                    Icons.settings,
                    Colors.grey,
                    () => _onQuickAction('系统设置'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildQuickActionCard(
                    '日志查看',
                    '查看系统运行日志',
                    Icons.list_alt,
                    Colors.indigo,
                    () => _onQuickAction('日志查看'),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 最近活动
            Text(
              '最近活动',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurface,
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),

            GFCard(
              color: Theme.of(context).colorScheme.surface,
              elevation: 4,
              content: Column(
                children: [
                  _buildActivityItem(
                    '图片传输完成',
                    '向会议室大屏发送了 5 张图片',
                    '2 分钟前',
                    Icons.check_circle,
                    Colors.green,
                  ),
                  Divider(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.24),
                      height: 1),
                  _buildActivityItem(
                    '设备上线',
                    '大厅显示屏重新连接',
                    '15 分钟前',
                    Icons.wifi,
                    Colors.blue,
                  ),
                  const Divider(color: Colors.white24, height: 1),
                  _buildActivityItem(
                    '新设备发现',
                    '发现新设备：接待处平板',
                    '1 小时前',
                    Icons.add_circle,
                    Colors.orange,
                  ),
                  const Divider(color: Colors.white24, height: 1),
                  _buildActivityItem(
                    '系统更新',
                    '系统已更新到版本 1.2.0',
                    '3 小时前',
                    Icons.system_update,
                    Colors.purple,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard(
    String title,
    String value,
    String suffix,
    IconData icon,
    Color color,
  ) {
    return GFCard(
      color: Theme.of(context).colorScheme.surface,
      elevation: 2,
      content: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    color: color,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (suffix.isNotEmpty)
                  Padding(
                    padding: const EdgeInsets.only(left: 4, bottom: 2),
                    child: Text(
                      suffix,
                      style: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.54),
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionCard(
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GFCard(
      color: Theme.of(context).colorScheme.surface,
      elevation: 2,
      content: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                icon,
                color: color,
                size: 28,
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: TextStyle(
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    String title,
    String description,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.7),
                    fontSize: 11,
                  ),
                ),
              ],
            ),
          ),
          Text(
            time,
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.54),
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const months = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月'
    ];

    return '${date.year}年${months[date.month - 1]}${date.day}日 ${weekdays[date.weekday - 1]}';
  }

  void _onQuickAction(String action) {
    print('执行快速操作: $action');
    // 这里可以添加具体的操作逻辑
  }
}
