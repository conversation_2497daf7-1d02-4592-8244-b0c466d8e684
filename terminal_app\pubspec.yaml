name: flutter_imgct_terminal
description: Flutter图片切换系统 - 终端显示应用
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI组件
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  
  # 状态管理
  provider: ^6.1.2
  riverpod: ^2.4.10
  flutter_riverpod: ^2.4.10
  
  # 网络通信
  http: ^1.2.0
  socket_io_client: ^2.0.3+1
  dio: ^5.4.1
  
  # 路由管理
  go_router: ^13.2.0
  
  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # 图片处理和显示
  cached_network_image: ^3.3.0
  photo_view: ^0.14.0
  flutter_image_compress: ^2.1.0
  image: ^4.1.3
  
  # 文件处理
  path_provider: ^2.1.1
  file_picker: ^6.1.1
  
  # 截图功能
  screenshot: ^3.0.0
  
  # 设备信息
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  
  # 网络检测
  connectivity_plus: ^5.0.2
  network_info_plus: ^4.1.0
  
  # 工具库
  intl: ^0.19.0
  uuid: ^4.2.1
  logger: ^2.0.2+1
  
  # 权限管理
  permission_handler: ^11.1.0
  
  # 窗口管理 (Windows)
  window_manager: ^0.3.7
  
  # 系统托盘
  system_tray: ^0.1.1
  
  # 自动启动
  launch_at_startup: ^0.2.2
  
  # UDP通信
  udp: ^5.0.3
  
  # WebSocket通信
  web_socket_channel: ^2.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/config/
  
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700