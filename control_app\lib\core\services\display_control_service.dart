import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/websocket_message.dart';
import '../models/image_info.dart';
import 'logger_service.dart';
import 'storage_service.dart';
import 'websocket_service.dart';
import 'device_management_service.dart';
import 'image_management_service.dart';

/// 显示模式
enum DisplayMode {
  /// 单张图片显示
  single,
  /// 轮播显示
  slideshow,
  /// 同步显示
  synchronized,
}

/// 显示配置
class DisplayConfig {
  final DisplayMode mode;
  final Duration duration;
  final bool autoPlay;
  final bool loop;
  final double opacity;
  final BoxFit fit;
  final Color backgroundColor;
  final bool showControls;
  
  const DisplayConfig({
    this.mode = DisplayMode.single,
    this.duration = const Duration(seconds: 5),
    this.autoPlay = false,
    this.loop = true,
    this.opacity = 1.0,
    this.fit = BoxFit.contain,
    this.backgroundColor = Colors.black,
    this.showControls = true,
  });
  
  factory DisplayConfig.fromJson(Map<String, dynamic> json) {
    return DisplayConfig(
      mode: DisplayMode.values.firstWhere(
        (e) => e.name == json['mode'],
        orElse: () => DisplayMode.single,
      ),
      duration: Duration(milliseconds: json['duration'] as int? ?? 5000),
      autoPlay: json['autoPlay'] as bool? ?? false,
      loop: json['loop'] as bool? ?? true,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1.0,
      fit: BoxFit.values.firstWhere(
        (e) => e.name == json['fit'],
        orElse: () => BoxFit.contain,
      ),
      backgroundColor: Color(json['backgroundColor'] as int? ?? Colors.black.value),
      showControls: json['showControls'] as bool? ?? true,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'mode': mode.name,
      'duration': duration.inMilliseconds,
      'autoPlay': autoPlay,
      'loop': loop,
      'opacity': opacity,
      'fit': fit.name,
      'backgroundColor': backgroundColor.value,
      'showControls': showControls,
    };
  }
  
  DisplayConfig copyWith({
    DisplayMode? mode,
    Duration? duration,
    bool? autoPlay,
    bool? loop,
    double? opacity,
    BoxFit? fit,
    Color? backgroundColor,
    bool? showControls,
  }) {
    return DisplayConfig(
      mode: mode ?? this.mode,
      duration: duration ?? this.duration,
      autoPlay: autoPlay ?? this.autoPlay,
      loop: loop ?? this.loop,
      opacity: opacity ?? this.opacity,
      fit: fit ?? this.fit,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      showControls: showControls ?? this.showControls,
    );
  }
}

/// 显示状态
class DisplayState {
  final bool isDisplaying;
  final String? currentImageId;
  final List<String> playlist;
  final int currentIndex;
  final DisplayConfig config;
  final Map<String, dynamic> metadata;
  
  const DisplayState({
    this.isDisplaying = false,
    this.currentImageId,
    this.playlist = const [],
    this.currentIndex = 0,
    this.config = const DisplayConfig(),
    this.metadata = const {},
  });
  
  DisplayState copyWith({
    bool? isDisplaying,
    String? currentImageId,
    List<String>? playlist,
    int? currentIndex,
    DisplayConfig? config,
    Map<String, dynamic>? metadata,
  }) {
    return DisplayState(
      isDisplaying: isDisplaying ?? this.isDisplaying,
      currentImageId: currentImageId ?? this.currentImageId,
      playlist: playlist ?? this.playlist,
      currentIndex: currentIndex ?? this.currentIndex,
      config: config ?? this.config,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// 显示控制服务
class DisplayControlService {
  static DisplayControlService? _instance;
  static DisplayControlService get instance => _instance ??= DisplayControlService._();
  
  DisplayControlService._();
  
  final StreamController<DisplayState> _stateController = 
      StreamController<DisplayState>.broadcast();
  final StreamController<Map<String, dynamic>> _statusController = 
      StreamController<Map<String, dynamic>>.broadcast();
  final Map<String, DisplayState> _deviceStates = {};
  Timer? _slideshowTimer;
  
  DisplayState _currentState = const DisplayState();
  
  /// 显示状态流
  Stream<DisplayState> get stateStream => _stateController.stream;
  
  /// 当前显示状态
  DisplayState get currentState => _currentState;
  
  /// 获取设备显示状态
  DisplayState? getDeviceState(String deviceId) => _deviceStates[deviceId];
  
  /// 初始化显示控制服务
  Future<void> initialize() async {
    try {
      // 加载保存的配置
      await _loadSavedConfig();
      
      // 监听WebSocket消息
      _listenToWebSocketMessages();
      
      LoggerService.info('Display control service initialized');
    } catch (error) {
      LoggerService.error('Failed to initialize display control service', error);
      rethrow;
    }
  }
  
  /// 监听WebSocket消息
  void _listenToWebSocketMessages() {
    WebSocketService.instance.messageStream.listen((message) {
      if (message.isDisplayMessage) {
        _handleDisplayMessage(message);
      }
    });
  }
  
  /// 处理显示相关消息
  Future<void> _handleDisplayMessage(WebSocketMessage message) async {
    try {
      switch (message.action) {
        case WebSocketMessageAction.displayStart:
          await _handleDisplayStart(message);
          break;
        case WebSocketMessageAction.displayStop:
          await _handleDisplayStop(message);
          break;
        case WebSocketMessageAction.displayNext:
          await _handleDisplayNext(message);
          break;
        case WebSocketMessageAction.displayPrevious:
          await _handleDisplayPrevious(message);
          break;
        case WebSocketMessageAction.displayStatus:
          await _handleDisplayStatus(message);
          break;
        default:
          LoggerService.warning('Unknown display action: ${message.action}');
      }
    } catch (error) {
      LoggerService.error('Failed to handle display message', error);
    }
  }
  
  /// 处理开始显示
  Future<void> _handleDisplayStart(WebSocketMessage message) async {
    final payload = message.payload ?? {};
    final imageId = payload['imageId'] as String?;
    final config = DisplayConfig.fromJson(payload['config'] as Map<String, dynamic>? ?? {});
    
    if (imageId != null && message.deviceId != null) {
      await startSingleDisplay(imageId, [message.deviceId!], config: config);
    }
  }
  
  /// 处理停止显示
  Future<void> _handleDisplayStop(WebSocketMessage message) async {
    final deviceId = message.deviceId;
    if (deviceId == null) return;
    await stopDisplay([deviceId]);
  }
  
  /// 处理下一张
  Future<void> _handleDisplayNext(WebSocketMessage message) async {
    final deviceId = message.deviceId;
    if (deviceId == null) return;
    await nextImage([deviceId]);
  }
  
  /// 处理上一张
  Future<void> _handleDisplayPrevious(WebSocketMessage message) async {
    final deviceId = message.deviceId;
    if (deviceId == null) return;
    await previousImage([deviceId]);
  }
  
  /// 处理显示状态查询
  Future<void> _handleDisplayStatus(WebSocketMessage message) async {
    final deviceId = message.deviceId;
    if (deviceId == null) return;
    
    // 获取设备显示状态
    final status = (message.payload?['status'] as String?) ?? 'unknown';
    
    // 更新设备状态
    if (_deviceStates.containsKey(deviceId)) {
      _deviceStates[deviceId] = _deviceStates[deviceId]!.copyWith(
        metadata: {..._deviceStates[deviceId]!.metadata, 'status': status},
      );
    }
    
    // 通知状态变化
    _statusController.add({
      'deviceId': deviceId,
      'status': status,
    });
  }
  
  /// 显示图片（简化接口）
  Future<void> showImage(
    String imageId, {
    required List<String> deviceIds,
    DisplayConfig config = const DisplayConfig(),
  }) async {
    await startSingleDisplay(imageId, deviceIds, config: config);
  }

  /// 开始单张图片显示
  Future<void> startSingleDisplay(
    String imageId,
    List<String> deviceIds, {
    DisplayConfig config = const DisplayConfig(),
  }) async {
    try {
      LoggerService.info('Starting single display: $imageId on devices: $deviceIds');
      
      // 更新本地状态
      _currentState = _currentState.copyWith(
        isDisplaying: true,
        currentImageId: imageId,
        playlist: [imageId],
        currentIndex: 0,
        config: config,
      );
      
      // 发送显示命令到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displayStart,
          {
            'imageId': imageId,
            'config': config.toJson(),
          },
        );
        
        _deviceStates[deviceId] = _currentState;
      }
      
      _notifyStateChanged();
      
      LoggerService.info('Single display started successfully');
    } catch (error) {
      LoggerService.error('Failed to start single display', error);
      rethrow;
    }
  }
  
  /// 开始轮播显示
  Future<void> startSlideshowDisplay(
    List<String> imageIds,
    List<String> deviceIds, {
    DisplayConfig config = const DisplayConfig(mode: DisplayMode.slideshow),
  }) async {
    try {
      if (imageIds.isEmpty) {
        throw ArgumentError('Image list cannot be empty');
      }
      
      LoggerService.info('Starting slideshow display: ${imageIds.length} images on devices: $deviceIds');
      
      // 停止现有的轮播
      _stopSlideshowTimer();
      
      // 更新本地状态
      _currentState = _currentState.copyWith(
        isDisplaying: true,
        currentImageId: imageIds.first,
        playlist: imageIds,
        currentIndex: 0,
        config: config,
      );
      
      // 发送轮播命令到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displaySlideshow,
          {
            'imageIds': imageIds,
            'config': config.toJson(),
          },
        );
        
        _deviceStates[deviceId] = _currentState;
      }
      
      // 启动轮播定时器
      if (config.autoPlay) {
        _startSlideshowTimer(deviceIds, config.duration);
      }
      
      _notifyStateChanged();
      
      LoggerService.info('Slideshow display started successfully');
    } catch (error) {
      LoggerService.error('Failed to start slideshow display', error);
      rethrow;
    }
  }
  
  /// 开始同步显示
  Future<void> startSynchronizedDisplay(
    String groupId,
    List<String> deviceIds, {
    DisplayConfig config = const DisplayConfig(mode: DisplayMode.synchronized),
  }) async {
    try {
      final imageService = ImageManagementService.instance;
      final group = imageService.groups.firstWhere(
        (g) => g.id == groupId,
        orElse: () => throw ArgumentError('Group not found: $groupId'),
      );
      
      if (group.imageIds.isEmpty) {
        throw ArgumentError('Group has no images: $groupId');
      }
      
      LoggerService.info('Starting synchronized display: group $groupId on devices: $deviceIds');
      
      // 更新本地状态
      _currentState = _currentState.copyWith(
        isDisplaying: true,
        currentImageId: group.imageIds.first,
        playlist: group.imageIds,
        currentIndex: 0,
        config: config,
        metadata: {'groupId': groupId},
      );
      
      // 发送同步显示命令到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displaySync,
          {
            'groupId': groupId,
            'imageIds': group.imageIds,
            'config': config.toJson(),
          },
        );
        
        _deviceStates[deviceId] = _currentState;
      }
      
      _notifyStateChanged();
      
      LoggerService.info('Synchronized display started successfully');
    } catch (error) {
      LoggerService.error('Failed to start synchronized display', error);
      rethrow;
    }
  }
  
  /// 停止显示
  Future<void> stopDisplay(List<String> deviceIds) async {
    try {
      LoggerService.info('Stopping display on devices: $deviceIds');
      
      // 停止轮播定时器
      _stopSlideshowTimer();
      
      // 发送停止命令到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displayStop,
          {},
        );
        
        _deviceStates[deviceId] = const DisplayState();
      }
      
      // 更新本地状态
      _currentState = const DisplayState();
      _notifyStateChanged();
      
      LoggerService.info('Display stopped successfully');
    } catch (error) {
      LoggerService.error('Failed to stop display', error);
      rethrow;
    }
  }
  
  /// 下一张图片
  Future<void> nextImage(List<String> deviceIds) async {
    try {
      if (_currentState.playlist.isEmpty) {
        LoggerService.warning('No playlist available for next image');
        return;
      }
      
      int nextIndex = _currentState.currentIndex + 1;
      
      // 检查是否需要循环
      if (nextIndex >= _currentState.playlist.length) {
        if (_currentState.config.loop) {
          nextIndex = 0;
        } else {
          LoggerService.info('Reached end of playlist, not looping');
          return;
        }
      }
      
      final nextImageId = _currentState.playlist[nextIndex];
      
      LoggerService.info('Moving to next image: $nextImageId (index: $nextIndex)');
      
      // 更新状态
      _currentState = _currentState.copyWith(
        currentImageId: nextImageId,
        currentIndex: nextIndex,
      );
      
      // 发送命令到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displayNext,
          {
            'imageId': nextImageId,
            'index': nextIndex,
          },
        );
        
        _deviceStates[deviceId] = _currentState;
      }
      
      _notifyStateChanged();
    } catch (error) {
      LoggerService.error('Failed to move to next image', error);
    }
  }
  
  /// 上一张图片
  Future<void> previousImage(List<String> deviceIds) async {
    try {
      if (_currentState.playlist.isEmpty) {
        LoggerService.warning('No playlist available for previous image');
        return;
      }
      
      int prevIndex = _currentState.currentIndex - 1;
      
      // 检查是否需要循环
      if (prevIndex < 0) {
        if (_currentState.config.loop) {
          prevIndex = _currentState.playlist.length - 1;
        } else {
          LoggerService.info('Reached beginning of playlist, not looping');
          return;
        }
      }
      
      final prevImageId = _currentState.playlist[prevIndex];
      
      LoggerService.info('Moving to previous image: $prevImageId (index: $prevIndex)');
      
      // 更新状态
      _currentState = _currentState.copyWith(
        currentImageId: prevImageId,
        currentIndex: prevIndex,
      );
      
      // 发送命令到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displayPrevious,
          {
            'imageId': prevImageId,
            'index': prevIndex,
          },
        );
        
        _deviceStates[deviceId] = _currentState;
      }
      
      _notifyStateChanged();
    } catch (error) {
      LoggerService.error('Failed to move to previous image', error);
    }
  }
  
  /// 跳转到指定图片
  Future<void> jumpToImage(int index, List<String> deviceIds) async {
    try {
      if (index < 0 || index >= _currentState.playlist.length) {
        throw ArgumentError('Invalid image index: $index');
      }
      
      final imageId = _currentState.playlist[index];
      
      LoggerService.info('Jumping to image: $imageId (index: $index)');
      
      // 更新状态
      _currentState = _currentState.copyWith(
        currentImageId: imageId,
        currentIndex: index,
      );
      
      // 发送命令到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displayJump,
          {
            'imageId': imageId,
            'index': index,
          },
        );
        
        _deviceStates[deviceId] = _currentState;
      }
      
      _notifyStateChanged();
    } catch (error) {
      LoggerService.error('Failed to jump to image', error);
    }
  }
  
  /// 更新显示配置
  Future<void> updateDisplayConfig(
    DisplayConfig config,
    List<String> deviceIds,
  ) async {
    try {
      LoggerService.info('Updating display config on devices: $deviceIds');
      
      // 更新本地状态
      _currentState = _currentState.copyWith(config: config);
      
      // 发送配置更新到设备
      for (final deviceId in deviceIds) {
        await _sendDisplayCommand(
          deviceId,
          WebSocketMessageAction.displayConfig,
          {
            'config': config.toJson(),
          },
        );
        
        _deviceStates[deviceId] = _currentState;
      }
      
      // 重新启动轮播定时器（如果需要）
      if (config.mode == DisplayMode.slideshow && config.autoPlay) {
        _stopSlideshowTimer();
        _startSlideshowTimer(deviceIds, config.duration);
      } else {
        _stopSlideshowTimer();
      }
      
      // 保存配置
      await _saveConfig(config);
      
      _notifyStateChanged();
      
      LoggerService.info('Display config updated successfully');
    } catch (error) {
      LoggerService.error('Failed to update display config', error);
    }
  }
  
  /// 发送显示命令到设备
  Future<void> _sendDisplayCommand(
    String deviceId,
    WebSocketMessageAction action,
    Map<String, dynamic> payload,
  ) async {
    final message = WebSocketMessage(
      type: WebSocketMessageType.display,
      action: action,
      data: {
        'deviceId': deviceId,
        ...payload,
      },
      timestamp: DateTime.now(),
    );
    
    WebSocketService().sendMessage(message);
  }
  
  /// 启动轮播定时器
  void _startSlideshowTimer(List<String> deviceIds, Duration duration) {
    _slideshowTimer = Timer.periodic(duration, (timer) {
      nextImage(deviceIds);
    });
  }
  
  /// 停止轮播定时器
  void _stopSlideshowTimer() {
    _slideshowTimer?.cancel();
    _slideshowTimer = null;
  }
  
  /// 加载保存的配置
  Future<void> _loadSavedConfig() async {
    try {
      final configJson = await StorageService.instance.getString('display_config');
      if (configJson != null) {
        final configData = jsonDecode(configJson) as Map<String, dynamic>;
        final config = DisplayConfig.fromJson(configData);
        
        _currentState = _currentState.copyWith(config: config);
        LoggerService.info('Loaded saved display config');
      }
    } catch (error) {
      LoggerService.error('Failed to load saved config', error);
    }
  }
  
  /// 保存配置
  Future<void> _saveConfig(DisplayConfig config) async {
    try {
      final configJson = jsonEncode(config.toJson());
      await StorageService.instance.setString('display_config', configJson);
    } catch (error) {
      LoggerService.error('Failed to save config', error);
    }
  }
  
  /// 通知状态变化
  void _notifyStateChanged() {
    _stateController.add(_currentState);
  }
  
  /// 获取所有设备的显示状态
  Map<String, DisplayState> getAllDeviceStates() {
    return Map.from(_deviceStates);
  }
  
  /// 清除设备状态
  void clearDeviceState(String deviceId) {
    _deviceStates.remove(deviceId);
  }
  
  /// 释放资源
  void dispose() {
    _stopSlideshowTimer();
    _stateController.close();
  }
}

/// 显示控制服务提供者
final displayControlServiceProvider = Provider<DisplayControlService>((ref) {
  return DisplayControlService.instance;
});

/// 显示状态流提供者
final displayStateStreamProvider = StreamProvider<DisplayState>((ref) {
  final service = ref.watch(displayControlServiceProvider);
  return service.stateStream;
});

/// 当前显示状态提供者
final currentDisplayStateProvider = Provider<DisplayState>((ref) {
  final service = ref.watch(displayControlServiceProvider);
  return service.currentState;
});