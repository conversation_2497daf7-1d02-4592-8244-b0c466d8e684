import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/device_model.dart';
import '../services/websocket_service.dart';
import '../services/device_management_service.dart';
import '../services/storage_service.dart';
import '../services/logger_service.dart';
import '../config/app_config.dart';

/// WebSocket服务提供者
final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  return WebSocketService();
});

/// 存储服务提供者
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

/// 日志服务提供者
final loggerServiceProvider = Provider<LoggerService>((ref) {
  return LoggerService.instance;
});

/// 设备管理服务提供者
final deviceManagementServiceProvider = Provider<DeviceManagementService>((ref) {
  return DeviceManagementService();
});

/// WebSocket连接状态提供者
final webSocketConnectionStateProvider = StreamProvider<WebSocketConnectionState>((ref) {
  final webSocketService = ref.watch(webSocketServiceProvider);
  return webSocketService.connectionStateStream;
});

/// 设备列表提供者
final devicesProvider = StreamProvider<List<Device>>((ref) {
  final deviceService = ref.watch(deviceManagementServiceProvider);
  return deviceService.devicesStream;
});

/// 设备更新提供者
final deviceUpdateProvider = StreamProvider<Device>((ref) {
  final deviceService = ref.watch(deviceManagementServiceProvider);
  return deviceService.deviceUpdateStream;
});

/// 设备组列表提供者
final deviceGroupsProvider = StreamProvider<List<DeviceGroup>>((ref) {
  final deviceService = ref.watch(deviceManagementServiceProvider);
  return deviceService.groupsStream;
});

/// 网关URL提供者
final gatewayUrlProvider = Provider<String>((ref) {
  return AppConfig.getGatewayWebSocketUrl(
    AppConfig.defaultGatewayHost,
    AppConfig.defaultGatewayPort,
  );
});