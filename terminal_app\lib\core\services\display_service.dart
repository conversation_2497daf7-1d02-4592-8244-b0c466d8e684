import 'dart:async';
import 'dart:io';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';

import '../models/websocket_message.dart';
import 'logger_service.dart';
import 'storage_service.dart';
import 'websocket_service.dart';
import 'screenshot_config_service.dart';
import 'app_config_service.dart';

/// 显示模式
enum DisplayMode {
  /// 单张图片显示
  single,

  /// 轮播显示
  slideshow,

  /// 同步显示
  synchronized,
}

/// 显示配置
class DisplayConfig {
  final DisplayMode mode;
  final Duration duration;
  final bool autoPlay;
  final bool loop;
  final double opacity;
  final BoxFit fit;
  final Color backgroundColor;
  final bool showControls;

  const DisplayConfig({
    this.mode = DisplayMode.single,
    this.duration = const Duration(seconds: 5),
    this.autoPlay = false,
    this.loop = true,
    this.opacity = 1.0,
    this.fit = BoxFit.contain,
    this.backgroundColor = Colors.black,
    this.showControls = false, // 终端默认不显示控制按钮
  });

  factory DisplayConfig.fromJson(Map<String, dynamic> json) {
    return DisplayConfig(
      mode: DisplayMode.values.firstWhere(
        (e) => e.name == json['mode'],
        orElse: () => DisplayMode.single,
      ),
      duration: Duration(milliseconds: json['duration'] as int? ?? 5000),
      autoPlay: json['autoPlay'] as bool? ?? false,
      loop: json['loop'] as bool? ?? true,
      opacity: (json['opacity'] as num?)?.toDouble() ?? 1.0,
      fit: BoxFit.values.firstWhere(
        (e) => e.name == json['fit'],
        orElse: () => BoxFit.contain,
      ),
      backgroundColor:
          Color(json['backgroundColor'] as int? ?? Colors.black.value),
      showControls: json['showControls'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'mode': mode.name,
      'duration': duration.inMilliseconds,
      'autoPlay': autoPlay,
      'loop': loop,
      'opacity': opacity,
      'fit': fit.name,
      'backgroundColor': backgroundColor.value,
      'showControls': showControls,
    };
  }
}

/// 显示状态
class DisplayState {
  final bool isDisplaying;
  final bool isFullscreen;
  final String? currentImageId;
  final List<String> playlist;
  final int currentIndex;
  final DisplayConfig config;
  final Map<String, dynamic> metadata;

  const DisplayState({
    this.isDisplaying = false,
    this.isFullscreen = false,
    this.currentImageId,
    this.playlist = const [],
    this.currentIndex = 0,
    this.config = const DisplayConfig(),
    this.metadata = const {},
  });

  DisplayState copyWith({
    bool? isDisplaying,
    bool? isFullscreen,
    String? currentImageId,
    List<String>? playlist,
    int? currentIndex,
    DisplayConfig? config,
    Map<String, dynamic>? metadata,
  }) {
    return DisplayState(
      isDisplaying: isDisplaying ?? this.isDisplaying,
      isFullscreen: isFullscreen ?? this.isFullscreen,
      currentImageId: currentImageId ?? this.currentImageId,
      playlist: playlist ?? this.playlist,
      currentIndex: currentIndex ?? this.currentIndex,
      config: config ?? this.config,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// 图片信息
class ImageData {
  final String id;
  final String name;
  final Uint8List data;
  final String filePath;
  final Map<String, dynamic> metadata;

  const ImageData({
    required this.id,
    required this.name,
    required this.data,
    required this.filePath,
    this.metadata = const {},
  });
}

/// 显示服务
class DisplayService {
  static DisplayService? _instance;
  static DisplayService get instance => _instance ??= DisplayService._();

  DisplayService._();

  final StreamController<DisplayState> _stateController =
      StreamController<DisplayState>.broadcast();
  final Map<String, ImageData> _imageCache = {};
  final List<String> _localImagePaths = []; // 本地图片路径列表
  Timer? _slideshowTimer;

  DisplayState _currentState = const DisplayState();

  /// 显示状态流
  Stream<DisplayState> get stateStream => _stateController.stream;

  /// 当前显示状态
  DisplayState get currentState => _currentState;

  /// 是否正在显示
  bool get isDisplaying => _currentState.isDisplaying;

  /// 初始化显示服务
  Future<void> initialize() async {
    try {
      // 创建图片缓存目录
      await _ensureImageCacheDirectory();

      // 扫描本地图片文件
      await scanLocalImages();

      // 监听WebSocket消息
      _listenToWebSocketMessages();

      LoggerService.info('Display service initialized');
    } catch (error) {
      LoggerService.error('Failed to initialize display service', error);
      rethrow;
    }
  }

  /// 监听WebSocket消息
  void _listenToWebSocketMessages() {
    WebSocketService.instance.messageStream.listen((message) {
      final messageType = message['type'] as String?;
      if (messageType == 'display' || messageType == 'screen_control') {
        _handleDisplayMessage(message);
      } else if (messageType == 'image' || messageType == 'screenshot') {
        _handleImageMessage(message);
      }
    });
  }

  /// 处理显示相关消息
  Future<void> _handleDisplayMessage(Map<String, dynamic> message) async {
    try {
      final action = message['action'] as String?;
      switch (action) {
        case 'display_start':
          await _handleDisplayStart(message);
          break;
        case 'display_stop':
          await _handleDisplayStop(message);
          break;
        case 'display_next':
          await _handleDisplayNext(message);
          break;
        case 'display_previous':
          await _handleDisplayPrevious(message);
          break;
        case 'display_jump':
          await _handleDisplayJump(message);
          break;
        case 'display_slideshow':
          await _handleDisplaySlideshow(message);
          break;
        case WebSocketMessageAction.displaySync:
          await _handleDisplaySync(message);
          break;
        case WebSocketMessageAction.displayConfig:
          await _handleDisplayConfig(message);
          break;
        case WebSocketMessageAction.displayStatus:
          await _handleDisplayStatus(message);
          break;
        default:
          LoggerService.warning('Unknown display action: $action');
      }
    } catch (error) {
      LoggerService.error('Failed to handle display message', error);

      // 发送错误响应
      final errorMessage = {
        'type': 'display',
        'action': 'error',
        'deviceId': await _getDeviceId(),
        'error': error.toString(),
        'originalAction': message['action'] ?? 'unknown',
        'timestamp': DateTime.now().toIso8601String(),
      };

      await WebSocketService.instance.sendMessage(errorMessage);
    }
  }

  /// 处理图片相关消息
  Future<void> _handleImageMessage(Map<String, dynamic> message) async {
    try {
      final action = message['action'] as String?;
      switch (action) {
        case 'image_sync':
          await _handleImageReceived(message);
          break;
        default:
          LoggerService.warning('Unknown image action: ${message['action']}');
      }
    } catch (error) {
      LoggerService.error('Failed to handle image message', error);
    }
  }

  /// 处理开始显示
  Future<void> _handleDisplayStart(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final imageId = payload['imageId'] as String?;
    final config = DisplayConfig.fromJson(
        payload['config'] as Map<String, dynamic>? ?? {});

    if (imageId == null) {
      throw ArgumentError('Image ID is required for display start');
    }

    LoggerService.info('Starting display: $imageId');

    await startSingleDisplay(imageId, config: config);

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理停止显示
  Future<void> _handleDisplayStop(Map<String, dynamic> message) async {
    LoggerService.info('Stopping display');

    await stopDisplay();

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理下一张
  Future<void> _handleDisplayNext(Map<String, dynamic> message) async {
    LoggerService.info('Moving to next image');

    await nextImage();

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理上一张
  Future<void> _handleDisplayPrevious(Map<String, dynamic> message) async {
    LoggerService.info('Moving to previous image');

    await previousImage();

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理跳转
  Future<void> _handleDisplayJump(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final index = payload['index'] as int?;

    if (index == null) {
      throw ArgumentError('Index is required for display jump');
    }

    LoggerService.info('Jumping to image at index: $index');

    await jumpToImage(index);

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理轮播显示
  Future<void> _handleDisplaySlideshow(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final imageIds = List<String>.from(payload['imageIds'] as List? ?? []);
    final config = DisplayConfig.fromJson(
        payload['config'] as Map<String, dynamic>? ?? {});

    if (imageIds.isEmpty) {
      throw ArgumentError('Image IDs are required for slideshow');
    }

    LoggerService.info('Starting slideshow: ${imageIds.length} images');

    await startSlideshowDisplay(imageIds, config: config);

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理同步显示
  Future<void> _handleDisplaySync(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final groupId = payload['groupId'] as String?;
    final imageIds = List<String>.from(payload['imageIds'] as List? ?? []);
    final config = DisplayConfig.fromJson(
        payload['config'] as Map<String, dynamic>? ?? {});

    if (imageIds.isEmpty) {
      throw ArgumentError('Image IDs are required for synchronized display');
    }

    LoggerService.info(
        'Starting synchronized display: group $groupId, ${imageIds.length} images');

    await startSynchronizedDisplay(imageIds, config: config, groupId: groupId);

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理配置更新
  Future<void> _handleDisplayConfig(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final config = DisplayConfig.fromJson(
        payload['config'] as Map<String, dynamic>? ?? {});

    LoggerService.info('Updating display config');

    await updateDisplayConfig(config);

    // 发送状态响应
    await _sendDisplayStatus();
  }

  /// 处理状态查询
  Future<void> _handleDisplayStatus(Map<String, dynamic> message) async {
    LoggerService.debug('Sending display status');

    await _sendDisplayStatus();
  }

  /// 处理接收到的图片
  Future<void> _handleImageReceived(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final imageId = payload['imageId'] as String?;
    final imageData = payload['imageData'] as String?;
    final metadata = payload['metadata'] as Map<String, dynamic>? ?? {};

    if (imageId == null || imageData == null) {
      LoggerService.warning('Invalid image data received');
      return;
    }

    try {
      final bytes = base64Decode(imageData);
      await _cacheImage(imageId, bytes, metadata);

      LoggerService.info('Image cached: $imageId, size: ${bytes.length} bytes');
    } catch (error) {
      LoggerService.error('Failed to cache image: $imageId', error);
    }
  }

  /// 开始单张图片显示
  Future<void> startSingleDisplay(
    String imageId, {
    DisplayConfig config = const DisplayConfig(),
  }) async {
    try {
      // 确保图片已缓存
      await _ensureImageCached(imageId);

      // 停止现有显示
      await _stopCurrentDisplay();

      // 更新状态
      _currentState = _currentState.copyWith(
        isDisplaying: true,
        isFullscreen: true,
        currentImageId: imageId,
        playlist: [imageId],
        currentIndex: 0,
        config: config,
      );

      _notifyStateChanged();

      LoggerService.info('Single display started: $imageId');
    } catch (error) {
      LoggerService.error('Failed to start single display', error);
      rethrow;
    }
  }

  /// 开始轮播显示
  Future<void> startSlideshowDisplay(
    List<String> imageIds, {
    DisplayConfig config = const DisplayConfig(mode: DisplayMode.slideshow),
  }) async {
    try {
      if (imageIds.isEmpty) {
        throw ArgumentError('Image list cannot be empty');
      }

      // 确保所有图片已缓存
      for (final imageId in imageIds) {
        await _ensureImageCached(imageId);
      }

      // 停止现有显示
      await _stopCurrentDisplay();

      // 更新状态
      _currentState = _currentState.copyWith(
        isDisplaying: true,
        isFullscreen: true,
        currentImageId: imageIds.first,
        playlist: imageIds,
        currentIndex: 0,
        config: config,
      );

      // 启动轮播定时器
      if (config.autoPlay) {
        _startSlideshowTimer(config.duration);
      }

      _notifyStateChanged();

      LoggerService.info(
          'Slideshow display started: ${imageIds.length} images');
    } catch (error) {
      LoggerService.error('Failed to start slideshow display', error);
      rethrow;
    }
  }

  /// 开始同步显示
  Future<void> startSynchronizedDisplay(
    List<String> imageIds, {
    DisplayConfig config = const DisplayConfig(mode: DisplayMode.synchronized),
    String? groupId,
  }) async {
    try {
      if (imageIds.isEmpty) {
        throw ArgumentError('Image list cannot be empty');
      }

      // 确保所有图片已缓存
      for (final imageId in imageIds) {
        await _ensureImageCached(imageId);
      }

      // 停止现有显示
      await _stopCurrentDisplay();

      // 更新状态
      _currentState = _currentState.copyWith(
        isDisplaying: true,
        isFullscreen: true,
        currentImageId: imageIds.first,
        playlist: imageIds,
        currentIndex: 0,
        config: config,
        metadata: groupId != null ? {'groupId': groupId} : {},
      );

      _notifyStateChanged();

      LoggerService.info(
          'Synchronized display started: group $groupId, ${imageIds.length} images');
    } catch (error) {
      LoggerService.error('Failed to start synchronized display', error);
      rethrow;
    }
  }

  /// 停止显示
  Future<void> stopDisplay() async {
    try {
      await _stopCurrentDisplay();

      // 更新状态
      _currentState = const DisplayState();
      _notifyStateChanged();

      LoggerService.info('Display stopped');
    } catch (error) {
      LoggerService.error('Failed to stop display', error);
    }
  }

  /// 下一张图片
  Future<void> nextImage() async {
    try {
      if (_currentState.playlist.isEmpty) {
        LoggerService.warning('No playlist available for next image');
        return;
      }

      int nextIndex = _currentState.currentIndex + 1;

      // 检查是否需要循环
      if (nextIndex >= _currentState.playlist.length) {
        if (_currentState.config.loop) {
          nextIndex = 0;
        } else {
          LoggerService.info('Reached end of playlist, not looping');
          return;
        }
      }

      await _switchToImage(nextIndex);
    } catch (error) {
      LoggerService.error('Failed to move to next image', error);
    }
  }

  /// 上一张图片
  Future<void> previousImage() async {
    try {
      if (_currentState.playlist.isEmpty) {
        LoggerService.warning('No playlist available for previous image');
        return;
      }

      int prevIndex = _currentState.currentIndex - 1;

      // 检查是否需要循环
      if (prevIndex < 0) {
        if (_currentState.config.loop) {
          prevIndex = _currentState.playlist.length - 1;
        } else {
          LoggerService.info('Reached beginning of playlist, not looping');
          return;
        }
      }

      await _switchToImage(prevIndex);
    } catch (error) {
      LoggerService.error('Failed to move to previous image', error);
    }
  }

  /// 跳转到指定图片
  Future<void> jumpToImage(int index) async {
    try {
      if (index < 0 || index >= _currentState.playlist.length) {
        throw ArgumentError('Invalid image index: $index');
      }

      await _switchToImage(index);
      _notifyStateChanged();
    } catch (error) {
      LoggerService.error('Failed to jump to image', error);
    }
  }

  /// 更新显示配置
  Future<void> updateDisplayConfig(DisplayConfig config) async {
    try {
      // 更新状态
      _currentState = _currentState.copyWith(config: config);

      // 重新启动轮播定时器（如果需要）
      if (config.mode == DisplayMode.slideshow && config.autoPlay) {
        _stopSlideshowTimer();
        _startSlideshowTimer(config.duration);
      } else {
        _stopSlideshowTimer();
      }

      _notifyStateChanged();

      LoggerService.info('Display config updated');
    } catch (error) {
      LoggerService.error('Failed to update display config', error);
    }
  }

  /// 切换到指定图片
  Future<void> _switchToImage(int index) async {
    final imageId = _currentState.playlist[index];

    // 确保图片已缓存
    await _ensureImageCached(imageId);

    // 更新状态
    _currentState = _currentState.copyWith(
      currentImageId: imageId,
      currentIndex: index,
    );

    _notifyStateChanged();

    LoggerService.info('Switched to image: $imageId (index: $index)');
  }

  /// 停止当前显示
  Future<void> _stopCurrentDisplay() async {
    _stopSlideshowTimer();

    // 退出全屏模式
    if (_currentState.isFullscreen) {
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    }
  }

  /// 启动轮播定时器
  void _startSlideshowTimer(Duration duration) {
    _slideshowTimer = Timer.periodic(duration, (timer) {
      nextImage();
    });
  }

  /// 停止轮播定时器
  void _stopSlideshowTimer() {
    _slideshowTimer?.cancel();
    _slideshowTimer = null;
  }

  /// 确保图片已缓存
  Future<void> _ensureImageCached(String imageId) async {
    if (!_imageCache.containsKey(imageId)) {
      // 请求图片数据
      await _requestImage(imageId);

      // 等待图片缓存（最多等待5秒）
      int attempts = 0;
      while (!_imageCache.containsKey(imageId) && attempts < 50) {
        await Future.delayed(const Duration(milliseconds: 100));
        attempts++;
      }

      if (!_imageCache.containsKey(imageId)) {
        throw Exception('Failed to cache image: $imageId');
      }
    }
  }

  /// 请求图片数据
  Future<void> _requestImage(String imageId) async {
    final message = WebSocketMessage.imageRequest(
      deviceId: await _getDeviceId(),
      imageId: imageId,
    );

    await WebSocketService.instance.sendMessage(message.toJson());
    LoggerService.debug('Requested image: $imageId');
  }

  /// 缓存图片
  Future<void> _cacheImage(
    String imageId,
    Uint8List data,
    Map<String, dynamic> metadata,
  ) async {
    try {
      // 保存到文件
      final filePath = await _saveImageToCache(imageId, data);

      // 添加到内存缓存
      _imageCache[imageId] = ImageData(
        id: imageId,
        name: metadata['name'] as String? ?? 'Image_$imageId',
        data: data,
        filePath: filePath,
        metadata: metadata,
      );

      LoggerService.debug('Image cached: $imageId');
    } catch (error) {
      LoggerService.error('Failed to cache image: $imageId', error);
      rethrow;
    }
  }

  /// 保存图片到缓存目录
  Future<String> _saveImageToCache(String imageId, Uint8List data) async {
    final directory = await _getImageCacheDirectory();
    final filePath = '${directory.path}/$imageId.png';

    final file = File(filePath);
    await file.writeAsBytes(data);

    return filePath;
  }

  /// 确保图片缓存目录存在
  Future<void> _ensureImageCacheDirectory() async {
    final directory = await _getImageCacheDirectory();
    if (!await directory.exists()) {
      await directory.create(recursive: true);
    }
  }

  /// 获取图片缓存目录
  Future<Directory> _getImageCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    return Directory('${appDir.path}/image_cache');
  }

  /// 发送显示状态
  Future<void> _sendDisplayStatus() async {
    final message = {
      'type': 'display',
      'action': 'status',
      'deviceId': await _getDeviceId(),
      'data': {
        'isDisplaying': _currentState.isDisplaying,
        'currentImageId': _currentState.currentImageId,
        'config': _currentState.config.toJson(),
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    await WebSocketService.instance.sendMessage(message);
  }

  /// 获取设备ID
  Future<String> _getDeviceId() async {
    return await StorageService().getString('device_id') ?? 'unknown';
  }

  /// 通知状态变化
  void _notifyStateChanged() {
    _stateController.add(_currentState);
  }

  /// 获取缓存的图片
  ImageData? getCachedImage(String imageId) {
    return _imageCache[imageId];
  }

  /// 扫描本地图片文件
  Future<void> scanLocalImages() async {
    try {
      _localImagePaths.clear();

      // 获取应用配置
      final appConfigService = AppConfigService.instance;
      final appConfig = await appConfigService.getConfig();

      // 获取截图配置服务中的多文件夹配置
      final screenshotConfig = ScreenshotConfigService.instance;
      await screenshotConfig.initialize();
      final settings = screenshotConfig.currentSettings;
      final folderConfig = settings.folderConfig;

      List<Directory> imageDirs = [];

      // 检查是否使用自定义文件夹（应用配置）
      if (appConfig.useCustomImageFolder == true &&
          appConfig.customImageFolderPath != null &&
          appConfig.customImageFolderPath!.isNotEmpty) {
        // 使用自定义图片文件夹
        imageDirs.add(Directory(appConfig.customImageFolderPath!));
        LoggerService.info('使用自定义图片文件夹: ${appConfig.customImageFolderPath!}');
      } else {
        // 使用截图配置的多文件夹功能
        if (folderConfig.folders.isNotEmpty) {
          // 优先使用启用的文件夹
          final enabledFolders = folderConfig.enabledFolders;
          if (enabledFolders.isNotEmpty) {
            for (final folder in enabledFolders) {
              imageDirs.add(Directory(folder.path));
              LoggerService.info('添加截图文件夹: ${folder.name} -> ${folder.path}');
            }
          } else {
            // 如果没有启用的文件夹，使用默认文件夹
            final defaultFolder = folderConfig.defaultFolder;
            if (defaultFolder != null) {
              imageDirs.add(Directory(defaultFolder.path));
              LoggerService.info(
                  '使用默认截图文件夹: ${defaultFolder.name} -> ${defaultFolder.path}');
            }
          }
        }

        // 如果多文件夹配置为空，使用传统的截图目录
        if (imageDirs.isEmpty) {
          Directory fallbackDir;
          if (settings.customSavePath != null &&
              settings.customSavePath!.isNotEmpty) {
            fallbackDir = Directory(settings.customSavePath!);
          } else {
            final appDir = await getApplicationDocumentsDirectory();
            fallbackDir = Directory('${appDir.path}/screenshots');
          }
          imageDirs.add(fallbackDir);
          LoggerService.info('使用回退截图文件夹: ${fallbackDir.path}');
        }
      }

      // 扫描所有目录
      final allFiles = <File>[];
      final supportedExtensions = ['.png', '.jpg', '.jpeg', '.bmp', '.gif'];

      for (final imageDir in imageDirs) {
        if (await imageDir.exists()) {
          LoggerService.debug('扫描文件夹: ${imageDir.path}');

          try {
            // 扫描文件
            final files = await imageDir
                .list()
                .where((entity) => entity is File)
                .cast<File>()
                .where((file) {
              final extension = file.path.toLowerCase();
              return supportedExtensions.any((ext) => extension.endsWith(ext));
            }).toList();

            allFiles.addAll(files);
            LoggerService.debug('文件夹 ${imageDir.path} 找到 ${files.length} 张图片');
          } catch (error) {
            LoggerService.warning('扫描文件夹失败: ${imageDir.path}', error);
            continue;
          }
        } else {
          LoggerService.warning('图片文件夹不存在: ${imageDir.path}');
        }
      }

      // 按修改时间排序（最新的在前）
      allFiles.sort((a, b) {
        try {
          final aStat = a.statSync();
          final bStat = b.statSync();
          return bStat.modified.compareTo(aStat.modified);
        } catch (error) {
          // 如果获取文件信息失败，使用文件名排序
          return b.path.compareTo(a.path);
        }
      });

      // 处理所有找到的图片文件
      for (final file in allFiles) {
        _localImagePaths.add(file.path);

        // 为本地图片创建缓存条目
        final fileName = file.path.split(Platform.pathSeparator).last;
        final imageId = 'local_$fileName';

        if (!_imageCache.containsKey(imageId)) {
          try {
            final imageData = await file.readAsBytes();
            final stat = await file.stat();

            _imageCache[imageId] = ImageData(
              id: imageId,
              name: fileName,
              data: imageData,
              filePath: file.path,
              metadata: {
                'isLocal': true,
                'fileSize': stat.size,
                'lastModified': stat.modified.toIso8601String(),
                'extension': file.path.split('.').last.toLowerCase(),
                'sourceFolder': file.parent.path,
              },
            );
          } catch (error) {
            LoggerService.warning('无法加载图片: ${file.path}', error);
          }
        }
      }

      LoggerService.info(
          '扫描完成，从 ${imageDirs.length} 个文件夹找到 ${_localImagePaths.length} 张图片');

      // 更新显示状态
      _currentState = _currentState.copyWith(
        playlist: _localImagePaths.map((path) {
          final fileName = path.split(Platform.pathSeparator).last;
          return 'local_$fileName';
        }).toList(),
      );

      _notifyStateChanged();
    } catch (error) {
      LoggerService.error('扫描本地图片失败', error);
    }
  }

  /// 获取本地图片列表
  List<String> getLocalImagePaths() {
    return List.from(_localImagePaths);
  }

  /// 获取本地图片ID列表
  List<String> getLocalImageIds() {
    return _localImagePaths.map((path) {
      final fileName = path.split(Platform.pathSeparator).last;
      return 'local_$fileName';
    }).toList();
  }

  /// 删除指定索引的图片
  Future<bool> deleteImageAt(int index) async {
    try {
      if (index < 0 || index >= _localImagePaths.length) {
        throw ArgumentError('Invalid image index: $index');
      }

      final imagePath = _localImagePaths[index];
      final file = File(imagePath);

      // 删除文件
      if (await file.exists()) {
        await file.delete();
      }

      // 从缓存中移除
      final fileName = imagePath.split(Platform.pathSeparator).last;
      final imageId = 'local_$fileName';
      _imageCache.remove(imageId);

      // 从路径列表中移除
      _localImagePaths.removeAt(index);

      // 更新播放列表
      _currentState = _currentState.copyWith(
        playlist: _localImagePaths.map((path) {
          final name = path.split(Platform.pathSeparator).last;
          return 'local_$name';
        }).toList(),
        // 如果删除的是当前图片或之前的图片，需要调整当前索引
        currentIndex: index < _currentState.currentIndex
            ? _currentState.currentIndex - 1
            : _currentState.currentIndex >= _localImagePaths.length
                ? (_localImagePaths.length > 0
                    ? _localImagePaths.length - 1
                    : 0)
                : _currentState.currentIndex,
      );

      _notifyStateChanged();
      LoggerService.info('Image deleted: $imagePath');
      return true;
    } catch (error) {
      LoggerService.error('Failed to delete image at index $index', error);
      return false;
    }
  }

  /// 重命名指定索引的图片
  Future<bool> renameImageAt(int index, String newName) async {
    try {
      if (index < 0 || index >= _localImagePaths.length) {
        throw ArgumentError('Invalid image index: $index');
      }

      final oldPath = _localImagePaths[index];
      final file = File(oldPath);

      if (!await file.exists()) {
        throw Exception('File does not exist: $oldPath');
      }

      // 获取文件扩展名
      final extension = oldPath.substring(oldPath.lastIndexOf('.'));
      final directory = file.parent;
      final newPath =
          '${directory.path}${Platform.pathSeparator}$newName$extension';

      // 重命名文件
      await file.rename(newPath);

      // 更新缓存
      final oldFileName = oldPath.split(Platform.pathSeparator).last;
      final oldImageId = 'local_$oldFileName';
      final newImageId = 'local_$newName$extension';

      if (_imageCache.containsKey(oldImageId)) {
        final imageData = _imageCache[oldImageId]!;
        _imageCache.remove(oldImageId);
        _imageCache[newImageId] = ImageData(
          id: newImageId,
          name: '$newName$extension',
          data: imageData.data,
          filePath: newPath,
          metadata: imageData.metadata,
        );
      }

      // 更新路径列表
      _localImagePaths[index] = newPath;

      // 更新播放列表
      _currentState = _currentState.copyWith(
        playlist: _localImagePaths.map((path) {
          final name = path.split(Platform.pathSeparator).last;
          return 'local_$name';
        }).toList(),
      );

      _notifyStateChanged();
      LoggerService.info('Image renamed from $oldPath to $newPath');
      return true;
    } catch (error) {
      LoggerService.error('Failed to rename image at index $index', error);
      return false;
    }
  }

  /// 清除图片缓存
  Future<void> clearImageCache() async {
    try {
      _imageCache.clear();

      final directory = await _getImageCacheDirectory();
      if (await directory.exists()) {
        await directory.delete(recursive: true);
        await directory.create(recursive: true);
      }

      LoggerService.info('Image cache cleared');
    } catch (error) {
      LoggerService.error('Failed to clear image cache', error);
    }
  }

  /// 获取缓存统计
  Map<String, dynamic> getCacheStats() {
    return {
      'cachedImages': _imageCache.length,
      'totalSize': _imageCache.values.fold<int>(
        0,
        (sum, image) => sum + image.data.length,
      ),
    };
  }

  /// 释放资源
  void dispose() {
    _stopSlideshowTimer();
    _stateController.close();
    _imageCache.clear();
  }
}

/// 显示服务提供者
final displayServiceProvider = Provider<DisplayService>((ref) {
  return DisplayService.instance;
});

/// 显示状态流提供者
final displayStateStreamProvider = StreamProvider<DisplayState>((ref) {
  final service = ref.watch(displayServiceProvider);
  return service.stateStream;
});

/// 当前显示状态提供者
final currentDisplayStateProvider = Provider<DisplayState>((ref) {
  final service = ref.watch(displayServiceProvider);
  return service.currentState;
});

/// 本地图片列表提供者
final localImageListProvider = Provider<List<String>>((ref) {
  final service = ref.watch(displayServiceProvider);
  return service.getLocalImageIds();
});
