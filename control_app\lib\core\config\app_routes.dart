import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../features/splash/presentation/pages/splash_page.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/devices/presentation/pages/devices_page.dart';
import '../../features/gallery/presentation/pages/gallery_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';

class AppRoutes {
  // 路由路径常量
  static const String splash = '/';
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String devices = '/devices';
  static const String gallery = '/gallery';
  static const String settings = '/settings';

  // 路由名称常量
  static const String splashName = 'splash';
  static const String loginName = 'login';
  static const String dashboardName = 'dashboard';
  static const String devicesName = 'devices';
  static const String galleryName = 'gallery';
  static const String settingsName = 'settings';

  // 创建路由配置
  static GoRouter createRouter() {
    return GoRouter(
      initialLocation: splash,
      debugLogDiagnostics: true,

      // 路由重定向逻辑 - 暂时禁用，由各页面自行处理认证检查
      // redirect: (BuildContext context, GoRouterState state) {
      //   // 由于 Riverpod 的限制，认证检查移至各个页面的 initState 中处理
      //   return null;
      // },

      // 路由定义
      routes: [
        // 启动页
        GoRoute(
          path: splash,
          name: splashName,
          builder: (context, state) => const SplashPage(),
        ),

        // 登录页
        GoRoute(
          path: login,
          name: loginName,
          builder: (context, state) => const LoginPage(),
        ),

        // 仪表板页面
        GoRoute(
          path: dashboard,
          name: dashboardName,
          builder: (context, state) => const DashboardPage(),
        ),

        // 设备管理页面
        GoRoute(
          path: devices,
          name: devicesName,
          builder: (context, state) => const DevicesPage(),
        ),

        // 图片库页面
        GoRoute(
          path: gallery,
          name: galleryName,
          builder: (context, state) => const GalleryPage(),
        ),

        // 设置页面
        GoRoute(
          path: settings,
          name: settingsName,
          builder: (context, state) => const SettingsPage(),
        ),
      ],

      // 错误页面
      errorBuilder: (context, state) => Scaffold(
        appBar: AppBar(
          title: const Text('页面未找到'),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                '页面未找到',
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 8),
              Text(
                '请求的页面不存在: ${state.uri.toString()}',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => context.go(dashboard),
                child: const Text('返回首页'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 路由扩展方法
extension AppRoutesExtension on BuildContext {
  // 导航到启动页
  void goToSplash() => go(AppRoutes.splash);

  // 导航到登录页
  void goToLogin() => go(AppRoutes.login);

  // 导航到仪表板
  void goToDashboard() => go(AppRoutes.dashboard);

  // 导航到设备页面
  void goToDevices() => go(AppRoutes.devices);

  // 导航到图片库
  void goToGallery() => go(AppRoutes.gallery);

  // 导航到设置页面
  void goToSettings() => go(AppRoutes.settings);

  // 推送到指定路由
  void pushToLogin() => push(AppRoutes.login);
  void pushToDevices() => push(AppRoutes.devices);
  void pushToGallery() => push(AppRoutes.gallery);
  void pushToSettings() => push(AppRoutes.settings);

  // 替换当前路由
  void replaceWithLogin() => pushReplacement(AppRoutes.login);
  void replaceWithDashboard() => pushReplacement(AppRoutes.dashboard);

  // 检查当前路由
  bool get isOnSplash =>
      GoRouter.of(this).routerDelegate.currentConfiguration.uri.toString() ==
      AppRoutes.splash;
  bool get isOnLogin =>
      GoRouter.of(this).routerDelegate.currentConfiguration.uri.toString() ==
      AppRoutes.login;
  bool get isOnDashboard =>
      GoRouter.of(this).routerDelegate.currentConfiguration.uri.toString() ==
      AppRoutes.dashboard;
  bool get isOnDevices =>
      GoRouter.of(this).routerDelegate.currentConfiguration.uri.toString() ==
      AppRoutes.devices;
  bool get isOnGallery =>
      GoRouter.of(this).routerDelegate.currentConfiguration.uri.toString() ==
      AppRoutes.gallery;
  bool get isOnSettings =>
      GoRouter.of(this).routerDelegate.currentConfiguration.uri.toString() ==
      AppRoutes.settings;
}

// 路由守卫
class RouteGuard {
  // 检查是否需要认证
  static bool requiresAuth(String location) {
    const publicRoutes = [
      AppRoutes.splash,
      AppRoutes.login,
    ];
    return !publicRoutes.contains(location);
  }

  // 检查是否为公开路由
  static bool isPublicRoute(String location) {
    return !requiresAuth(location);
  }

  // 获取默认重定向路由
  static String getDefaultRoute(bool isAuthenticated) {
    return isAuthenticated ? AppRoutes.dashboard : AppRoutes.login;
  }
}

// 路由动画
class RouteTransitions {
  // 淡入淡出动画
  static Page<T> fadeTransition<T extends Object?>(
    BuildContext context,
    GoRouterState state,
    Widget child,
  ) {
    return CustomTransitionPage<T>(
      key: state.pageKey,
      child: child,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
    );
  }

  // 滑动动画
  static Page<T> slideTransition<T extends Object?>(
    BuildContext context,
    GoRouterState state,
    Widget child, {
    Offset begin = const Offset(1.0, 0.0),
  }) {
    return CustomTransitionPage<T>(
      key: state.pageKey,
      child: child,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: animation.drive(
            Tween(begin: begin, end: Offset.zero).chain(
              CurveTween(curve: Curves.easeInOut),
            ),
          ),
          child: child,
        );
      },
    );
  }

  // 缩放动画
  static Page<T> scaleTransition<T extends Object?>(
    BuildContext context,
    GoRouterState state,
    Widget child,
  ) {
    return CustomTransitionPage<T>(
      key: state.pageKey,
      child: child,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: animation.drive(
            Tween(begin: 0.8, end: 1.0).chain(
              CurveTween(curve: Curves.easeInOut),
            ),
          ),
          child: child,
        );
      },
    );
  }
}
