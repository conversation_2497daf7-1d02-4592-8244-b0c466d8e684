# Flutter图片切换系统 - 开发实施计划

## 1. 项目概述

### 1.1 重构目标
- 将现有的多技术栈系统（React + Electron + Python）统一为Flutter技术栈
- 实现真正的跨平台支持（Windows、macOS、Linux、Android、iOS）
- 提升系统性能和用户体验
- 简化部署和维护流程

### 1.2 核心优势
- **技术统一**：前端控制端和终端应用都使用Flutter开发
- **性能提升**：原生编译，更好的内存管理和渲染性能
- **移动支持**：新增移动端控制功能
- **维护简化**：单一技术栈，降低维护成本

## 2. 开发阶段规划

### 阶段一：基础架构搭建 (2周)

**目标**：建立项目基础结构和核心通信框架

**任务清单**：
- [ ] 创建Flutter项目结构（控制端 + 终端端）
- [ ] 搭建Node.js网关服务
- [ ] 实现WebSocket通信协议
- [ ] 建立设备发现机制
- [ ] 创建基础UI框架和路由

**技术要点**：
```yaml
# pubspec.yaml 核心依赖
dependencies:
  flutter: ^3.16.0
  riverpod: ^2.4.0
  dio: ^5.3.0
  web_socket_channel: ^2.4.0
  window_manager: ^0.3.0
  screen_capturer: ^0.1.0
  system_tray: ^2.0.0
```

### 阶段二：设备管理模块 (2周)

**目标**：实现设备发现、注册和管理功能

**任务清单**：
- [ ] 实现设备自动发现（UDP广播）
- [ ] 开发设备注册和认证
- [ ] 创建设备分组管理
- [ ] 实现设备状态监控
- [ ] 开发设备配置界面

**核心代码结构**：
```
lib/
├── models/
│   ├── device_model.dart
│   └── device_group_model.dart
├── services/
│   ├── device_discovery_service.dart
│   └── device_management_service.dart
├── providers/
│   └── device_provider.dart
└── screens/
    └── device_management_screen.dart
```

### 阶段三：截图控制模块 (2周)

**目标**：实现跨平台截图功能和批量控制

**任务清单**：
- [ ] 集成screen_capturer插件
- [ ] 实现批量截图控制
- [ ] 开发定时截图功能
- [ ] 创建截图预览界面
- [ ] 实现截图质量和区域配置

**关键技术实现**：
```dart
// 截图服务示例
class ScreenCaptureService {
  static Future<String?> captureScreen({
    CaptureMode mode = CaptureMode.screen,
    String? imagePath,
    bool copyToClipboard = false,
  }) async {
    try {
      final capturedData = await screenCapturer.capture(
        mode: mode,
        imagePath: imagePath,
        copyToClipboard: copyToClipboard,
      );
      return capturedData?.imagePath;
    } catch (e) {
      print('截图失败: $e');
      return null;
    }
  }
}
```

### 阶段四：图片管理模块 (1.5周)

**目标**：实现图片存储、浏览和管理功能

**任务清单**：
- [ ] 开发图片上传和存储
- [ ] 创建图片浏览界面
- [ ] 实现图片分类和标签
- [ ] 开发批量操作功能
- [ ] 实现图片搜索和筛选

### 阶段五：同步显示模块 (2周)

**目标**：实现多设备同步显示控制

**任务清单**：
- [ ] 开发全屏显示功能
- [ ] 实现多设备同步控制
- [ ] 创建显示效果配置
- [ ] 开发轮播功能
- [ ] 实现显示状态监控

**显示控制核心代码**：
```dart
class DisplayControlService {
  static Future<void> showImageFullscreen(String imagePath) async {
    await windowManager.setFullScreen(true);
    await windowManager.setAlwaysOnTop(true);
    // 显示图片逻辑
  }
  
  static Future<void> syncDisplayToDevices(
    List<String> deviceIds, 
    String imageUrl
  ) async {
    final message = DisplayControlMessage(
      type: 'display_control',
      action: 'show',
      targetDevices: deviceIds,
      content: DisplayContent(
        imageUrl: imageUrl,
        displayMode: 'fullscreen',
      ),
    );
    
    await WebSocketService.broadcast(message);
  }
}
```

### 阶段六：移动端适配 (1.5周)

**目标**：开发移动端控制应用

**任务清单**：
- [ ] 适配移动端UI布局
- [ ] 实现触控优化
- [ ] 开发快速截图功能
- [ ] 创建移动端设备管理
- [ ] 实现推送通知

### 阶段七：系统集成测试 (1周)

**目标**：完整系统测试和优化

**任务清单**：
- [ ] 端到端功能测试
- [ ] 性能压力测试
- [ ] 跨平台兼容性测试
- [ ] 网络异常处理测试
- [ ] 用户体验优化

## 3. 技术实现细节

### 3.1 项目结构设计

```
flutter_imgct/
├── control_app/                 # 控制端应用
│   ├── lib/
│   │   ├── main.dart
│   │   ├── models/
│   │   │   ├── device_model.dart
│   │   │   ├── image_model.dart
│   │   │   └── task_model.dart
│   │   ├── services/
│   │   │   ├── websocket_service.dart
│   │   │   ├── device_service.dart
│   │   │   ├── capture_service.dart
│   │   │   └── display_service.dart
│   │   ├── providers/
│   │   │   ├── device_provider.dart
│   │   │   ├── image_provider.dart
│   │   │   └── app_state_provider.dart
│   │   ├── screens/
│   │   │   ├── dashboard_screen.dart
│   │   │   ├── device_management_screen.dart
│   │   │   ├── capture_control_screen.dart
│   │   │   ├── image_gallery_screen.dart
│   │   │   └── sync_display_screen.dart
│   │   ├── widgets/
│   │   │   ├── device_card.dart
│   │   │   ├── image_grid.dart
│   │   │   └── status_indicator.dart
│   │   └── utils/
│   │       ├── constants.dart
│   │       └── helpers.dart
│   └── pubspec.yaml
├── terminal_app/                # 终端应用
│   ├── lib/
│   │   ├── main.dart
│   │   ├── services/
│   │   │   ├── capture_service.dart
│   │   │   ├── display_service.dart
│   │   │   └── websocket_client.dart
│   │   ├── screens/
│   │   │   ├── status_screen.dart
│   │   │   └── config_screen.dart
│   │   └── utils/
│   │       └── system_info.dart
│   └── pubspec.yaml
├── gateway_service/             # 网关服务
│   ├── src/
│   │   ├── app.js
│   │   ├── routes/
│   │   ├── services/
│   │   └── models/
│   ├── package.json
│   └── config/
└── shared/                      # 共享代码
    ├── models/
    └── protocols/
```

### 3.2 状态管理架构

使用Riverpod进行状态管理：

```dart
// providers/device_provider.dart
final deviceListProvider = StateNotifierProvider<DeviceListNotifier, List<Device>>(
  (ref) => DeviceListNotifier(),
);

class DeviceListNotifier extends StateNotifier<List<Device>> {
  DeviceListNotifier() : super([]);
  
  void addDevice(Device device) {
    state = [...state, device];
  }
  
  void updateDeviceStatus(String deviceId, DeviceStatus status) {
    state = [
      for (final device in state)
        if (device.id == deviceId)
          device.copyWith(status: status)
        else
          device,
    ];
  }
}
```

### 3.3 网络通信协议

**WebSocket消息格式**：
```dart
class WebSocketMessage {
  final String type;
  final String action;
  final Map<String, dynamic> payload;
  final String timestamp;
  final String messageId;
  
  WebSocketMessage({
    required this.type,
    required this.action,
    required this.payload,
    required this.timestamp,
    required this.messageId,
  });
}
```

**设备发现协议**：
```dart
class DeviceDiscoveryProtocol {
  static const int DISCOVERY_PORT = 8888;
  static const String DISCOVERY_MESSAGE = 'IMGCT_DISCOVERY';
  
  static Future<void> broadcastDiscovery() async {
    final socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 0);
    socket.broadcastEnabled = true;
    
    final data = utf8.encode(DISCOVERY_MESSAGE);
    socket.send(data, InternetAddress('***************'), DISCOVERY_PORT);
  }
}
```

## 4. 部署方案

### 4.1 构建配置

**Windows桌面应用**：
```bash
# 构建Windows应用
flutter build windows --release

# 创建安装包
inno_setup_compiler.exe windows_installer.iss
```

**移动端应用**：
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release
```

### 4.2 自动化部署脚本

```powershell
# deploy.ps1
# Flutter应用构建和部署脚本

Write-Host "开始构建Flutter图片切换系统..."

# 构建控制端应用
Set-Location control_app
flutter clean
flutter pub get
flutter build windows --release

# 构建终端应用
Set-Location ../terminal_app
flutter clean
flutter pub get
flutter build windows --release

# 构建网关服务
Set-Location ../gateway_service
npm install
npm run build

# 创建发布包
Set-Location ..
Compress-Archive -Path "control_app/build/windows/runner/Release/*" -DestinationPath "release/imgct_control.zip"
Compress-Archive -Path "terminal_app/build/windows/runner/Release/*" -DestinationPath "release/imgct_terminal.zip"
Compress-Archive -Path "gateway_service/dist/*" -DestinationPath "release/imgct_gateway.zip"

Write-Host "构建完成！发布包已生成到 release/ 目录"
```

## 5. 测试策略

### 5.1 单元测试
```dart
// test/services/device_service_test.dart
void main() {
  group('DeviceService Tests', () {
    test('should discover devices on network', () async {
      final service = DeviceService();
      final devices = await service.discoverDevices();
      expect(devices, isNotEmpty);
    });
    
    test('should capture screen successfully', () async {
      final service = CaptureService();
      final imagePath = await service.captureScreen();
      expect(imagePath, isNotNull);
    });
  });
}
```

### 5.2 集成测试
```dart
// integration_test/app_test.dart
void main() {
  group('App Integration Tests', () {
    testWidgets('complete workflow test', (tester) async {
      app.main();
      await tester.pumpAndSettle();
      
      // 测试设备发现
      await tester.tap(find.text('扫描设备'));
      await tester.pumpAndSettle();
      
      // 测试截图功能
      await tester.tap(find.text('截图'));
      await tester.pumpAndSettle();
      
      // 验证结果
      expect(find.text('截图成功'), findsOneWidget);
    });
  });
}
```

## 6. 风险评估与应对

### 6.1 技术风险
- **跨平台兼容性**：在不同操作系统上进行充分测试
- **性能问题**：使用性能分析工具优化关键路径
- **网络稳定性**：实现重连机制和错误恢复

### 6.2 进度风险
- **学习曲线**：提前进行Flutter技术培训
- **依赖问题**：选择稳定的第三方包，准备备选方案
- **测试时间**：预留充足的测试和调试时间

## 7. 成功标准

### 7.1 功能标准
- [ ] 支持至少10个设备同时连接
- [ ] 截图响应时间小于3秒
- [ ] 图片同步显示延迟小于1秒
- [ ] 支持Windows、macOS、Linux三个桌面平台
- [ ] 移动端基本功能完整

### 7.2 性能标准
- [ ] 应用启动时间小于5秒
- [ ] 内存占用小于200MB
- [ ] 网络带宽利用率优化
- [ ] 支持7x24小时稳定运行

### 7.3 用户体验标准
- [ ] 界面响应流畅，无明显卡顿
- [ ] 操作逻辑清晰，易于上手
- [ ] 错误提示友好，便于排查问题
- [ ] 支持快捷键和批量操作

通过以上详细的开发实施计划，Flutter重构项目将能够系统性地推进，确保在预定时间内交付高质量的跨平台图片切换控制系统。