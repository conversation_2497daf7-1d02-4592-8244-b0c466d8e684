{"name": "flutter-imgct-controller", "version": "1.0.0", "description": "Flutter Image Control System - Controller Application", "main": "simple_controller.js", "type": "module", "scripts": {"start": "node simple_controller.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["flutter", "image-control", "controller", "websocket"], "author": "AI Assistant", "license": "MIT", "dependencies": {"ws": "^8.18.3"}}