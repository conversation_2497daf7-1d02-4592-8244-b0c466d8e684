import 'dart:io';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import '../../../../core/providers/app_state_provider.dart';
import '../../../../core/providers/device_provider.dart';
import '../../../../core/providers/service_providers.dart';
import '../../../../core/services/display_service.dart' as display_service;
import '../../../../core/services/logger_service.dart';
import '../../../../core/models/device_info.dart';
import '../../../../core/theme/app_theme.dart';

class DisplayPage extends ConsumerStatefulWidget {
  const DisplayPage({super.key});

  @override
  ConsumerState<DisplayPage> createState() => _DisplayPageState();
}

class _DisplayPageState extends ConsumerState<DisplayPage> {
  late display_service.DisplayService _displayService;
  late PageController _pageController;
  bool _isFullscreen = false;
  bool _isSlideshow = false;
  int _currentIndex = 0;
  String? _latestScreenshotPath;
  bool _showScreenshots = false;
  List<String> _localImagePaths = []; // 添加本地图片路径列表

  @override
  void initState() {
    super.initState();
    _initializeDisplayService();
    _initializeScreenshotListener();
    _pageController = PageController(initialPage: _currentIndex);
  }

  void _initializeDisplayService() {
    _displayService = display_service.DisplayService.instance;
    _displayService.initialize();

    // 立即扫描本地图片
    _displayService.scanLocalImages().then((_) {
      if (mounted) {
        // 更新本地图片路径列表
        _localImagePaths = _displayService.getLocalImagePaths();
        setState(() {}); // 刷新界面
      }
    });

    // 监听显示服务状态
    _displayService.stateStream.listen((state) {
      if (mounted) {
        // 更新本地图片路径列表
        final newImagePaths = _displayService.getLocalImagePaths();
        if (newImagePaths.length != _localImagePaths.length ||
            !const ListEquality().equals(newImagePaths, _localImagePaths)) {
          _localImagePaths = newImagePaths;
        }

        setState(() {
          _isSlideshow =
              state.config.mode == display_service.DisplayMode.slideshow;
        });
      }
    });

    // 监听当前图片索引变化
    _displayService.stateStream.listen((state) {
      if (mounted && state.currentIndex != _currentIndex) {
        setState(() {
          _currentIndex = state.currentIndex;
          _pageController.animateToPage(
            state.currentIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        });
      }
    });
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });
  }

  void _startSlideshow() {
    final displayService = ref.read(display_service.displayServiceProvider);
    final localImageIds = displayService.getLocalImageIds();

    if (localImageIds.isNotEmpty) {
      _displayService.startSlideshowDisplay(
        localImageIds,
        config: const display_service.DisplayConfig(
            mode: display_service.DisplayMode.slideshow),
      );
    }
  }

  void _stopSlideshow() {
    _displayService.stopDisplay();
  }

  void _previousImage() {
    _displayService.previousImage();
  }

  void _nextImage() {
    _displayService.nextImage();
  }

  void _jumpToImage(int index) {
    _displayService.jumpToImage(index);
    _pageController.animateToPage(
      index,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _navigateToScreenshot() {
    context.go('/screenshot');
  }

  /// 初始化截图监听
  void _initializeScreenshotListener() {
    final screenshotService = ref.read(screenshotServiceProvider);

    // 监听截图结果
    screenshotService.resultStream.listen((result) {
      if (mounted && result.success && result.filePath != null) {
        setState(() {
          _latestScreenshotPath = result.filePath;
        });

        // 显示截图成功提示
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.camera_alt, color: Colors.white),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text('新截图已保存，点击查看'),
                ),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _toggleScreenshotDisplay();
                  },
                  child: const Text(
                    '查看',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    });
  }

  /// 切换截图显示
  void _toggleScreenshotDisplay() {
    setState(() {
      _showScreenshots = !_showScreenshots;
    });
  }

  @override
  void dispose() {
    _displayService.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isFullscreen) {
      return _buildFullscreenView();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('图片显示'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () async {
              final displayService =
                  ref.read(display_service.displayServiceProvider);
              await displayService.scanLocalImages();
              // 更新本地图片路径列表
              _localImagePaths = displayService.getLocalImagePaths();
              setState(() {}); // 刷新界面
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('图片列表已刷新'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            tooltip: '刷新图片列表',
          ),
          IconButton(
            icon: const Icon(Icons.camera_alt),
            onPressed: _navigateToScreenshot,
            tooltip: '截图服务',
          ),
          IconButton(
            icon: Icon(
              _showScreenshots ? Icons.image : Icons.screenshot,
              color: _latestScreenshotPath != null && _showScreenshots
                  ? Colors.yellow
                  : null,
            ),
            onPressed:
                _latestScreenshotPath != null ? _toggleScreenshotDisplay : null,
            tooltip: _showScreenshots ? '隐藏截图' : '显示截图',
          ),
          IconButton(
            icon: const Icon(Icons.fullscreen),
            onPressed: _toggleFullscreen,
            tooltip: '全屏显示',
          ),
        ],
      ),
      body: Column(
        children: [
          // 控制面板
          _buildControlPanel(),

          // 图片显示区域
          Expanded(
            child: _buildImageDisplay(),
          ),

          // 图片列表
          _buildImageList(),
        ],
      ),
    );
  }

  Widget _buildControlPanel() {
    return Card(
      margin: const EdgeInsets.all(8.0),
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            // 上一张按钮
            IconButton(
              onPressed: _previousImage,
              icon:
                  const Icon(Icons.skip_previous, color: AppTheme.primaryColor),
              tooltip: '上一张',
            ),

            // 播放/暂停按钮
            Container(
              decoration: BoxDecoration(
                color:
                    _isSlideshow ? AppTheme.errorColor : AppTheme.successColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: IconButton(
                onPressed: _isSlideshow ? _stopSlideshow : _startSlideshow,
                icon: Icon(
                  _isSlideshow ? Icons.pause : Icons.play_arrow,
                  color: Colors.white,
                ),
                tooltip: _isSlideshow ? '暂停幻灯片' : '开始幻灯片',
              ),
            ),

            // 下一张按钮
            IconButton(
              onPressed: _nextImage,
              icon: const Icon(Icons.skip_next, color: AppTheme.primaryColor),
              tooltip: '下一张',
            ),

            const Spacer(),

            // 当前图片信息
            Consumer(
              builder: (context, ref, child) {
                final displayService =
                    ref.watch(display_service.displayServiceProvider);
                final localImageIds = displayService.getLocalImageIds();
                final totalImages = localImageIds.length;
                final currentIndex = totalImages > 0 ? (_currentIndex + 1) : 0;

                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.infoColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$currentIndex / $totalImages',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              },
            ),

            const SizedBox(width: 16),

            // 重命名按钮
            OutlinedButton.icon(
              onPressed: _renameCurrentImage,
              icon: const Icon(Icons.edit),
              label: const Text('重命名'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.secondaryColor,
                side: const BorderSide(color: AppTheme.secondaryColor),
              ),
            ),

            const SizedBox(width: 16),

            // 删除按钮
            OutlinedButton.icon(
              onPressed: _deleteCurrentImage,
              icon: const Icon(Icons.delete),
              label: const Text('删除'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
            ),

            const SizedBox(width: 16),

            // 全屏按钮
            OutlinedButton.icon(
              onPressed: _toggleFullscreen,
              icon: const Icon(Icons.fullscreen),
              label: const Text('全屏'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.secondaryColor,
                side: const BorderSide(color: AppTheme.secondaryColor),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageDisplay() {
    // 如果开启了截图显示且有截图，显示截图
    if (_showScreenshots && _latestScreenshotPath != null) {
      return _buildScreenshotDisplay();
    }

    // 显示本地图片列表
    return Consumer(builder: (context, ref, child) {
      final displayService = ref.watch(display_service.displayServiceProvider);
      final localImagePaths = displayService.getLocalImagePaths();

      if (localImagePaths.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.image_not_supported,
                size: 64,
                color: Colors.grey,
              ),
              const SizedBox(height: 16),
              const Text(
                '暂无图片',
                style: TextStyle(color: Colors.grey),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: () async {
                  await displayService.scanLocalImages();
                  setState(() {}); // 刷新界面
                },
                icon: const Icon(Icons.refresh),
                label: const Text('刷新图片'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        );
      }

      return PhotoViewGallery.builder(
        key: ValueKey('gallery_${localImagePaths.length}_${localImagePaths.hashCode}'),
        scrollPhysics: const BouncingScrollPhysics(),
        builder: (BuildContext context, int index) {
          // 确保索引在有效范围内，避免重复显示
          if (index >= localImagePaths.length) {
            return const Center(
              child: Text(
                '图片索引超出范围',
                style: TextStyle(color: Colors.red),
              ),
            );
          }

          final imagePath = localImagePaths[index];

          return PhotoViewGalleryPageOptions(
            imageProvider: FileImage(File(imagePath)),
            initialScale: PhotoViewComputedScale.contained,
            minScale: PhotoViewComputedScale.contained * 0.5,
            maxScale: PhotoViewComputedScale.covered * 2.0,
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red,
                    ),
                    SizedBox(height: 16),
                    Text(
                      '图片加载失败',
                      style: TextStyle(color: Colors.red),
                    ),
                  ],
                ),
              );
            },
          );
        },
        itemCount: localImagePaths.length,
        loadingBuilder: (context, event) => const Center(
          child: CircularProgressIndicator(),
        ),
        pageController: _pageController,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
      );
    });
  }

  /// 构建截图显示
  Widget _buildScreenshotDisplay() {
    if (_latestScreenshotPath == null) {
      return const Center(
        child: Text('暂无截图'),
      );
    }

    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.black,
      child: Stack(
        children: [
          // 截图显示
          Center(
            child: PhotoView(
              imageProvider: FileImage(File(_latestScreenshotPath!)),
              minScale: PhotoViewComputedScale.contained,
              maxScale: PhotoViewComputedScale.covered * 3,
              backgroundDecoration: const BoxDecoration(
                color: Colors.black,
              ),
              loadingBuilder: (context, event) => const Center(
                child: CircularProgressIndicator(),
              ),
              errorBuilder: (context, error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '截图加载失败',
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // 截图信息头
          Positioned(
            top: 16,
            left: 16,
            right: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.screenshot,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '最新截图',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '文件: ${_latestScreenshotPath!.split(Platform.pathSeparator).last}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () async {
                      try {
                        await Process.run(
                            'explorer', ['/select,', _latestScreenshotPath!]);
                      } catch (error) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('打开文件位置失败: $error'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                    icon: const Icon(
                      Icons.folder_open,
                      color: Colors.white,
                      size: 20,
                    ),
                    tooltip: '打开文件位置',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageList() {
    return Consumer(
      builder: (context, ref, child) {
        final displayService =
            ref.watch(display_service.displayServiceProvider);
        final localImagePaths = displayService.getLocalImagePaths();

        if (localImagePaths.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          height: 80,
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: ListView.builder(
            key: ValueKey('thumbnail_list_${localImagePaths.length}_${localImagePaths.hashCode}'),
            scrollDirection: Axis.horizontal,
            itemCount: localImagePaths.length,
            itemBuilder: (context, index) {
              // 确保索引在有效范围内
              if (index >= localImagePaths.length) {
                return const SizedBox.shrink();
              }

              final imagePath = localImagePaths[index];
              final isSelected = index == _currentIndex;

              return GestureDetector(
                onTap: () => _jumpToImage(index),
                onLongPress: () {
                  // 长按显示编辑选项
                  _showImageEditDialog(context, index, displayService);
                },
                child: Container(
                  width: 80,
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Colors.transparent,
                      width: 2,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(6),
                    child: Image.file(
                      File(imagePath),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.error,
                          color: Colors.red,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  /// 显示图片编辑对话框
  void _showImageEditDialog(BuildContext context, int index,
      display_service.DisplayService displayService) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        final localImagePaths = displayService.getLocalImagePaths();
        if (index >= localImagePaths.length) return Container();

        final imagePath = localImagePaths[index];
        final fileName = File(imagePath).uri.pathSegments.last;

        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                fileName,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('重命名'),
                onTap: () {
                  Navigator.of(context).pop();
                  _renameImageAtIndex(index);
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete, color: Colors.red),
                title: const Text('删除', style: TextStyle(color: Colors.red)),
                onTap: () {
                  Navigator.of(context).pop();
                  _deleteImageAtIndex(index);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFullscreenView() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleFullscreen,
        child: Stack(
          children: [
            // 全屏图片显示
            _buildImageDisplay(),

            // 全屏控制按钮
            Positioned(
              top: 40,
              right: 16,
              child: IconButton(
                onPressed: _toggleFullscreen,
                icon: const Icon(
                  Icons.fullscreen_exit,
                  color: Colors.white,
                  size: 32,
                ),
              ),
            ),

            // 底部控制栏
            Positioned(
              bottom: 40,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    IconButton(
                      onPressed: _previousImage,
                      icon: const Icon(
                        Icons.skip_previous,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 32),
                    IconButton(
                      onPressed:
                          _isSlideshow ? _stopSlideshow : _startSlideshow,
                      icon: Icon(
                        _isSlideshow ? Icons.pause : Icons.play_arrow,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                    const SizedBox(width: 32),
                    IconButton(
                      onPressed: _nextImage,
                      icon: const Icon(
                        Icons.skip_next,
                        color: Colors.white,
                        size: 32,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示连接状态对话框
  void _showConnectionDialog(
      BuildContext context, WidgetRef widgetRef, DeviceState deviceState) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                deviceState.isConnected ? Icons.wifi : Icons.wifi_off,
                color: deviceState.isConnected ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(deviceState.isConnected ? '网关连接正常' : '网关连接异常'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildConnectionInfo(
                  '连接状态', deviceState.isConnected ? '已连接' : '未连接'),
              _buildConnectionInfo('网关地址', deviceState.gatewayUrl ?? '未设置'),
              _buildConnectionInfo(
                  '设备ID', _getDeviceId(deviceState.deviceInfo)),
              _buildConnectionInfo(
                  '设备名称', _getDeviceName(deviceState.deviceInfo)),
              _buildConnectionInfo(
                  '最后心跳', _formatDateTime(deviceState.lastHeartbeat)),
              if (deviceState.errorMessage != null)
                _buildConnectionInfo('错误信息', deviceState.errorMessage!,
                    isError: true),
            ],
          ),
          actions: [
            if (!deviceState.isConnected) ...[
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _reconnectToGateway(widgetRef);
                },
                child: const Text('重新连接'),
              ),
            ],
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        );
      },
    );
  }

  /// 构建连接信息行
  Widget _buildConnectionInfo(String label, String value,
      {bool isError = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: isError ? Colors.red : null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取设备ID
  String _getDeviceId(DeviceInfo? deviceInfo) {
    return deviceInfo?.id ?? '未知设备';
  }

  /// 获取设备名称
  String _getDeviceName(DeviceInfo? deviceInfo) {
    if (deviceInfo?.name != null &&
        deviceInfo!.name.isNotEmpty &&
        deviceInfo.name != 'unknown') {
      return deviceInfo.name;
    }

    try {
      final hostname = Platform.localHostname.isNotEmpty
          ? Platform.localHostname
          : 'Windows终端';
      return hostname;
    } catch (e) {
      return 'Windows终端设备';
    }
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inSeconds < 60) {
      return '${difference.inSeconds}秒前';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else {
      return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }



  Future<void> _reconnectToGateway(WidgetRef widgetRef) async {
    try {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 12),
                Text('正在重新连接到网关...'),
              ],
            ),
            duration: Duration(seconds: 3),
          ),
        );
      }

      final websocketService = widgetRef.read(webSocketServiceProvider);
      final deviceRegistrationService =
          widgetRef.read(deviceRegistrationServiceProvider);
      final appStateNotifier = widgetRef.read(appStateProvider.notifier);
      final deviceNotifier = widgetRef.read(deviceProvider.notifier);

      deviceNotifier.setConnected(false);
      appStateNotifier.setConnectionStatus(ConnectionStatus.connecting);

      await websocketService.disconnect();
      await Future.delayed(const Duration(seconds: 1));

      try {
        await deviceRegistrationService.registerDevice();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('重新连接成功！'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (registerError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('重新连接失败，请检查网关服务是否正常运行'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (error) {
      LoggerService.error('重新连接失败', error);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('重新连接失败: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 重命名当前图片
  Future<void> _renameCurrentImage() async {
    if (_localImagePaths.isEmpty) return;

    final currentPath = _displayService.getLocalImagePaths()[_currentIndex];
    final currentName = File(currentPath).uri.pathSegments.last;
    final nameWithoutExtension =
        currentName.substring(0, currentName.lastIndexOf('.'));

    final controller = TextEditingController(text: nameWithoutExtension);

    final newName = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('重命名图片'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: '新文件名',
              hintText: '请输入新的文件名',
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(controller.text),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );

    if (newName != null &&
        newName.isNotEmpty &&
        newName != nameWithoutExtension) {
      final success =
          await _displayService.renameImageAt(_currentIndex, newName);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('图片已重命名'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('重命名图片失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 重命名指定索引的图片
  Future<void> _renameImageAtIndex(int index) async {
    final displayService = ref.read(display_service.displayServiceProvider);
    final localImagePaths = displayService.getLocalImagePaths();

    if (index >= localImagePaths.length) return;

    final currentPath = localImagePaths[index];
    final currentName = File(currentPath).uri.pathSegments.last;
    final nameWithoutExtension =
        currentName.substring(0, currentName.lastIndexOf('.'));

    final controller = TextEditingController(text: nameWithoutExtension);

    final newName = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('重命名图片'),
          content: TextField(
            controller: controller,
            decoration: const InputDecoration(
              labelText: '新文件名',
              hintText: '请输入新的文件名',
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(controller.text),
              child: const Text('确定'),
            ),
          ],
        );
      },
    );

    if (newName != null &&
        newName.isNotEmpty &&
        newName != nameWithoutExtension) {
      final success = await displayService.renameImageAt(index, newName);
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('图片已重命名'),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {}); // 刷新界面
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('重命名图片失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 删除指定索引的图片
  Future<void> _deleteImageAtIndex(int index) async {
    final displayService = ref.read(display_service.displayServiceProvider);
    final localImagePaths = displayService.getLocalImagePaths();

    if (index >= localImagePaths.length) return;

    final currentPath = localImagePaths[index];
    final fileName = File(currentPath).uri.pathSegments.last;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除 "$fileName" 吗？此操作不可撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      final success = await displayService.deleteImageAt(index);
      if (success) {
        // 更新本地图片路径列表
        _localImagePaths = displayService.getLocalImagePaths();

        // 更新当前索引
        if (index < _currentIndex) {
          _currentIndex--;
        } else if (index == _currentIndex &&
            _currentIndex >= displayService.getLocalImageIds().length) {
          _currentIndex = displayService.getLocalImageIds().length > 0
              ? displayService.getLocalImageIds().length - 1
              : 0;
        }

        // 如果还有图片，更新 PageController
        if (_localImagePaths.isNotEmpty &&
            _currentIndex < _localImagePaths.length) {
          _pageController.animateToPage(
            _currentIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('图片已删除'),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {}); // 刷新界面
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('删除图片失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 删除当前图片
  Future<void> _deleteCurrentImage() async {
    final displayService = ref.read(display_service.displayServiceProvider);
    _localImagePaths = displayService.getLocalImagePaths();

    if (_localImagePaths.isEmpty) return;

    final currentPath = _localImagePaths[_currentIndex];
    final fileName = File(currentPath).uri.pathSegments.last;

    final confirm = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除 "$fileName" 吗？此操作不可撤销。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );

    if (confirm == true) {
      final success = await displayService.deleteImageAt(_currentIndex);
      if (success) {
        // 更新本地图片路径列表
        _localImagePaths = displayService.getLocalImagePaths();

        // 更新当前索引
        if (_currentIndex >= _localImagePaths.length &&
            _localImagePaths.isNotEmpty) {
          _currentIndex = _localImagePaths.length - 1;
        } else if (_localImagePaths.isEmpty) {
          _currentIndex = 0;
        }

        // 如果还有图片，更新 PageController
        if (_localImagePaths.isNotEmpty &&
            _currentIndex < _localImagePaths.length) {
          _pageController.animateToPage(
            _currentIndex,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        }

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('图片已删除'),
            backgroundColor: Colors.green,
          ),
        );
        setState(() {}); // 刷新界面
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('删除图片失败'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// 显示状态枚举
enum DisplayStatus {
  idle,
  loading,
  displaying,
  slideshow,
  error,
}
