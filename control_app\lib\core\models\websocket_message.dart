import 'dart:convert';

/// WebSocket消息类型枚举
enum WebSocketMessageType {
  device,     // 设备相关消息
  capture,    // 截图相关消息
  display,    // 显示相关消息
  heartbeat,  // 心跳消息
  heartbeat_ack, // 心跳确认消息
  discovery,  // 设备发现消息
  sync,       // 同步消息
  error,      // 错误消息
  auth,       // 认证消息
  connection, // 连接相关消息
  device_registered, // 设备注册成功消息
  welcome,    // 欢迎消息
  ping,       // ping消息
  pong,       // pong消息
}

/// WebSocket消息动作枚举
enum WebSocketMessageAction {
  // 通用动作
  register,   // 注册
  update,     // 更新
  status,     // 状态
  request,    // 请求
  response,   // 响应
  result,     // 结果
  control,    // 控制
  
  // 设备相关
  list,       // 列表
  connect,    // 连接
  disconnect, // 断开连接
  
  // 设备控制相关
  powerOn,    // 开机
  powerOff,   // 关机
  restart,    // 重启
  executeCommand, // 执行远程命令
  getStatus,  // 获取设备状态
  batchOperation, // 批量操作
  
  // 截图相关
  start,      // 开始
  stop,       // 停止
  complete,   // 完成
  failed,     // 失败
  
  // 心跳相关
  ping,       // 心跳请求
  pong,       // 心跳响应
  
  // 认证相关
  login,      // 登录
  logout,     // 登出
  verify,     // 验证
  
  // 连接相关
  welcome,    // 欢迎消息
  
  // 同步相关
  sync,       // 同步
  imageSync,  // 图片同步
  imageRequest, // 图片请求
  imageReceived, // 图片接收
  
  // 发现相关
  announce,   // 公告
  discover,   // 发现
  scan,       // 扫描
  
  // 显示相关
  displayStart,     // 开始显示
  displayStop,      // 停止显示
  displayNext,      // 下一张
  displayPrevious,  // 上一张
  displayJump,      // 跳转到指定图片
  displayConfig,    // 显示配置
  displayPause,     // 暂停
  displayResume,    // 恢复
  displaySlideshow, // 轮播显示
  displaySync,      // 同步显示
  displayStatus,    // 显示状态
}

/// WebSocket消息接口
abstract class IWebSocketMessage {
  WebSocketMessageType get type;
  WebSocketMessageAction get action;
  Map<String, dynamic>? get data;
  DateTime get timestamp;
  String? get messageId;
  String? get correlationId;
  
  Map<String, dynamic> toJson();
}

/// WebSocket消息实现类
class WebSocketMessage implements IWebSocketMessage {
  @override
  final WebSocketMessageType type;
  
  @override
  final WebSocketMessageAction action;
  
  @override
  final Map<String, dynamic>? data;
  
  @override
  final DateTime timestamp;
  
  @override
  final String? messageId;
  
  @override
  final String? correlationId;
  
  const WebSocketMessage({
    required this.type,
    required this.action,
    this.data,
    required this.timestamp,
    this.messageId,
    this.correlationId,
  });

  /// 从JSON创建消息
  factory WebSocketMessage.fromJson(Map<String, dynamic> json) {
    return WebSocketMessage(
      type: _parseMessageType(json['type']),
      action: _parseMessageAction(json['action']),
      data: json['data'] as Map<String, dynamic>?,
      timestamp: _parseTimestamp(json['timestamp']),
      messageId: json['messageId'] as String?,
      correlationId: json['correlationId'] as String?,
    );
  }

  /// 从JSON字符串创建消息
  factory WebSocketMessage.fromJsonString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return WebSocketMessage.fromJson(json);
  }

  /// 转换为JSON
  @override
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'action': action.name,
      'data': data,
      'timestamp': timestamp.toIso8601String(),
      if (messageId != null) 'messageId': messageId,
      if (correlationId != null) 'correlationId': correlationId,
    };
  }

  /// 转换为JSON字符串
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// 创建设备注册消息
  factory WebSocketMessage.deviceRegister({
    required Map<String, dynamic> deviceInfo,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.device,
      action: WebSocketMessageAction.register,
      data: deviceInfo,
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建设备状态更新消息
  factory WebSocketMessage.deviceStatus({
    required String deviceId,
    required Map<String, dynamic> status,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.device,
      action: WebSocketMessageAction.status,
      data: {
        'deviceId': deviceId,
        'status': status,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建截图请求消息
  factory WebSocketMessage.captureRequest({
    required String deviceId,
    Map<String, dynamic>? options,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.capture,
      action: WebSocketMessageAction.request,
      data: {
        'deviceId': deviceId,
        'options': options ?? {},
      },
      timestamp: DateTime.now(),
      messageId: messageId,
      correlationId: correlationId,
    );
  }

  /// 创建截图结果消息
  factory WebSocketMessage.captureResult({
    required String deviceId,
    required bool success,
    String? imagePath,
    String? error,
    Map<String, dynamic>? metadata,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.capture,
      action: success ? WebSocketMessageAction.complete : WebSocketMessageAction.failed,
      data: {
        'deviceId': deviceId,
        'success': success,
        if (imagePath != null) 'imagePath': imagePath,
        if (error != null) 'error': error,
        if (metadata != null) 'metadata': metadata,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
      correlationId: correlationId,
    );
  }

  /// 创建显示控制消息
  factory WebSocketMessage.displayControl({
    required String deviceId,
    required String imagePath,
    Map<String, dynamic>? options,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.control,
      data: {
        'deviceId': deviceId,
        'imagePath': imagePath,
        'options': options ?? {},
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建显示状态消息
  factory WebSocketMessage.displayStatus({
    required String deviceId,
    required bool isDisplaying,
    String? currentImageId,
    Map<String, dynamic>? config,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.status,
      data: {
        'deviceId': deviceId,
        'isDisplaying': isDisplaying,
        if (currentImageId != null) 'currentImageId': currentImageId,
        if (config != null) 'config': config,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建心跳消息
  factory WebSocketMessage.heartbeat({
    required WebSocketMessageAction action, // ping 或 pong
    Map<String, dynamic>? data,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.heartbeat,
      action: action,
      data: data ?? {'timestamp': DateTime.now().millisecondsSinceEpoch},
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建设备发现消息
  factory WebSocketMessage.deviceDiscovery({
    required WebSocketMessageAction action,
    Map<String, dynamic>? data,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.discovery,
      action: action,
      data: data ?? {},
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建认证消息
  factory WebSocketMessage.auth({
    required WebSocketMessageAction action,
    Map<String, dynamic>? authData,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.auth,
      action: action,
      data: authData ?? {},
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建错误消息
  factory WebSocketMessage.error({
    required String message,
    String? code,
    Map<String, dynamic>? details,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.error,
      action: WebSocketMessageAction.response,
      data: {
        'message': message,
        if (code != null) 'code': code,
        if (details != null) 'details': details,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
      correlationId: correlationId,
    );
  }

  /// 创建图片同步消息
  factory WebSocketMessage.imageSync({
    required String deviceId,
    required String imageId,
    required String imageData,
    Map<String, dynamic>? metadata,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.capture,
      action: WebSocketMessageAction.imageSync,
      data: {
        'deviceId': deviceId,
        'imageId': imageId,
        'imageData': imageData,
        if (metadata != null) 'metadata': metadata,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建设备开机消息
  factory WebSocketMessage.devicePowerOn({
    required String deviceId,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.device,
      action: WebSocketMessageAction.powerOn,
      data: {
        'deviceId': deviceId,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
      correlationId: correlationId,
    );
  }

  /// 创建设备关机消息
  factory WebSocketMessage.devicePowerOff({
    required String deviceId,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.device,
      action: WebSocketMessageAction.powerOff,
      data: {
        'deviceId': deviceId,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
      correlationId: correlationId,
    );
  }

  /// 创建设备重启消息
  factory WebSocketMessage.deviceRestart({
    required String deviceId,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.device,
      action: WebSocketMessageAction.restart,
      data: {
        'deviceId': deviceId,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
      correlationId: correlationId,
    );
  }

  /// 创建远程命令执行消息
  factory WebSocketMessage.executeCommand({
    required String deviceId,
    required String command,
    required Map<String, dynamic> parameters,
    String? messageId,
  }) {
    return WebSocketMessage(
      messageId: messageId ?? _generateMessageId(),
      type: WebSocketMessageType.deviceControl,
      action: WebSocketMessageAction.executeCommand,
      deviceId: deviceId,
      data: {
        'command': command,
        'parameters': parameters,
      },
      timestamp: DateTime.now(),
    );
  }

  /// 创建批量操作消息
  factory WebSocketMessage.batchOperation({
    required List<String> deviceIds,
    required WebSocketMessageAction operation,
    Map<String, dynamic>? parameters,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.device,
      action: WebSocketMessageAction.batchOperation,
      data: {
        'deviceIds': deviceIds,
        'operation': operation.name,
        if (parameters != null) 'parameters': parameters,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
      correlationId: correlationId,
    );
  }

  /// 创建显示开始消息
  factory WebSocketMessage.displayStart({
    required String deviceId,
    required String imageId,
    Map<String, dynamic>? config,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.displayStart,
      data: {
        'deviceId': deviceId,
        'imageId': imageId,
        'config': config ?? {},
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建显示停止消息
  factory WebSocketMessage.displayStop({
    required String deviceId,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.displayStop,
      data: {
        'deviceId': deviceId,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建显示下一张消息
  factory WebSocketMessage.displayNext({
    required String deviceId,
    String? imageId,
    int? index,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.displayNext,
      data: {
        'deviceId': deviceId,
        if (imageId != null) 'imageId': imageId,
        if (index != null) 'index': index,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建显示上一张消息
  factory WebSocketMessage.displayPrevious({
    required String deviceId,
    String? imageId,
    int? index,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.displayPrevious,
      data: {
        'deviceId': deviceId,
        if (imageId != null) 'imageId': imageId,
        if (index != null) 'index': index,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建显示跳转消息
  factory WebSocketMessage.displayJump({
    required String deviceId,
    required String imageId,
    required int index,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.displayJump,
      data: {
        'deviceId': deviceId,
        'imageId': imageId,
        'index': index,
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建轮播显示消息
  factory WebSocketMessage.displaySlideshow({
    required String deviceId,
    required List<String> imageIds,
    Map<String, dynamic>? config,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.displaySlideshow,
      data: {
        'deviceId': deviceId,
        'imageIds': imageIds,
        'config': config ?? {},
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 创建同步显示消息
  factory WebSocketMessage.displaySync({
    required String deviceId,
    required String groupId,
    required List<String> imageIds,
    Map<String, dynamic>? config,
    String? messageId,
  }) {
    return WebSocketMessage(
      type: WebSocketMessageType.display,
      action: WebSocketMessageAction.displaySync,
      data: {
        'deviceId': deviceId,
        'groupId': groupId,
        'imageIds': imageIds,
        'config': config ?? {},
      },
      timestamp: DateTime.now(),
      messageId: messageId,
    );
  }

  /// 判断是否为设备消息
  bool get isDeviceMessage => type == WebSocketMessageType.device;

  /// 判断是否为截图消息
  bool get isCaptureMessage => type == WebSocketMessageType.capture;

  /// 判断是否为显示消息
  bool get isDisplayMessage => type == WebSocketMessageType.display;

  /// 判断是否为心跳消息
  bool get isHeartbeatMessage => type == WebSocketMessageType.heartbeat;

  /// 判断是否为发现消息
  bool get isDiscoveryMessage => type == WebSocketMessageType.discovery;

  /// 判断是否为错误消息
  bool get isErrorMessage => type == WebSocketMessageType.error;

  /// 判断是否为认证消息
  bool get isAuthMessage => type == WebSocketMessageType.auth;

  /// 判断是否为图片消息
  bool get isImageMessage => type == WebSocketMessageType.capture && 
      (action == WebSocketMessageAction.imageSync || 
       action == WebSocketMessageAction.imageRequest || 
       action == WebSocketMessageAction.imageReceived);

  /// 获取payload数据（兼容性别名）
  Map<String, dynamic>? get payload => data;

  /// 获取设备ID
  String? get deviceId => data?['deviceId'] as String?;

  /// 判断是否为请求消息
  bool get isRequest => action == WebSocketMessageAction.request;

  /// 判断是否为响应消息
  bool get isResponse => action == WebSocketMessageAction.response;

  /// 判断是否为结果消息
  bool get isResult => action == WebSocketMessageAction.result;

  /// 获取错误信息（如果存在）
  String? get errorMessage => data?['message'] as String?;

  /// 复制消息并修改部分属性
  WebSocketMessage copyWith({
    WebSocketMessageType? type,
    WebSocketMessageAction? action,
    Map<String, dynamic>? data,
    DateTime? timestamp,
    String? messageId,
    String? correlationId,
  }) {
    return WebSocketMessage(
      type: type ?? this.type,
      action: action ?? this.action,
      data: data ?? this.data,
      timestamp: timestamp ?? this.timestamp,
      messageId: messageId ?? this.messageId,
      correlationId: correlationId ?? this.correlationId,
    );
  }

  @override
  String toString() {
    return 'WebSocketMessage(type: $type, action: $action, data: $data, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WebSocketMessage &&
        other.type == type &&
        other.action == action &&
        other.messageId == messageId &&
        other.correlationId == correlationId;
  }

  @override
  int get hashCode {
    return Object.hash(type, action, messageId, correlationId);
  }
}

/// 解析消息类型
WebSocketMessageType _parseMessageType(dynamic value) {
  if (value is String) {
    for (final type in WebSocketMessageType.values) {
      if (type.name == value) {
        return type;
      }
    }
  }
  throw ArgumentError('Invalid message type: $value');
}

/// 解析消息动作
WebSocketMessageAction _parseMessageAction(dynamic value) {
  if (value is String) {
    for (final action in WebSocketMessageAction.values) {
      if (action.name == value) {
        return action;
      }
    }
  }
  // 如果action为null或无效，返回默认的response动作
  if (value == null) {
    return WebSocketMessageAction.response;
  }
  throw ArgumentError('Invalid message action: $value');
}

/// 解析时间戳
DateTime _parseTimestamp(dynamic value) {
  if (value == null) {
    return DateTime.now();
  }
  
  if (value is String) {
    return DateTime.parse(value);
  }
  
  if (value is int) {
    return DateTime.fromMillisecondsSinceEpoch(value);
  }
  
  throw ArgumentError('Invalid timestamp format: $value');
}

/// 消息验证器
class MessageValidator {
  /// 验证消息格式
  static bool isValidMessage(Map<String, dynamic> json) {
    try {
      // 检查必需字段
      if (!json.containsKey('type') || !json.containsKey('action') || !json.containsKey('timestamp')) {
        return false;
      }
      
      // 验证类型和动作
      _parseMessageType(json['type']);
      _parseMessageAction(json['action']);
      
      // 验证时间戳
      DateTime.parse(json['timestamp']);
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 验证设备消息
  static bool isValidDeviceMessage(WebSocketMessage message) {
    if (!message.isDeviceMessage) return false;
    
    switch (message.action) {
      case WebSocketMessageAction.register:
        return message.data != null && message.data!.containsKey('deviceId');
      case WebSocketMessageAction.status:
        return message.data != null && 
               message.data!.containsKey('deviceId') && 
               message.data!.containsKey('status');
      default:
        return true;
    }
  }

  /// 验证截图消息
  static bool isValidCaptureMessage(WebSocketMessage message) {
    if (!message.isCaptureMessage) return false;
    
    switch (message.action) {
      case WebSocketMessageAction.request:
        return message.data != null && message.data!.containsKey('deviceId');
      case WebSocketMessageAction.complete:
      case WebSocketMessageAction.failed:
        return message.data != null && message.data!.containsKey('deviceId');
      default:
        return true;
    }
  }

  /// 验证显示消息
  static bool isValidDisplayMessage(WebSocketMessage message) {
    if (!message.isDisplayMessage) return false;
    
    switch (message.action) {
      case WebSocketMessageAction.control:
        return message.data != null && 
               message.data!.containsKey('deviceId') && 
               message.data!.containsKey('imagePath');
      default:
        return true;
    }
  }
}