// Device related types
export interface Device {
  id: string;
  name: string;
  ip_address: string;
  platform: string;
  version: string;
  capabilities: string[];
  status: 'online' | 'offline' | 'busy' | 'discovered' | 'removed';
  last_seen: Date;
  config?: DeviceConfig;
  group_id?: string;
  created_at: Date;
}

export interface DeviceConfig {
  captureQuality?: number;
  autoCapture?: boolean;
  captureInterval?: number;
  displayMode?: 'fullscreen' | 'fit' | 'stretch';
  resolution?: {
    width: number;
    height: number;
  };
  customSettings?: Record<string, any>;
}

export interface DeviceGroup {
  id: string;
  name: string;
  description?: string;
  default_config?: DeviceConfig;
  created_at: Date;
}

// User related types
export interface User {
  id: string;
  username: string;
  password_hash: string;
  role: 'admin' | 'operator' | 'viewer';
  permissions: UserPermissions;
  created_at: Date;
  last_login?: Date;
}

export interface UserPermissions {
  devices: ('read' | 'write' | 'delete')[];
  images: ('read' | 'write' | 'delete')[];
  users: ('read' | 'write' | 'delete')[];
  system: ('read' | 'write')[];
}

// Task related types
export interface CaptureTask {
  id: string;
  device_id: string;
  user_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  options: CaptureOptions;
  created_at: Date;
  completed_at?: Date;
  error_message?: string;
}

export interface CaptureOptions {
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  region?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  delay?: number;
  filename?: string;
  metadata?: Record<string, any>;
}

export interface DisplayTask {
  id: string;
  device_id: string;
  image_id: string;
  user_id: string;
  status: 'pending' | 'active' | 'completed' | 'cancelled';
  display_options: DisplayOptions;
  created_at: Date;
  started_at?: Date;
  ended_at?: Date;
}

export interface DisplayOptions {
  mode?: 'fullscreen' | 'fit' | 'stretch' | 'center';
  duration?: number; // in seconds, 0 for indefinite
  transition?: 'fade' | 'slide' | 'none';
  background?: string; // color or 'transparent'
  position?: {
    x: number;
    y: number;
  };
  size?: {
    width: number;
    height: number;
  };
}

// Image related types
export interface Image {
  id: string;
  filename: string;
  original_name: string;
  device_id: string;
  task_id?: string;
  file_size: number;
  format: string;
  metadata: ImageMetadata;
  thumbnail_path?: string;
  created_at: Date;
}

export interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  density?: number;
  hasAlpha?: boolean;
  channels: number;
  exif?: Record<string, any>;
  colorSpace?: string;
}

// Device command types
export interface DeviceCommand {
  command: string;
  params: any;
  userId?: string;
  timestamp: string;
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Discovery related types
export interface DiscoveryMessage {
  type: 'discovery_request' | 'device_announcement' | 'device_response' | 'connectivity_test' | 'connectivity_response';
  data: any;
}

export interface DeviceAnnouncement {
  deviceId: string;
  name: string;
  platform: string;
  version: string;
  capabilities: string[];
  port?: number;
  timestamp: string;
}

// Configuration types
export interface GatewayConfig {
  server: {
    port: number;
    host: string;
    cors: {
      origin: string | string[];
      credentials: boolean;
    };
  };
  database: {
    path: string;
    backupInterval?: number;
  };
  websocket: {
    transports: string[];
    pingTimeout: number;
    pingInterval: number;
  };
  discovery: {
    port: number;
    broadcastInterval: number;
    deviceTimeout: number;
  };
  upload: {
    maxFileSize: number;
    allowedFormats: string[];
    thumbnailSize: {
      width: number;
      height: number;
    };
  };
  security: {
    jwtSecret: string;
    jwtExpiration: string;
    rateLimiting: {
      windowMs: number;
      max: number;
    };
  };
}

// Statistics types
export interface SystemStats {
  devices: {
    total: number;
    online: number;
    offline: number;
    byPlatform: Record<string, number>;
  };
  images: {
    total: number;
    totalSize: number;
    averageSize: number;
    byFormat: Record<string, number>;
    byDevice: Record<string, number>;
  };
  tasks: {
    capture: {
      pending: number;
      running: number;
      completed: number;
      failed: number;
    };
    display: {
      pending: number;
      active: number;
      completed: number;
      cancelled: number;
    };
  };
  connections: {
    devices: number;
    clients: number;
  };
  uptime: number;
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  code: string;
  details?: any;
}

export class ValidationError extends Error implements AppError {
  statusCode = 400;
  code = 'VALIDATION_ERROR';
  details?: any;

  constructor(message: string, details?: any) {
    super(message);
    this.name = 'ValidationError';
    this.details = details;
  }
}

export class NotFoundError extends Error implements AppError {
  statusCode = 404;
  code = 'NOT_FOUND';

  constructor(message: string = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends Error implements AppError {
  statusCode = 401;
  code = 'UNAUTHORIZED';

  constructor(message: string = 'Unauthorized') {
    super(message);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends Error implements AppError {
  statusCode = 403;
  code = 'FORBIDDEN';

  constructor(message: string = 'Forbidden') {
    super(message);
    this.name = 'ForbiddenError';
  }
}

export class ConflictError extends Error implements AppError {
  statusCode = 409;
  code = 'CONFLICT';

  constructor(message: string = 'Conflict') {
    super(message);
    this.name = 'ConflictError';
  }
}

export class InternalServerError extends Error implements AppError {
  statusCode = 500;
  code = 'INTERNAL_SERVER_ERROR';
  details?: any;

  constructor(message: string = 'Internal server error', details?: any) {
    super(message);
    this.name = 'InternalServerError';
    this.details = details;
  }
}

// Utility types
export type SortOrder = 'ASC' | 'DESC';
export type DeviceStatus = Device['status'];
export type UserRole = User['role'];
export type TaskStatus = CaptureTask['status'] | DisplayTask['status'];
export type ImageFormat = 'jpeg' | 'png' | 'webp' | 'gif' | 'bmp' | 'tiff';

// Request/Response types for API endpoints
export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  token: string;
  user: {
    id: string;
    username: string;
    role: UserRole;
    permissions: UserPermissions;
  };
}

export interface CreateUserRequest {
  username: string;
  password: string;
  role: UserRole;
  permissions?: UserPermissions;
}

export interface UpdateUserRequest {
  username?: string;
  password?: string;
  role?: UserRole;
  permissions?: UserPermissions;
}

export interface CreateDeviceRequest {
  id?: string;
  name: string;
  ip_address: string;
  platform: string;
  version?: string;
  capabilities?: string[];
  config?: DeviceConfig;
  group_id?: string;
}

export interface UpdateDeviceRequest {
  name?: string;
  ip_address?: string;
  platform?: string;
  version?: string;
  capabilities?: string[];
  config?: DeviceConfig;
  group_id?: string;
}

export interface CreateDeviceGroupRequest {
  name: string;
  description?: string;
  default_config?: DeviceConfig;
}

export interface UpdateDeviceGroupRequest {
  name?: string;
  description?: string;
  default_config?: DeviceConfig;
}

export interface CaptureRequest {
  deviceId: string;
  options?: CaptureOptions;
}

export interface DisplayRequest {
  deviceId: string;
  imageId: string;
  options?: DisplayOptions;
}

export interface ImageSearchQuery {
  deviceId?: string;
  format?: string;
  dateFrom?: string;
  dateTo?: string;
  minSize?: number;
  maxSize?: number;
  keyword?: string;
  page?: number;
  limit?: number;
  sortBy?: 'created_at' | 'file_size' | 'original_name';
  sortOrder?: SortOrder;
}

// Export all types as a namespace for easier importing
export namespace Types {
  export type Device = Device;
  export type DeviceConfig = DeviceConfig;
  export type DeviceGroup = DeviceGroup;
  export type User = User;
  export type UserPermissions = UserPermissions;
  export type CaptureTask = CaptureTask;
  export type CaptureOptions = CaptureOptions;
  export type DisplayTask = DisplayTask;
  export type DisplayOptions = DisplayOptions;
  export type Image = Image;
  export type ImageMetadata = ImageMetadata;
  export type WebSocketMessage = WebSocketMessage;
  export type DeviceCommand = DeviceCommand;
  export type ApiResponse<T = any> = ApiResponse<T>;
  export type PaginatedResponse<T> = PaginatedResponse<T>;
  export type DiscoveryMessage = DiscoveryMessage;
  export type DeviceAnnouncement = DeviceAnnouncement;
  export type GatewayConfig = GatewayConfig;
  export type SystemStats = SystemStats;
  export type AppError = AppError;
}