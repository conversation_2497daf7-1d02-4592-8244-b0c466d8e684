import 'package:equatable/equatable.dart';

/// 用户角色枚举
enum UserRole {
  admin('admin', '管理员'),
  operator('operator', '操作员'),
  viewer('viewer', '查看者');

  const UserRole(this.value, this.displayName);

  final String value;
  final String displayName;

  /// 从字符串创建用户角色
  static UserRole fromString(String value) {
    switch (value.toLowerCase()) {
      case 'admin':
        return UserRole.admin;
      case 'operator':
        return UserRole.operator;
      case 'viewer':
        return UserRole.viewer;
      default:
        return UserRole.viewer;
    }
  }
}

/// 用户模型
class User extends Equatable {
  const User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.avatar,
    this.isActive = true,
    this.createdAt,
    this.lastLoginAt,
  });

  final String id;
  final String name;
  final String email;
  final String role;
  final String? avatar;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;

  /// 从JSON创建用户对象
  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      role: json['role'] as String,
      avatar: json['avatar'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'avatar': avatar,
      'isActive': isActive,
      'createdAt': createdAt?.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
    };
  }

  /// 复制并修改用户对象
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    String? avatar,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      avatar: avatar ?? this.avatar,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  /// 获取用户角色
  UserRole get userRole => UserRole.fromString(role);

  /// 是否为管理员
  bool get isAdmin => userRole == UserRole.admin;

  /// 是否为操作员
  bool get isOperator => userRole == UserRole.operator;

  /// 是否为查看者
  bool get isViewer => userRole == UserRole.viewer;

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        role,
        avatar,
        isActive,
        createdAt,
        lastLoginAt,
      ];

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $role, isActive: $isActive)';
  }
}