/// 应用常量定义
class AppConstants {
  // 应用信息
  static const String appName = 'Control Display App';
  static const String appVersion = '1.0.0';
  static const String appDescription = '图片切换控制端应用';
  
  // 路由常量
  static const String splashRoute = '/splash';
  static const String loginRoute = '/login';
  static const String dashboardRoute = '/dashboard';
  static const String devicesRoute = '/devices';
  static const String galleryRoute = '/gallery';
  static const String settingsRoute = '/settings';
  
  // 存储键常量
  static const String userTokenKey = 'user_token';
  static const String userInfoKey = 'user_info';
  static const String gatewayConfigKey = 'gateway_config';
  static const String appSettingsKey = 'app_settings';
  static const String deviceListKey = 'device_list';
  static const String imageListKey = 'image_list';
  
  // 网络常量
  static const String defaultGatewayHost = 'localhost';
  static const String defaultGatewayUrl = 'http://localhost:3001';
  static const int defaultGatewayPort = 3001;
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration requestTimeout = Duration(seconds: 30);
  
  // UI常量
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 3);
  static const double defaultBorderRadius = 8.0;
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 8.0;
  
  // 图片常量
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const List<String> supportedImageFormats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  static const int defaultImageQuality = 85;
  static const int thumbnailSize = 200;
  
  // 设备常量
  static const Duration deviceDiscoveryTimeout = Duration(seconds: 10);
  static const Duration deviceHeartbeatInterval = Duration(seconds: 15);
  static const Duration deviceOfflineTimeout = Duration(minutes: 2);
  
  // 错误消息
  static const String networkErrorMessage = '网络连接失败，请检查网络设置';
  static const String authErrorMessage = '认证失败，请重新登录';
  static const String unknownErrorMessage = '未知错误，请稍后重试';
  
  // 成功消息
  static const String loginSuccessMessage = '登录成功';
  static const String logoutSuccessMessage = '退出成功';
  static const String saveSuccessMessage = '保存成功';
  
  // 默认值
  static const String defaultUsername = 'admin';
  static const String defaultPassword = 'admin123';
  
  // 主题常量
  static const String lightThemeKey = 'light';
  static const String darkThemeKey = 'dark';
  static const String systemThemeKey = 'system';
  
  // 语言常量
  static const String chineseLanguageKey = 'zh';
  static const String englishLanguageKey = 'en';
  
  // 文件路径常量
  static const String imagesFolder = 'images';
  static const String thumbnailsFolder = 'thumbnails';
  static const String logsFolder = 'logs';
  static const String cacheFolder = 'cache';
  
  // 权限常量
  static const String adminRole = 'admin';
  static const String userRole = 'user';
  static const String guestRole = 'guest';
  
  // WebSocket事件常量
  static const String deviceConnectedEvent = 'device_connected';
  static const String deviceDisconnectedEvent = 'device_disconnected';
  static const String imageUpdatedEvent = 'image_updated';
  static const String statusUpdatedEvent = 'status_updated';
}