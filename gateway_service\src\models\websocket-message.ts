/**
 * WebSocket消息协议定义
 * 统一控制端、终端应用和网关服务之间的通信格式
 */

/// WebSocket消息类型
export enum WebSocketMessageType {
  DEVICE = 'device',
  SCREENSHOT = 'screenshot',
  DISPLAY = 'display',
  HEARTBEAT = 'heartbeat',
  DISCOVERY = 'discovery',
  SYNC = 'sync',
  ERROR = 'error',
  AUTH = 'auth'
}

/// WebSocket消息动作
export enum WebSocketMessageAction {
  // 设备相关
  DEVICE_REGISTER = 'register',
  DEVICE_UNREGISTER = 'unregister',
  DEVICE_UPDATE = 'update',
  DEVICE_STATUS = 'status',
  DEVICE_LIST = 'list',
  DEVICE_FOUND = 'found',
  
  // 截图相关
  SCREENSHOT_TAKE = 'take',
  SCREENSHOT_BATCH = 'batch',
  SCREENSHOT_SCHEDULE = 'schedule',
  SCREENSHOT_RESULT = 'result',
  SCREENSHOT_ERROR = 'error',
  
  // 显示相关
  DISPLAY_SHOW = 'show',
  DISPLAY_HIDE = 'hide',
  DISPLAY_FULLSCREEN = 'fullscreen',
  DISPLAY_WINDOW = 'window',
  DISPLAY_SYNC = 'sync',
  DISPLAY_CONTROL = 'control',
  
  // 发现相关
  DISCOVERY_REQUEST = 'request',
  DISCOVERY_RESPONSE = 'response',
  DISCOVERY_BROADCAST = 'broadcast',
  
  // 同步相关
  SYNC_START = 'start',
  SYNC_STOP = 'stop',
  SYNC_STATUS = 'status',
  
  // 心跳
  HEARTBEAT_PING = 'ping',
  HEARTBEAT_PONG = 'pong',
  
  // 认证
  AUTH_LOGIN = 'login',
  AUTH_LOGOUT = 'logout',
  AUTH_VERIFY = 'verify'
}

/// WebSocket消息接口
export interface IWebSocketMessage {
  type: WebSocketMessageType;
  action: WebSocketMessageAction;
  payload?: Record<string, any>;
  timestamp: string;
  messageId: string;
  senderId?: string;
  targetId?: string;
}

/// WebSocket消息类
export class WebSocketMessage implements IWebSocketMessage {
  public readonly type: WebSocketMessageType;
  public readonly action: WebSocketMessageAction;
  public readonly payload?: Record<string, any>;
  public readonly timestamp: string;
  public readonly messageId: string;
  public readonly senderId?: string;
  public readonly targetId?: string;
  
  constructor({
    type,
    action,
    payload,
    timestamp,
    messageId,
    senderId,
    targetId
  }: {
    type: WebSocketMessageType;
    action: WebSocketMessageAction;
    payload?: Record<string, any>;
    timestamp?: string;
    messageId?: string;
    senderId?: string;
    targetId?: string;
  }) {
    this.type = type;
    this.action = action;
    this.payload = payload;
    this.timestamp = timestamp || new Date().toISOString();
    this.messageId = messageId || this.generateMessageId();
    this.senderId = senderId;
    this.targetId = targetId;
  }
  
  /// 生成消息ID
  private generateMessageId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /// 从JSON创建消息
  static fromJson(json: Record<string, any>): WebSocketMessage {
    return new WebSocketMessage({
      type: json.type as WebSocketMessageType,
      action: json.action as WebSocketMessageAction,
      payload: json.payload,
      timestamp: json.timestamp,
      messageId: json.messageId,
      senderId: json.senderId,
      targetId: json.targetId
    });
  }
  
  /// 转换为JSON
  toJson(): Record<string, any> {
    return {
      type: this.type,
      action: this.action,
      payload: this.payload,
      timestamp: this.timestamp,
      messageId: this.messageId,
      senderId: this.senderId,
      targetId: this.targetId
    };
  }
  
  /// 转换为JSON字符串
  toJsonString(): string {
    return JSON.stringify(this.toJson());
  }
  
  /// 从JSON字符串创建消息
  static fromJsonString(jsonString: string): WebSocketMessage {
    const json = JSON.parse(jsonString);
    return WebSocketMessage.fromJson(json);
  }
  
  // === 静态工厂方法 ===
  
  /// 设备注册消息
  static deviceRegister(deviceInfo: Record<string, any>, senderId?: string): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.DEVICE,
      action: WebSocketMessageAction.DEVICE_REGISTER,
      payload: deviceInfo,
      senderId
    });
  }
  
  /// 设备注销消息
  static deviceUnregister(deviceId: string, senderId?: string): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.DEVICE,
      action: WebSocketMessageAction.DEVICE_UNREGISTER,
      payload: { deviceId },
      senderId
    });
  }
  
  /// 设备状态更新消息
  static deviceStatusUpdate(
    deviceId: string, 
    status: string, 
    metadata?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.DEVICE,
      action: WebSocketMessageAction.DEVICE_STATUS,
      payload: {
        deviceId,
        status,
        metadata,
        timestamp: new Date().toISOString()
      },
      senderId
    });
  }
  
  /// 设备发现请求消息
  static discoveryRequest(senderId?: string): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.DISCOVERY,
      action: WebSocketMessageAction.DISCOVERY_REQUEST,
      senderId
    });
  }
  
  /// 设备发现响应消息
  static discoveryResponse(deviceInfo: Record<string, any>, senderId?: string): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.DISCOVERY,
      action: WebSocketMessageAction.DISCOVERY_RESPONSE,
      payload: deviceInfo,
      senderId
    });
  }
  
  /// 截图请求消息
  static screenshotTake(
    deviceId: string,
    screenshotId: string,
    config?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.SCREENSHOT,
      action: WebSocketMessageAction.SCREENSHOT_TAKE,
      payload: {
        deviceId,
        screenshotId,
        config
      },
      senderId,
      targetId: deviceId
    });
  }
  
  /// 批量截图请求消息
  static screenshotBatch(
    deviceIds: string[],
    batchId: string,
    config?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.SCREENSHOT,
      action: WebSocketMessageAction.SCREENSHOT_BATCH,
      payload: {
        deviceIds,
        batchId,
        config
      },
      senderId
    });
  }
  
  /// 截图结果消息
  static screenshotResult(
    deviceId: string,
    screenshotId: string,
    success: boolean,
    imagePath?: string,
    imageData?: string,
    error?: string,
    metadata?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.SCREENSHOT,
      action: WebSocketMessageAction.SCREENSHOT_RESULT,
      payload: {
        deviceId,
        screenshotId,
        success,
        imagePath,
        imageData,
        error,
        metadata,
        timestamp: new Date().toISOString()
      },
      senderId
    });
  }
  
  /// 显示控制消息
  static displayControl(
    deviceId: string,
    action: string,
    imageUrl?: string,
    config?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.DISPLAY,
      action: WebSocketMessageAction.DISPLAY_CONTROL,
      payload: {
        deviceId,
        displayAction: action,
        imageUrl,
        config
      },
      senderId,
      targetId: deviceId
    });
  }
  
  /// 同步显示消息
  static displaySync(
    deviceIds: string[],
    imageUrl: string,
    syncId: string,
    config?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.DISPLAY,
      action: WebSocketMessageAction.DISPLAY_SYNC,
      payload: {
        deviceIds,
        imageUrl,
        syncId,
        config
      },
      senderId
    });
  }
  
  /// 心跳消息
  static heartbeat(senderId?: string): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.HEARTBEAT,
      action: WebSocketMessageAction.HEARTBEAT_PING,
      senderId
    });
  }
  
  /// 心跳响应消息
  static heartbeatPong(senderId?: string): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.HEARTBEAT,
      action: WebSocketMessageAction.HEARTBEAT_PONG,
      senderId
    });
  }
  
  /// 错误消息
  static error(
    error: string,
    details?: string,
    context?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: WebSocketMessageType.ERROR,
      action: 'general' as WebSocketMessageAction,
      payload: {
        error,
        details,
        context,
        timestamp: new Date().toISOString()
      },
      senderId
    });
  }
  
  // === 辅助方法 ===
  
  /// 是否为设备消息
  get isDeviceMessage(): boolean {
    return this.type === WebSocketMessageType.DEVICE;
  }
  
  /// 是否为截图消息
  get isScreenshotMessage(): boolean {
    return this.type === WebSocketMessageType.SCREENSHOT;
  }
  
  /// 是否为显示消息
  get isDisplayMessage(): boolean {
    return this.type === WebSocketMessageType.DISPLAY;
  }
  
  /// 是否为心跳消息
  get isHeartbeatMessage(): boolean {
    return this.type === WebSocketMessageType.HEARTBEAT;
  }
  
  /// 是否为发现消息
  get isDiscoveryMessage(): boolean {
    return this.type === WebSocketMessageType.DISCOVERY;
  }
  
  /// 是否为错误消息
  get isErrorMessage(): boolean {
    return this.type === WebSocketMessageType.ERROR;
  }
  
  /// 获取设备ID
  get deviceId(): string | undefined {
    return this.payload?.deviceId || this.targetId;
  }
  
  /// 获取错误信息
  get errorMessage(): string | undefined {
    return this.payload?.error;
  }
  
  /// 创建响应消息
  createResponse(
    action: WebSocketMessageAction,
    payload?: Record<string, any>,
    senderId?: string
  ): WebSocketMessage {
    return new WebSocketMessage({
      type: this.type,
      action,
      payload,
      senderId,
      targetId: this.senderId // 响应给原发送者
    });
  }
  
  toString(): string {
    return `WebSocketMessage(type: ${this.type}, action: ${this.action}, messageId: ${this.messageId})`;
  }
}

/// 消息验证器
export class MessageValidator {
  /// 验证消息格式
  static validate(message: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!message.type || !Object.values(WebSocketMessageType).includes(message.type)) {
      errors.push('Invalid or missing message type');
    }
    
    if (!message.action || !Object.values(WebSocketMessageAction).includes(message.action)) {
      errors.push('Invalid or missing message action');
    }
    
    if (!message.timestamp) {
      errors.push('Missing timestamp');
    }
    
    if (!message.messageId) {
      errors.push('Missing message ID');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /// 验证设备注册消息
  static validateDeviceRegister(payload: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!payload.id) {
      errors.push('Device ID is required');
    }
    
    if (!payload.name) {
      errors.push('Device name is required');
    }
    
    if (!payload.type) {
      errors.push('Device type is required');
    }
    
    if (!payload.capabilities || !Array.isArray(payload.capabilities)) {
      errors.push('Device capabilities must be an array');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  /// 验证截图请求消息
  static validateScreenshotRequest(payload: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!payload.deviceId && !payload.deviceIds) {
      errors.push('Device ID or device IDs are required');
    }
    
    if (!payload.screenshotId && !payload.batchId) {
      errors.push('Screenshot ID or batch ID is required');
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
}