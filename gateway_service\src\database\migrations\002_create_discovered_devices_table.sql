-- 创建发现设备表
CREATE TABLE IF NOT EXISTS discovered_devices (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'terminal',
    platform TEXT NOT NULL DEFAULT 'unknown',
    version TEXT NOT NULL DEFAULT '1.0.0',
    ip_address TEXT NOT NULL,
    port INTEGER NOT NULL DEFAULT 0,
    capabilities TEXT NOT NULL DEFAULT '{}', -- JSON格式的设备能力
    metadata TEXT NOT NULL DEFAULT '{}', -- JSON格式的设备元数据
    discovered_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_seen DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_discovered_devices_ip_address ON discovered_devices(ip_address);
CREATE INDEX IF NOT EXISTS idx_discovered_devices_type ON discovered_devices(type);
CREATE INDEX IF NOT EXISTS idx_discovered_devices_last_seen ON discovered_devices(last_seen);
CREATE INDEX IF NOT EXISTS idx_discovered_devices_discovered_at ON discovered_devices(discovered_at);

-- 创建触发器以自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS update_discovered_devices_updated_at
    AFTER UPDATE ON discovered_devices
    FOR EACH ROW
BEGIN
    UPDATE discovered_devices SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 插入一些示例数据（可选）
-- INSERT OR IGNORE INTO discovered_devices (id, name, type, platform, version, ip_address, port, capabilities, metadata)
-- VALUES 
--     ('example_device_1', 'Example Terminal 1', 'terminal', 'windows', '1.0.0', '*************', 8080, '{"screenshot": true, "display": true}', '{"location": "Office 1"}'),
--     ('example_device_2', 'Example Terminal 2', 'terminal', 'linux', '1.0.0', '*************', 8080, '{"screenshot": true, "display": true}', '{"location": "Office 2"}');