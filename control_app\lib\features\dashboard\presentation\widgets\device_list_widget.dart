import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/models/device_model.dart';
import '../../../../core/providers/service_providers.dart';
import '../../../../core/services/device_management_service.dart';
import '../../../../core/services/logger_service.dart';

/// 设备列表组件
class DeviceListWidget extends ConsumerStatefulWidget {
  const DeviceListWidget({super.key});
  
  @override
  ConsumerState<DeviceListWidget> createState() => _DeviceListWidgetState();
}

class _DeviceListWidgetState extends ConsumerState<DeviceListWidget> {
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildDeviceList(),
        ),
      ],
    );
  }
  
  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: '搜索设备...',
          hintStyle: const TextStyle(color: Colors.grey),
          prefixIcon: const Icon(Icons.search, color: Colors.grey),
          border: const OutlineInputBorder(
            borderSide: BorderSide(color: Colors.white24),
          ),
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide(color: Colors.white24),
          ),
          focusedBorder: const OutlineInputBorder(
            borderSide: BorderSide(color: Colors.blue),
          ),
          filled: true,
          fillColor: Theme.of(context).colorScheme.surface,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }
  
  /// 构建设备列表
  Widget _buildDeviceList() {
    final deviceService = ref.watch(deviceManagementServiceProvider);
    final devices = deviceService.getAllDevices();
    
    // 应用搜索过滤
    final filteredDevices = devices.where((device) {
      if (_searchQuery.isEmpty) return true;
      
      return device.name.toLowerCase().contains(_searchQuery) ||
             (device.address?.toLowerCase().contains(_searchQuery) ?? false) ||
             device.id.toLowerCase().contains(_searchQuery);
    }).toList();
    
    if (filteredDevices.isEmpty) {
      return _buildEmptyState();
    }
    
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredDevices.length,
      itemBuilder: (context, index) {
        final device = filteredDevices[index];
        return _buildDeviceCard(device);
      },
    );
  }
  
  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.devices_outlined,
            size: 64,
            color: Colors.white54,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty ? '未找到匹配的设备' : '暂无设备',
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? '尝试使用不同的搜索关键词'
                : '点击扫描按钮发现设备',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.white54,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建设备卡片
  Widget _buildDeviceCard(Device device) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 设备图标和状态
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor(device.status.name).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getDeviceIcon(device),
                    color: _getStatusColor(device.status.name),
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // 设备信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        device.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        device.networkAddress ?? device.address ?? '未知地址',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white54,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 状态指示器
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: _getStatusColor(device.status.name).withOpacity(0.5)),
                  ),
                  child: Text(
                    _getStatusText(device.status.name),
                    style: TextStyle(
                      color: _getStatusColor(device.status.name),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                
                // 操作菜单
                PopupMenuButton<String>(
                  color: Theme.of(context).colorScheme.surface,
                  iconColor: Theme.of(context).colorScheme.onSurface,
                  onSelected: (value) => _handleDeviceAction(device, value),
                  itemBuilder: (context) => [
                    if (device.isOffline)
                      const PopupMenuItem(
                        value: 'connect',
                        child: ListTile(
                          leading: Icon(Icons.link, color: Theme.of(context).colorScheme.onSurface),
                          title: Text('连接', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    if (device.isOnline)
                      PopupMenuItem(
                        value: 'disconnect',
                        child: ListTile(
                        leading: Icon(Icons.link_off, color: Theme.of(context).colorScheme.onSurface),
                          title: Text('断开', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    PopupMenuItem(
                      value: 'screenshot',
                      child: ListTile(
                        leading: Icon(Icons.screenshot, color: Theme.of(context).colorScheme.onSurface),
                        title: Text('截图', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit, color: Theme.of(context).colorScheme.onSurface),
                        title: Text('编辑', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
                        title: Text('删除', style: TextStyle(color: Theme.of(context).colorScheme.error)),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // 详细信息芯片
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: [
                _buildInfoChip(
                  Icons.phone_android,
                  device.platform,
                  Colors.blue,
                ),
                _buildInfoChip(
                  Icons.info_outline,
                  device.version,
                  Colors.green,
                ),
                if (device.lastSeen != null)
                  _buildInfoChip(
                    Icons.access_time,
                    _formatLastSeen(device.lastSeen!),
                    Colors.orange,
                  ),
              ],
            ),
            
            // 显示状态信息（基于设备状态）
            if (device.isOnline) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.tv,
                      size: 16,
                      color: Colors.blue,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '状态: ${device.status}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.blue,
                      ),
                    ),
                    if (device.canDisplay) ...[
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.image,
                        size: 16,
                        color: Colors.blue,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '显示功能: 可用',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  /// 构建信息芯片
  Widget _buildInfoChip(IconData icon, String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.5)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 获取设备图标
  IconData _getDeviceIcon(Device device) {
    switch (device.platform?.toLowerCase()) {
      case 'windows':
        return Icons.desktop_windows;
      case 'macos':
        return Icons.desktop_mac;
      case 'linux':
        return Icons.computer;
      case 'android':
        return Icons.phone_android;
      case 'ios':
        return Icons.phone_iphone;
      default:
        return Icons.device_unknown;
    }
  }
  
  /// 获取状态颜色
  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return Colors.green;
      case 'offline':
        return Colors.grey;
      case 'connecting':
        return Colors.orange;
      case 'disconnecting':
        return Colors.orange;
      case 'maintenance':
        return Colors.blue;
      case 'error':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
  
  /// 获取状态文本
  String _getStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'online':
        return '在线';
      case 'offline':
        return '离线';
      case 'connecting':
        return '连接中';
      case 'disconnecting':
        return '断开中';
      case 'maintenance':
        return '维护中';
      case 'error':
        return '错误';
      default:
        return '未知';
    }
  }
  
  /// 格式化最后见到时间
  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }
  
  /// 处理设备操作
  Future<void> _handleDeviceAction(Device device, String action) async {
    final deviceService = ref.read(deviceManagementServiceProvider);
    
    try {
      switch (action) {
        case 'connect':
          await deviceService.connectDevice(device.id);
          _showSuccessSnackBar('设备连接成功');
          break;
          
        case 'disconnect':
          await deviceService.disconnectDevice(device.id);
          _showSuccessSnackBar('设备断开成功');
          break;
          
        case 'screenshot':
          await deviceService.requestScreenshot(device.id);
          _showSuccessSnackBar('截图请求已发送');
          break;
          
        case 'edit':
          await _showEditDeviceDialog(device);
          break;
          
        case 'delete':
          final confirmed = await _showDeleteConfirmDialog(device);
          if (confirmed) {
            await deviceService.removeDevice(device.id);
            _showSuccessSnackBar('设备删除成功');
          }
          break;
      }
    } catch (error) {
      LoggerService.error('Device action failed: $action', error);
      _showErrorSnackBar('操作失败: $action');
    }
  }
  
  /// 显示编辑设备对话框
  Future<void> _showEditDeviceDialog(Device device) async {
    final nameController = TextEditingController(text: device.name);
    final addressController = TextEditingController(text: device.networkAddress ?? device.address ?? '');
    
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).colorScheme.surface,
          title: Text('编辑设备', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                decoration: InputDecoration(
                  labelText: '设备名称',
                  labelStyle: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: addressController,
                style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
                decoration: InputDecoration(
                  labelText: '设备地址',
                  labelStyle: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('取消', style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6))),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
              ),
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('保存'),
            ),
          ],
        );
      },
    );
    
    if (result == true) {
      try {
        final deviceService = ref.read(deviceManagementServiceProvider);
        await deviceService.updateDevice(
          device.id,
          name: nameController.text.trim(),
          address: addressController.text.trim(),
        );
        _showSuccessSnackBar('设备信息更新成功');
      } catch (error) {
        LoggerService.error('Failed to update device', error);
        _showErrorSnackBar('设备信息更新失败');
      }
    }
    
    nameController.dispose();
    addressController.dispose();
  }
  
  /// 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog(Device device) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: Theme.of(context).colorScheme.surface,
          title: Text('删除设备', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
          content: Text('确定要删除设备 "${device.name}" 吗？此操作无法撤销。', style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text('取消', style: TextStyle(color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6))),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Theme.of(context).colorScheme.onError,
              ),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
    
    return result ?? false;
  }
  
  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 2),
      ),
    );
  }
  
  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: TextStyle(color: Theme.of(context).colorScheme.onError)),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}