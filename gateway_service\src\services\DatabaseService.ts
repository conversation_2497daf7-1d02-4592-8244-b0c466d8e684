import sqlite3 from 'sqlite3';
import path from 'path';
import fs from 'fs';

class DatabaseService {
  private static instance: DatabaseService;
  private db: sqlite3.Database | null = null;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Ensure data directory exists
      const dataDir = path.join(process.cwd(), 'data');
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const dbPath = path.join(dataDir, 'imgct.db');
      
      // Create database connection
      this.db = new sqlite3.Database(dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err);
          throw err;
        }
        console.log('Connected to SQLite database');
      });

      // Enable foreign keys
      await this.query('PRAGMA foreign_keys = ON');
      
      // Create tables
      await this.createTables();
      
      // Insert default data
      await this.insertDefaultData();
      
      this.isInitialized = true;
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    const tables = [
      // Device groups table
      `CREATE TABLE IF NOT EXISTS device_groups (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        description TEXT,
        default_config TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Devices table
      `CREATE TABLE IF NOT EXISTS devices (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        type TEXT NOT NULL DEFAULT 'terminal',
        ip_address TEXT NOT NULL,
        port INTEGER NOT NULL DEFAULT 8080,
        platform TEXT NOT NULL DEFAULT 'unknown',
        version TEXT NOT NULL DEFAULT '1.0.0',
        capabilities TEXT,
        status TEXT DEFAULT 'offline' CHECK (status IN ('online', 'offline', 'busy')),
        last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
        config TEXT DEFAULT '{}',
        group_id TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (group_id) REFERENCES device_groups(id)
      )`,
      
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT NOT NULL UNIQUE,
        password_hash TEXT NOT NULL,
        role TEXT DEFAULT 'operator' CHECK (role IN ('admin', 'operator', 'viewer')),
        permissions TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        last_login DATETIME
      )`,
      
      // Capture tasks table
      `CREATE TABLE IF NOT EXISTS capture_tasks (
        id TEXT PRIMARY KEY,
        device_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
        options TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        completed_at DATETIME,
        error_message TEXT,
        FOREIGN KEY (device_id) REFERENCES devices(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`,
      
      // Images table
      `CREATE TABLE IF NOT EXISTS images (
        id TEXT PRIMARY KEY,
        filename TEXT NOT NULL,
        original_name TEXT,
        device_id TEXT NOT NULL,
        task_id TEXT,
        file_size INTEGER,
        format TEXT,
        metadata TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        thumbnail_path TEXT,
        FOREIGN KEY (device_id) REFERENCES devices(id),
        FOREIGN KEY (task_id) REFERENCES capture_tasks(id)
      )`,
      
      // Display tasks table
      `CREATE TABLE IF NOT EXISTS display_tasks (
        id TEXT PRIMARY KEY,
        device_id TEXT NOT NULL,
        image_id TEXT NOT NULL,
        user_id TEXT NOT NULL,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'active', 'completed', 'cancelled')),
        display_options TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        started_at DATETIME,
        ended_at DATETIME,
        FOREIGN KEY (device_id) REFERENCES devices(id),
        FOREIGN KEY (image_id) REFERENCES images(id),
        FOREIGN KEY (user_id) REFERENCES users(id)
      )`
    ];

    for (const table of tables) {
      await this.query(table);
    }

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status)',
      'CREATE INDEX IF NOT EXISTS idx_devices_group ON devices(group_id)',
      'CREATE INDEX IF NOT EXISTS idx_devices_last_seen ON devices(last_seen DESC)',
      'CREATE INDEX IF NOT EXISTS idx_capture_tasks_device ON capture_tasks(device_id)',
      'CREATE INDEX IF NOT EXISTS idx_capture_tasks_status ON capture_tasks(status)',
      'CREATE INDEX IF NOT EXISTS idx_capture_tasks_created ON capture_tasks(created_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_images_device ON images(device_id)',
      'CREATE INDEX IF NOT EXISTS idx_images_created ON images(created_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_display_tasks_device ON display_tasks(device_id)',
      'CREATE INDEX IF NOT EXISTS idx_display_tasks_status ON display_tasks(status)'
    ];

    for (const index of indexes) {
      await this.query(index);
    }
  }

  private async insertDefaultData(): Promise<void> {
    try {
      // Check if admin user exists
      const adminExists = await this.query(
        'SELECT COUNT(*) as count FROM users WHERE username = ?',
        ['admin']
      );

      if (adminExists[0].count === 0) {
        // Create default admin user (password: admin123)
        const bcrypt = await import('bcryptjs');
        const passwordHash = await bcrypt.hash('admin123', 10);
        
        await this.query(
          'INSERT INTO users (id, username, password_hash, role, permissions) VALUES (?, ?, ?, ?, ?)',
          [
            'admin_001',
            'admin',
            passwordHash,
            'admin',
            JSON.stringify({
              devices: ['read', 'write', 'delete'],
              images: ['read', 'write', 'delete'],
              users: ['read', 'write', 'delete'],
              system: ['read', 'write']
            })
          ]
        );
        console.log('Default admin user created (username: admin, password: admin123)');
      }

      // Check if default device groups exist
      const groupsExist = await this.query(
        'SELECT COUNT(*) as count FROM device_groups'
      );

      if (groupsExist[0].count === 0) {
        const defaultGroups = [
          {
            id: 'group_default',
            name: '默认分组',
            description: '系统默认设备分组',
            config: {}
          },
          {
            id: 'group_meeting',
            name: '会议室',
            description: '会议室显示设备',
            config: {
              captureQuality: 90,
              autoCapture: false,
              displayMode: 'fullscreen'
            }
          },
          {
            id: 'group_monitor',
            name: '监控中心',
            description: '监控中心显示设备',
            config: {
              captureQuality: 85,
              autoCapture: true,
              captureInterval: 30,
              displayMode: 'fit'
            }
          }
        ];

        for (const group of defaultGroups) {
          await this.query(
            'INSERT INTO device_groups (id, name, description, default_config) VALUES (?, ?, ?, ?)',
            [group.id, group.name, group.description, JSON.stringify(group.config)]
          );
        }
        console.log('Default device groups created');
      }
    } catch (error) {
      console.error('Error inserting default data:', error);
    }
  }

  public async query(sql: string, params: any[] = []): Promise<any[]> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      if (sql.trim().toUpperCase().startsWith('SELECT') || sql.trim().toUpperCase().startsWith('PRAGMA')) {
        this.db.all(sql, params, (err, rows) => {
          if (err) {
            console.error('Database query error:', err);
            reject(err);
          } else {
            resolve(rows || []);
          }
        });
      } else {
        this.db.run(sql, params, function(err) {
          if (err) {
            console.error('Database query error:', err);
            reject(err);
          } else {
            resolve([{ changes: this.changes, lastID: this.lastID }]);
          }
        });
      }
    });
  }

  public async transaction(queries: Array<{ sql: string; params?: any[] }>): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      this.db.serialize(() => {
        this.db!.run('BEGIN TRANSACTION');
        
        let completed = 0;
        let hasError = false;

        const executeNext = () => {
          if (completed >= queries.length) {
            if (hasError) {
              this.db!.run('ROLLBACK', (err) => {
                if (err) console.error('Rollback error:', err);
                reject(new Error('Transaction failed'));
              });
            } else {
              this.db!.run('COMMIT', (err) => {
                if (err) {
                  console.error('Commit error:', err);
                  reject(err);
                } else {
                  resolve();
                }
              });
            }
            return;
          }

          const query = queries[completed];
          if (query && query.sql) {
            this.db!.run(query.sql, query.params || [], (err) => {
            if (err) {
              console.error('Transaction query error:', err);
              hasError = true;
            }
              completed++;
              executeNext();
            });
          } else {
            hasError = true;
            completed++;
            executeNext();
          }
        };

        executeNext();
      });
    });
  }

  public isConnected(): boolean {
    return this.isInitialized && this.db !== null;
  }

  public async close(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error('Error closing database:', err);
            reject(err);
          } else {
            console.log('Database connection closed');
            this.db = null;
            this.isInitialized = false;
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  // Backup database
  public async backup(backupPath: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('Database not initialized'));
        return;
      }

      const backup = new sqlite3.Database(backupPath);
      
      (this.db as any).backup(backup, (err: any) => {
        backup.close();
        if (err) {
          console.error('Backup error:', err);
          reject(err);
        } else {
          console.log(`Database backed up to ${backupPath}`);
          resolve();
        }
      });
    });
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance();
export default databaseService;