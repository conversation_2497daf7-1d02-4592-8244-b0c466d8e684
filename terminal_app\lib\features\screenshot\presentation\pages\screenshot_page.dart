import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import '../../../../core/providers/device_provider.dart';
import '../../../../core/providers/service_providers.dart';
import '../../../../core/services/logger_service.dart';
import '../../../../core/services/screenshot_log_service.dart';
import 'screenshot_log_page.dart';
import 'image_viewer_page.dart';

class ScreenshotPage extends ConsumerStatefulWidget {
  const ScreenshotPage({super.key});

  @override
  ConsumerState<ScreenshotPage> createState() => _ScreenshotPageState();
}

class _ScreenshotPageState extends ConsumerState<ScreenshotPage> {
  bool _isCapturing = false;
  List<ScreenshotLogEntry> _recentScreenshots = [];
  bool _loadingRecentLogs = false;
  StreamSubscription? _resultStreamSubscription;
  StreamSubscription? _logStreamSubscription;

  @override
  void initState() {
    super.initState();
    _initializeScreenshotService();
    _loadRecentScreenshots();
  }

  Future<void> _initializeScreenshotService() async {
    try {
      final screenshotService = ref.read(screenshotServiceProvider);
      await screenshotService.initialize();

      // 监听截图结果
      _resultStreamSubscription?.cancel();
      _resultStreamSubscription = screenshotService.resultStream.listen((result) {
        if (mounted) {
          setState(() {
            _isCapturing = false;
          });

          if (result.success) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('截图成功: \${result.filePath}'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('截图失败: ${result.error}'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      });

      // 监听截图日志流，实时更新列表
      _logStreamSubscription?.cancel();
      _logStreamSubscription = ScreenshotLogService.instance.logStream.listen((newLog) {
        if (mounted) {
          _loadRecentScreenshots();
        }
      });
    } catch (error) {
      LoggerService.error('Failed to initialize screenshot service', error);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('截图服务初始化失败: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _takeScreenshot() async {
    if (_isCapturing) return;

    setState(() {
      _isCapturing = true;
    });

    try {
      final screenshotService = ref.read(screenshotServiceProvider);
      await screenshotService.takeScreenshot(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
      );
      // 结果通过stream处理
    } catch (error) {
      LoggerService.error('Screenshot failed', error);
      setState(() {
        _isCapturing = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('截图失败: $error'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _resultStreamSubscription?.cancel();
    _logStreamSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('截图服务'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ScreenshotLogPage(),
                ),
              );
            },
            tooltip: '查看截图日志',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 设备信息卡片
            Consumer(
              builder: (context, widgetRef, child) {
                final deviceState = widgetRef.watch(deviceProvider);
                return Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '设备信息',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '设备ID: ${deviceState.deviceInfo?.id ?? '未知'}',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          softWrap: true,
                        ),
                        Text(
                          '设备名称: ${deviceState.deviceInfo?.name ?? '未知'}',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          softWrap: true,
                        ),
                        Text(
                          '设备类型: ${deviceState.deviceInfo?.type ?? '未知'}',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          softWrap: true,
                        ),
                        Text(
                          '连接状态: ${deviceState.isConnected ? '已连接' : '未连接'}',
                          style: TextStyle(
                            color: deviceState.isConnected
                                ? Colors.green
                                : Colors.red,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          softWrap: true,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            // 截图控制区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '截图控制',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),

                    // 截图控制按钮 - 只保留立即截图
                    Center(
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width > 600
                            ? 200
                            : double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _isCapturing ? null : _takeScreenshot,
                          icon: _isCapturing
                              ? const SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2.0,
                                    color: Colors.white,
                                  ),
                                )
                              : const Icon(Icons.camera_alt, size: 20),
                          label: Text(
                            _isCapturing ? '截图中...' : '立即截图',
                            style: const TextStyle(fontSize: 16),
                          ),
                          style: ElevatedButton.styleFrom(
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            minimumSize: const Size(0, 48),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // 最近截图列表
            _buildRecentScreenshots(),
          ],
        ),
      ),
    );
  }

  /// 加载最近的截图记录
  Future<void> _loadRecentScreenshots() async {
    if (_loadingRecentLogs) return;

    setState(() {
      _loadingRecentLogs = true;
    });

    try {
      final recentLogs =
          await ScreenshotLogService.instance.getRecentLogs(days: 3);
      final newLogs = recentLogs.take(5).toList();
      
      // 只有当列表内容实际发生变化时才更新UI
      if (!_areListsEqual(_recentScreenshots, newLogs)) {
        setState(() {
          _recentScreenshots = newLogs;
        });
      }
    } catch (error) {
      LoggerService.error('Failed to load recent screenshots', error);
    } finally {
      setState(() {
        _loadingRecentLogs = false;
      });
    }
  }

  /// 比较两个截图日志列表是否相同
  bool _areListsEqual(List<ScreenshotLogEntry> list1, List<ScreenshotLogEntry> list2) {
    if (list1.length != list2.length) return false;
    
    for (int i = 0; i < list1.length; i++) {
      if (list1[i].id != list2[i].id) {
        return false;
      }
    }
    
    return true;
  }

  /// 构建最近截图列表
  Widget _buildRecentScreenshots() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '最近截图',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                if (_loadingRecentLogs)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            if (_recentScreenshots.isEmpty && !_loadingRecentLogs)
              Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.photo_library_outlined,
                      size: 48,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '暂无截图记录',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              )
            else
              Column(
                children: [
                  // 截图列表
                  ...List.generate(_recentScreenshots.length, (index) {
                    final log = _recentScreenshots[index];
                    final isLast = index == _recentScreenshots.length - 1;
                    return Column(
                      children: [
                        _buildScreenshotItem(log),
                        if (!isLast) const Divider(height: 16),
                      ],
                    );
                  }),

                  // 查看更多按钮
                  if (_recentScreenshots.isNotEmpty) const SizedBox(height: 12),
                  if (_recentScreenshots.isNotEmpty)
                    Center(
                      child: TextButton.icon(
                        onPressed: () {
                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => const ScreenshotLogPage(),
                            ),
                          );
                        },
                        icon: const Icon(Icons.history),
                        label: const Text('查看全部历史'),
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  /// 构建单个截图条目
  Widget _buildScreenshotItem(ScreenshotLogEntry log) {
    final hasImage = log.filePath != null;
    final imageExists = hasImage ? File(log.filePath!).existsSync() : false;
    final timeText = DateFormat('MM-dd HH:mm:ss').format(log.timestamp);

    return InkWell(
      onTap: hasImage && imageExists
          ? () => _viewImage(log.filePath!, log.id)
          : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: log.success
                ? Colors.green.withOpacity(0.3)
                : Colors.red.withOpacity(0.3),
          ),
          color: log.success
              ? Colors.green.withOpacity(0.05)
              : Colors.red.withOpacity(0.05),
        ),
        child: Row(
          children: [
            // 状态图标
            CircleAvatar(
              backgroundColor: log.success ? Colors.green : Colors.red,
              radius: 12,
              child: Icon(
                log.success ? Icons.check : Icons.close,
                color: Colors.white,
                size: 14,
              ),
            ),
            const SizedBox(width: 12),

            // 主要信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          _getActionText(log.action),
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Text(
                        timeText,
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  if (hasImage) const SizedBox(height: 4),
                  if (hasImage)
                    Row(
                      children: [
                        Icon(
                          imageExists ? Icons.image : Icons.broken_image,
                          size: 14,
                          color: imageExists ? Colors.blue : Colors.grey,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            _getFileName(log.filePath!),
                            style: TextStyle(
                              fontSize: 12,
                              color: imageExists ? Colors.blue : Colors.grey,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  if (!log.success && log.error != null)
                    const SizedBox(height: 4),
                  if (!log.success && log.error != null)
                    Text(
                      log.error!,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Colors.red,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                ],
              ),
            ),

            // 跳转按钮
            if (hasImage && imageExists) const SizedBox(width: 8),
            if (hasImage && imageExists)
              Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: Colors.grey[600],
              ),
          ],
        ),
      ),
    );
  }

  /// 查看图片
  void _viewImage(String imagePath, String logId) {
    // 收集相关的所有截图路径，支持左右滑动查看
    final allImagePaths = _recentScreenshots
        .where(
            (log) => log.filePath != null && File(log.filePath!).existsSync())
        .map((log) => log.filePath!)
        .toList();

    final initialIndex = allImagePaths.indexOf(imagePath);

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ImageViewerPage(
          imagePath: imagePath,
          title: '截图 - $logId',
          imageList: allImagePaths.isNotEmpty ? allImagePaths : null,
          initialIndex: initialIndex >= 0 ? initialIndex : 0,
        ),
      ),
    );
  }

  /// 获取操作文本
  String _getActionText(String action) {
    switch (action) {
      case 'take_screenshot':
        return '立即截图';
      case 'scheduled_screenshot':
        return '定时截图';
      case 'batch_screenshot':
        return '批量截图';
      default:
        return action;
    }
  }

  /// 获取文件名
  String _getFileName(String filePath) {
    return filePath.split(Platform.pathSeparator).last;
  }
}

// 截图状态枚举
enum ScreenshotStatus {
  idle,
  capturing,
  processing,
  completed,
  error,
}
