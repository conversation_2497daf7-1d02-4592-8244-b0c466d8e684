/**
 * Flutter图片切换系统 - 统一网关服务
 *
 * 版本: v1.0.0 (统一网关架构)
 * 端口: 9999
 * 架构: 单端口多服务路由
 *
 * 功能特性:
 * - 统一端口管理: 所有服务通过端口9999访问
 * - 路由分离: 通过路径区分终端设备(/terminal/ws)和控制端(/controller/ws)
 * - 原生WebSocket: 高性能实时通信
 * - 设备管理: 完整的设备注册、发现和管理
 * - 命令传输: 控制端到终端设备的实时命令传输
 * - 心跳保活: 自动连接保活机制
 * - HTTP API: 完整的REST API接口
 *
 * 路由说明:
 * - GET  /                    - 服务信息
 * - GET  /health              - 健康检查
 * - GET  /terminal/status     - 终端设备状态
 * - GET  /controller/status   - 控制端状态
 * - WS   /terminal/ws         - 终端设备WebSocket连接
 * - WS   /controller/ws       - 控制端WebSocket连接
 *
 * 注意: 这是当前生产版本，请勿使用历史版本的网关服务
 *
 * <AUTHOR> Assistant
 * @date 2025-08-19
 * @version 1.0.0
 */

const { createServer } = require('http');
const WebSocket = require('ws');

console.log('🚀 启动统一网关服务 v1.0.0...');

// 存储连接的设备
const connectedDevices = new Map();
const connectedControllers = new Map();

// 创建HTTP服务器
const server = createServer((req, res) => {
  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  res.setHeader('Content-Type', 'application/json');

  if (req.url === '/health') {
    res.writeHead(200);
    res.end(JSON.stringify({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      connectedDevices: connectedDevices.size,
      connectedControllers: connectedControllers.size
    }));
  } else if (req.url === '/terminal/status') {
    res.writeHead(200);
    res.end(JSON.stringify({
      service: 'terminal',
      connectedDevices: connectedDevices.size,
      devices: Array.from(connectedDevices.keys())
    }));
  } else if (req.url === '/controller/status') {
    res.writeHead(200);
    res.end(JSON.stringify({
      service: 'controller',
      connectedControllers: connectedControllers.size,
      controllers: Array.from(connectedControllers.keys())
    }));
  } else {
    res.writeHead(200);
    res.end(JSON.stringify({
      name: 'Flutter Image Control Gateway (最终统一版)',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      services: {
        terminal: {
          path: '/terminal',
          websocket: '/terminal/ws',
          connectedDevices: connectedDevices.size
        },
        controller: {
          path: '/controller', 
          websocket: '/controller/ws',
          connectedControllers: connectedControllers.size
        }
      }
    }));
  }
});

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
  server: server
});

console.log('📡 WebSocket服务器已创建');

// WebSocket连接处理
wss.on('connection', (ws, req) => {
  const pathname = req.url;
  console.log('🔗 WebSocket连接建立，路径:', pathname);
  
  if (pathname === '/terminal/ws') {
    handleTerminalConnection(ws, req);
  } else if (pathname === '/controller/ws') {
    handleControllerConnection(ws, req);
  } else {
    console.log('❌ 未知WebSocket路径:', pathname);
    ws.send(JSON.stringify({
      type: 'error',
      message: '未知路径，请使用 /terminal/ws 或 /controller/ws'
    }));
    ws.close(1000, '未知路径');
  }
});

// 处理终端设备连接
function handleTerminalConnection(ws, req) {
  const clientId = `terminal_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  console.log('📱 终端设备连接:', clientId);
  
  // 存储连接信息
  connectedDevices.set(clientId, {
    ws: ws,
    connectedAt: new Date().toISOString(),
    lastHeartbeat: new Date().toISOString(),
    type: 'terminal'
  });
  
  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: '欢迎连接到终端设备服务',
    clientId: clientId,
    service: 'terminal',
    timestamp: new Date().toISOString()
  }));
  
  // 处理消息
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('📨 终端消息:', message);
      
      switch (message.type) {
        case 'device_register':
          console.log('📋 终端设备注册:', message.data);
          // 获取设备ID，支持多种字段名
          const deviceId = message.data?.id || message.data?.deviceId || 'unknown';
          console.log('🆔 设备ID:', deviceId);

          // 更新连接设备信息
          if (connectedDevices.has(clientId)) {
            const deviceInfo = connectedDevices.get(clientId);
            deviceInfo.deviceId = deviceId;
            deviceInfo.deviceData = message.data;
            deviceInfo.registeredAt = new Date().toISOString();
            connectedDevices.set(clientId, deviceInfo);
            console.log('✅ 设备信息已更新:', deviceId);
          }

          ws.send(JSON.stringify({
            type: 'device_registered',
            data: {
              success: true,
              deviceId: deviceId,
              clientId: clientId,
              service: 'terminal',
              timestamp: new Date().toISOString()
            }
          }));
          break;
          
        case 'heartbeat':
          console.log('💓 终端心跳:', clientId);
          if (connectedDevices.has(clientId)) {
            connectedDevices.get(clientId).lastHeartbeat = new Date().toISOString();
          }
          ws.send(JSON.stringify({
            type: 'heartbeat_ack',
            data: {
              timestamp: new Date().toISOString()
            }
          }));
          break;
          
        case 'ping':
          ws.send(JSON.stringify({
            type: 'pong',
            data: {
              timestamp: new Date().toISOString()
            }
          }));
          break;
          
        default:
          console.log('❓ 未知终端消息类型:', message.type);
          ws.send(JSON.stringify({
            type: 'error',
            data: {
              message: '未知消息类型',
              receivedType: message.type
            }
          }));
      }
    } catch (error) {
      console.error('❌ 终端消息解析错误:', error);
      ws.send(JSON.stringify({
        type: 'error',
        data: {
          message: '消息格式错误',
          error: error.message
        }
      }));
    }
  });
  
  // 处理连接错误
  ws.on('error', (error) => {
    console.error('❌ 终端WebSocket错误:', error);
  });
  
  // 处理断开连接
  ws.on('close', () => {
    console.log('📱 终端设备断开:', clientId);
    connectedDevices.delete(clientId);
  });
}

// 处理控制端连接
function handleControllerConnection(ws, req) {
  const clientId = `controller_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  console.log('🎮 控制端连接:', clientId);
  
  // 存储连接信息
  connectedControllers.set(clientId, {
    ws: ws,
    connectedAt: new Date().toISOString(),
    lastHeartbeat: new Date().toISOString(),
    type: 'controller'
  });
  
  // 发送欢迎消息
  ws.send(JSON.stringify({
    type: 'welcome',
    message: '欢迎连接到控制端服务',
    clientId: clientId,
    service: 'controller',
    timestamp: new Date().toISOString()
  }));
  
  // 处理消息
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('📨 控制端消息:', message);
      
      switch (message.type) {
        case 'get_devices':
          // 返回所有连接的终端设备
          ws.send(JSON.stringify({
            type: 'devices_list',
            data: {
              devices: Array.from(connectedDevices.keys()),
              count: connectedDevices.size,
              timestamp: new Date().toISOString()
            }
          }));
          break;
          
        case 'send_command':
          // 向指定终端设备发送命令
          const targetDevice = message.data?.targetDevice;
          const command = message.data?.command;
          
          if (connectedDevices.has(targetDevice)) {
            const deviceConnection = connectedDevices.get(targetDevice);
            deviceConnection.ws.send(JSON.stringify({
              type: 'command',
              data: command,
              from: clientId,
              timestamp: new Date().toISOString()
            }));
            
            ws.send(JSON.stringify({
              type: 'command_sent',
              data: {
                success: true,
                targetDevice: targetDevice,
                command: command
              }
            }));
          } else {
            ws.send(JSON.stringify({
              type: 'error',
              data: {
                message: '目标设备未连接',
                targetDevice: targetDevice
              }
            }));
          }
          break;
          
        case 'heartbeat':
          console.log('💓 控制端心跳:', clientId);
          if (connectedControllers.has(clientId)) {
            connectedControllers.get(clientId).lastHeartbeat = new Date().toISOString();
          }
          ws.send(JSON.stringify({
            type: 'heartbeat_ack',
            data: {
              timestamp: new Date().toISOString()
            }
          }));
          break;
          
        default:
          console.log('❓ 未知控制端消息类型:', message.type);
          ws.send(JSON.stringify({
            type: 'error',
            data: {
              message: '未知消息类型',
              receivedType: message.type
            }
          }));
      }
    } catch (error) {
      console.error('❌ 控制端消息解析错误:', error);
      ws.send(JSON.stringify({
        type: 'error',
        data: {
          message: '消息格式错误',
          error: error.message
        }
      }));
    }
  });
  
  // 处理连接错误
  ws.on('error', (error) => {
    console.error('❌ 控制端WebSocket错误:', error);
  });
  
  // 处理断开连接
  ws.on('close', () => {
    console.log('🎮 控制端断开:', clientId);
    connectedControllers.delete(clientId);
  });
}

// 监听WebSocket服务器事件
wss.on('error', (error) => {
  console.error('❌ WebSocket服务器错误:', error);
});

wss.on('listening', () => {
  console.log('👂 WebSocket服务器开始监听');
});

// 启动服务器
const PORT = process.env.PORT || 9998;
server.listen(PORT, () => {
  console.log('\n╔══════════════════════════════════════════════════════════════╗');
  console.log('║                                                              ║');
  console.log('║    Flutter Image Control Gateway (最终统一版)               ║');
  console.log('║    Version: 1.0.0                                           ║');
  console.log('║                                                              ║');
  console.log('╚══════════════════════════════════════════════════════════════╝\n');
  
  console.log(`🌐 服务器运行在: http://localhost:${PORT}`);
  console.log(`📱 终端设备WebSocket: ws://localhost:${PORT}/terminal/ws`);
  console.log(`🎮 控制端WebSocket: ws://localhost:${PORT}/controller/ws`);
  console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
  console.log(`⏰ 启动时间: ${new Date().toLocaleString('zh-CN')}`);
  console.log('');
  console.log('📋 可用路由:');
  console.log(`   GET  /                    - 服务信息`);
  console.log(`   GET  /health              - 健康检查`);
  console.log(`   GET  /terminal/status     - 终端设备状态`);
  console.log(`   GET  /controller/status   - 控制端状态`);
  console.log('');
});

// 监听服务器错误
server.on('error', (error) => {
  console.error('❌ HTTP服务器错误:', error);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  
  // 关闭所有WebSocket连接
  wss.clients.forEach((ws) => {
    ws.close();
  });
  
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
