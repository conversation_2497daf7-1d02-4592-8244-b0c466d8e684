---
trigger: manual
---
系统规则
- 请保持AI与我的对话全程使用中文；
- 项目结构前后端结构清晰，文件分类明确；
- 为了方便后续方便布署与分享，系统内资源使用相对路径来管理资源；
- 我的系统为 Windows,注意相代系统命令不要混用其它系统命令；
- 故障排除：帮助识别和解决运行时错误、逻辑错误及其他编程问题；
- 按功能规范区分不同的代码文件，单文件代码量尽可能不超过1000行，代码过长时需要按功能分块，或者折分为子文件依赖，通过方法调用来关联使用；
- 编写生成运行脚本时使用UTF-8编码，防止运行报错闪退；
- 代码中不要使用emoji字符，防止跨平台报错；
- 不要使用中文路径防止报错；
- 不需每次都编写功能更新md文件；
- 不要随意修改项目中设定的端口，如果端口被占用请先结束后再重启使用；
- 项目中使用的所有依赖包都需要在项目根目录下的package.json文件中进行配置；
- 素材资源放到项目根目录下的public文件夹中；