import 'dart:io';

/// 应用配置
class AppConfig {
  static AppConfig? _instance;
  static AppConfig get instance => _instance ??= AppConfig._();
  
  AppConfig._();

  // 应用信息
  static const String appName = 'Control Display App';
  static const String appVersion = '1.0.0';
  static const String appDescription = '图片切换控制端应用';

  // 网络配置
  static const String defaultGatewayHost = 'localhost';
  static const int defaultGatewayPort = 8080;
  static const Duration connectionTimeout = Duration(seconds: 10);
  static const Duration heartbeatInterval = Duration(seconds: 30);
  static const Duration reconnectDelay = Duration(seconds: 5);
  static const int maxReconnectAttempts = 5;

  // WebSocket配置
  static const String websocketPath = '/socket.io';
  static const bool enableWebSocketLogging = true;
  static const Duration websocketTimeout = Duration(seconds: 30);

  // 设备配置
  static const Duration deviceDiscoveryTimeout = Duration(seconds: 10);
  static const Duration deviceHeartbeatInterval = Duration(seconds: 15);
  static const Duration deviceOfflineTimeout = Duration(minutes: 2);

  // 图片配置
  static const int maxImageSize = 10 * 1024 * 1024; // 10MB
  static const List<String> supportedImageFormats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
  static const int defaultImageQuality = 85;
  static const int thumbnailSize = 200;

  // 缓存配置
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCachedImages = 1000;

  // 日志配置
  static const int maxLogFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxLogFiles = 5;
  static const bool enableFileLogging = true;
  static const bool enableConsoleLogging = true;

  // 系统托盘配置
  static const bool enableSystemTray = true;
  static const String trayIconPath = 'assets/icons/tray_icon.ico';
  static const String trayTooltip = 'Control Display App';

  // 窗口配置
  static const double defaultWindowWidth = 1200;
  static const double defaultWindowHeight = 800;
  static const double minWindowWidth = 1000;
  static const double minWindowHeight = 700;
  static const bool defaultWindowResizable = true;
  static const bool defaultWindowMaximizable = true;
  static const bool defaultWindowMinimizable = true;

  // UI配置
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const double defaultBorderRadius = 8.0;
  static const double defaultPadding = 16.0;
  static const double defaultMargin = 8.0;

  // 性能配置
  static const int maxConcurrentUploads = 3;
  static const int maxConcurrentDownloads = 5;
  static const Duration uploadTimeout = Duration(minutes: 5);
  static const Duration downloadTimeout = Duration(minutes: 3);

  // 安全配置
  static const Duration sessionTimeout = Duration(hours: 8);
  static const Duration tokenRefreshInterval = Duration(minutes: 30);
  static const int maxLoginAttempts = 5;
  static const Duration loginCooldown = Duration(minutes: 15);

  // 通知配置
  static const bool enableNotifications = true;
  static const Duration notificationDuration = Duration(seconds: 5);
  static const int maxNotifications = 10;

  // 开发配置
  static const bool enableDebugMode = true;
  static const bool enablePerformanceMonitoring = true;
  static const bool enableErrorReporting = true;

  // API配置
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  /// 获取网关WebSocket URL
  static String getGatewayWebSocketUrl(String host, int port) {
    return 'http://$host:$port';
  }

  /// 获取网关HTTP URL
  static String getGatewayHttpUrl(String host, int port) {
    return 'http://$host:$port';
  }

  /// 获取API基础URL
  static String getApiBaseUrl(String host, int port) {
    return 'http://$host:$port/api/$apiVersion';
  }

  /// 检查图片格式是否支持
  static bool isSupportedImageFormat(String fileName) {
    final extension = fileName.toLowerCase();
    return supportedImageFormats.any((format) => extension.endsWith(format));
  }

  /// 获取应用数据目录
  static Future<String> getAppDataDir() async {
    String baseDir;
    
    if (Platform.isWindows) {
      baseDir = Platform.environment['APPDATA'] ?? Platform.environment['USERPROFILE'] ?? '';
    } else if (Platform.isMacOS) {
      baseDir = '${Platform.environment['HOME']}/Library/Application Support';
    } else {
      baseDir = '${Platform.environment['HOME']}/.local/share';
    }
    
    return '$baseDir/$appName';
  }

  /// 获取缓存目录
  static Future<String> getCacheDir() async {
    final appDataDir = await getAppDataDir();
    return '$appDataDir/cache';
  }

  /// 获取日志目录
  static Future<String> getLogDir() async {
    final appDataDir = await getAppDataDir();
    return '$appDataDir/logs';
  }

  /// 获取配置文件路径
  static Future<String> getConfigFilePath() async {
    final appDataDir = await getAppDataDir();
    return '$appDataDir/config.json';
  }

  /// 验证网关URL格式
  static bool isValidGatewayUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && (uri.scheme == 'http' || uri.scheme == 'https') && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  /// 获取环境变量配置
  static String? getEnvConfig(String key, [String? defaultValue]) {
    return Platform.environment[key] ?? defaultValue;
  }

  /// 是否为调试模式
  static bool get isDebugMode {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  /// 是否为生产模式
  static bool get isProductionMode => !isDebugMode;

  /// 初始化配置
  Future<void> initialize() async {
    // 创建必要的目录
    final appDataDir = await getAppDataDir();
    final cacheDir = await getCacheDir();
    final logDir = await getLogDir();
    
    await Directory(appDataDir).create(recursive: true);
    await Directory(cacheDir).create(recursive: true);
    await Directory(logDir).create(recursive: true);
  }
}