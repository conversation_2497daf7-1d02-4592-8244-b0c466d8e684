import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../models/device_model.dart';
import '../models/device_info.dart' as device_info;
import 'dart:io' if (dart.library.html) '../utils/web_platform_stub.dart' as io;

/// 设备状态数据
class DeviceState {
  final DeviceStatus status;
  final device_info.DeviceInfo? deviceInfo;
  final String? errorMessage;
  final DateTime lastHeartbeat;
  final bool isConnected;
  final String? gatewayUrl;

  const DeviceState({
    required this.status,
    this.deviceInfo,
    this.errorMessage,
    required this.lastHeartbeat,
    this.isConnected = false,
    this.gatewayUrl,
  });

  DeviceState copyWith({
    DeviceStatus? status,
    device_info.DeviceInfo? deviceInfo,
    String? errorMessage,
    DateTime? lastHeartbeat,
    bool? isConnected,
    String? gatewayUrl,
  }) {
    return DeviceState(
      status: status ?? this.status,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      errorMessage: errorMessage ?? this.errorMessage,
      lastHeartbeat: lastHeartbeat ?? this.lastHeartbeat,
      isConnected: isConnected ?? this.isConnected,
      gatewayUrl: gatewayUrl ?? this.gatewayUrl,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceState &&
        other.status == status &&
        other.deviceInfo == deviceInfo &&
        other.errorMessage == errorMessage &&
        other.isConnected == isConnected &&
        other.gatewayUrl == gatewayUrl;
  }

  @override
  int get hashCode {
    return status.hashCode ^
        deviceInfo.hashCode ^
        errorMessage.hashCode ^
        isConnected.hashCode ^
        gatewayUrl.hashCode;
  }
}

/// 设备状态通知器
class DeviceStateNotifier extends StateNotifier<DeviceState> {
  DeviceStateNotifier()
      : super(DeviceState(
          status: DeviceStatus.offline,
          deviceInfo: _getInitialDeviceInfo(),
          lastHeartbeat: DateTime.now(),
        ));

  /// 获取初始设备信息
  static device_info.DeviceInfo _getInitialDeviceInfo() {
    final now = DateTime.now();

    // 尝试获取主机名作为默认设备名
    String deviceName = 'Terminal设备';
    String deviceId = 'terminal_default';

    try {
      if (kIsWeb) {
        deviceName = 'Web-Terminal';
        deviceId = 'web_terminal_default';
      } else {
        final hostname = io.Platform.localHostname;
        if (hostname.isNotEmpty) {
          deviceName = hostname;
          deviceId = '${_getSafeOperatingSystem()}_${hostname}_terminal';
        }
      }
    } catch (e) {
      // 使用默认值
    }

    return device_info.DeviceInfo(
      id: deviceId,
      name: deviceName,
      type: DeviceType.terminal,
      platform: _getSafeOperatingSystem(),
      platformVersion: _getSafeOperatingSystemVersion(),
      model: kIsWeb ? 'Web Browser' : 'PC',
      manufacturer: kIsWeb ? 'Browser' : 'Unknown',
      ipAddress: '127.0.0.1',
      macAddress: null,
      networkName: null,
      screenResolution: null,
      capabilities: DeviceCapabilities.getDefault(),
      status: DeviceStatus.offline,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 安全获取操作系统信息（Web兼容）
  static String _getSafeOperatingSystem() {
    if (kIsWeb) {
      return 'web';
    }

    try {
      return io.Platform.operatingSystem;
    } catch (e) {
      return 'unknown';
    }
  }

  /// 安全获取操作系统版本（Web兼容）
  static String _getSafeOperatingSystemVersion() {
    if (kIsWeb) {
      return 'Web Browser';
    }

    try {
      return io.Platform.operatingSystemVersion;
    } catch (e) {
      return 'Unknown';
    }
  }

  /// 更新设备状态
  void updateStatus(DeviceStatus status, {String? errorMessage}) {
    state = state.copyWith(
      status: status,
      errorMessage: errorMessage,
      lastHeartbeat: DateTime.now(),
    );
  }

  /// 设置设备信息
  void setDeviceInfo(device_info.DeviceInfo deviceInfo) {
    state = state.copyWith(
      deviceInfo: deviceInfo,
      lastHeartbeat: DateTime.now(),
    );
  }

  /// 设置连接状态
  void setConnected(bool isConnected, {String? gatewayUrl}) {
    debugPrint('🔄 设置设备连接状态: $isConnected, 网关: $gatewayUrl');
    state = state.copyWith(
      isConnected: isConnected,
      gatewayUrl: gatewayUrl,
      status: isConnected ? DeviceStatus.online : DeviceStatus.offline,
      lastHeartbeat: DateTime.now(),
    );
    debugPrint('📊 设备状态已更新: 连接=${state.isConnected}, 状态=${state.status}');
  }

  /// 更新心跳
  void updateHeartbeat() {
    state = state.copyWith(
      lastHeartbeat: DateTime.now(),
    );
  }

  /// 清除错误
  void clearError() {
    state = state.copyWith(
      errorMessage: null,
      lastHeartbeat: DateTime.now(),
    );
  }

  /// 重置状态
  void reset() {
    state = DeviceState(
      status: DeviceStatus.offline,
      lastHeartbeat: DateTime.now(),
    );
  }

  /// 初始化设备
  Future<void> initializeDevice() async {
    try {
      updateStatus(DeviceStatus.registering);

      // 获取设备信息
      debugPrint('🔍 正在获取设备信息...');
      final deviceInfo = await device_info.DeviceInfo.getCurrentDevice();

      // 设置设备信息到状态
      setDeviceInfo(deviceInfo);

      debugPrint('✅ 设备信息初始化成功: ${deviceInfo.name} (${deviceInfo.id})');
      updateStatus(DeviceStatus.registered);
    } catch (e) {
      debugPrint('❌ 设备初始化失败: $e');
      updateStatus(DeviceStatus.error, errorMessage: e.toString());
      rethrow;
    }
  }
}

/// 设备状态Provider
final deviceProvider = StateNotifierProvider<DeviceStateNotifier, DeviceState>(
  (ref) => DeviceStateNotifier(),
);
