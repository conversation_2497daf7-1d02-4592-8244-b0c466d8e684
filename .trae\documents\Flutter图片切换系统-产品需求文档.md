# Flutter图片切换系统 - 产品需求文档

## 1. Product Overview

基于Flutter技术栈重构的跨平台分布式图片切换控制系统，支持多服务器同时截屏、实时图片推送和全屏显示控制。

该系统解决了传统多机截屏控制系统的跨平台兼容性问题，为会议室、监控中心、展厅等场景提供统一的图片控制解决方案。通过Flutter统一技术栈，实现桌面端、移动端的一体化控制体验。

目标市场价值：为企业级多屏显示场景提供现代化、高效的分布式图片控制平台。

## 2. Core Features

### 2.1 User Roles

| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| 系统管理员 | 默认账户登录 | 设备管理、系统配置、用户权限管理 |
| 控制操作员 | 管理员分配账户 | 截图控制、图片推送、设备监控 |
| 观察用户 | 只读权限账户 | 查看设备状态、浏览截图历史 |

### 2.2 Feature Module

我们的Flutter图片切换系统包含以下主要页面：

1. **主控制台页面**：设备管理面板、截图控制中心、实时状态监控
2. **设备管理页面**：设备发现与配置、分组管理、远程控制设置
3. **截图控制页面**：批量截图操作、实时预览、定时截图配置
4. **图片管理页面**：图片库管理、分类标签、批量操作
5. **同步显示页面**：多屏同步控制、显示效果配置、轮播设置
6. **系统设置页面**：网络配置、性能优化、日志管理
7. **移动端控制页面**：快速截图、远程显示控制、设备状态查看

### 2.3 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 主控制台页面 | 设备状态面板 | 实时显示所有连接设备的在线状态、CPU使用率、网络延迟等关键指标 |
| 主控制台页面 | 快速操作区 | 提供一键截图、批量显示、紧急停止等常用功能的快捷按钮 |
| 主控制台页面 | 实时预览网格 | 显示最近截图的缩略图预览，支持点击放大查看 |
| 设备管理页面 | 自动发现模块 | 扫描局域网内的终端设备，自动识别并添加到设备列表 |
| 设备管理页面 | 设备分组管理 | 创建设备组、拖拽分组、批量配置组内设备参数 |
| 设备管理页面 | 远程配置面板 | 远程修改终端设备的截图质量、显示参数、网络设置 |
| 截图控制页面 | 批量截图控制 | 选择多个设备同时执行截图，支持自定义截图区域和质量 |
| 截图控制页面 | 定时任务管理 | 设置定时截图任务，配置执行频率和保存策略 |
| 截图控制页面 | 实时预览显示 | 显示截图进度和结果预览，支持失败重试和错误诊断 |
| 图片管理页面 | 图片库浏览 | 按时间、设备、标签分类浏览历史截图，支持搜索和筛选 |
| 图片管理页面 | 批量操作工具 | 批量删除、导出、标记图片，支持拖拽选择和快捷键操作 |
| 同步显示页面 | 多屏同步控制 | 选择图片组和目标设备，实现多屏幕同步显示相同内容 |
| 同步显示页面 | 显示效果配置 | 设置显示模式（全屏、适应、拉伸）、切换动画、停留时间 |
| 同步显示页面 | 轮播管理 | 创建图片轮播序列，设置切换间隔和循环模式 |
| 系统设置页面 | 网络配置 | 配置服务器地址、端口、连接超时等网络参数 |
| 系统设置页面 | 性能优化 | 调整并发数量、缓存大小、图片压缩等性能参数 |
| 移动端控制页面 | 快速截图 | 移动端一键截图功能，支持选择设备和截图区域 |
| 移动端控制页面 | 远程显示控制 | 移动端推送图片到指定设备显示，支持手势操作 |

## 3. Core Process

### 管理员操作流程
1. 登录系统 → 设备管理 → 添加/配置终端设备 → 创建设备分组 → 设置权限和参数
2. 截图控制 → 选择设备/设备组 → 执行截图 → 查看结果 → 保存到图片库
3. 同步显示 → 选择图片 → 选择目标设备 → 配置显示参数 → 执行同步显示

### 操作员日常流程
1. 登录系统 → 查看设备状态 → 执行截图任务 → 管理图片库 → 控制显示内容
2. 移动端快速操作 → 选择设备 → 一键截图 → 推送显示

### 系统自动化流程
1. 设备自动发现 → 状态监控 → 定时截图 → 自动归档 → 异常报警

```mermaid
graph TD
    A[登录页面] --> B[主控制台]
    B --> C[设备管理]
    B --> D[截图控制]
    B --> E[图片管理]
    B --> F[同步显示]
    B --> G[系统设置]
    
    C --> C1[设备发现]
    C --> C2[设备配置]
    C --> C3[分组管理]
    
    D --> D1[批量截图]
    D --> D2[定时任务]
    D --> D3[实时预览]
    
    E --> E1[图片浏览]
    E --> E2[分类管理]
    E --> E3[批量操作]
    
    F --> F1[选择图片]
    F --> F2[选择设备]
    F --> F3[同步显示]
    
    H[移动端] --> B
    H --> I[快速截图]
    H --> J[远程控制]
```

## 4. User Interface Design

### 4.1 Design Style

- **主色调**：深蓝色 (#1E3A8A) 和科技蓝 (#3B82F6)
- **辅助色**：成功绿 (#10B981)、警告橙 (#F59E0B)、错误红 (#EF4444)
- **按钮样式**：圆角矩形，支持悬停和点击动画效果
- **字体**：Roboto (英文) / 思源黑体 (中文)，主要字号 14px-16px
- **布局风格**：卡片式设计，左侧导航 + 主内容区域
- **图标风格**：Material Design 图标系统，支持主题色彩适配

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 主控制台页面 | 设备状态面板 | 网格布局卡片，实时状态指示灯，进度条显示资源使用率 |
| 主控制台页面 | 快速操作区 | 大按钮设计，图标+文字组合，悬停效果和点击反馈 |
| 设备管理页面 | 设备列表 | 表格布局，状态标签，操作按钮组，支持拖拽排序 |
| 截图控制页面 | 批量选择器 | 复选框列表，全选/反选按钮，选中数量提示 |
| 截图控制页面 | 预览网格 | 响应式网格布局，图片缩略图，加载动画效果 |
| 图片管理页面 | 图片浏览器 | 瀑布流布局，懒加载，缩放预览，批量选择模式 |
| 同步显示页面 | 设备映射 | 拖拽连线界面，设备图标，连接状态可视化 |
| 移动端界面 | 触控优化 | 大按钮设计，手势支持，底部导航栏，侧滑菜单 |

### 4.3 Responsiveness

系统采用桌面优先设计，同时支持移动端自适应。桌面端支持多窗口操作和快捷键，移动端优化触控交互，支持手势操作和语音控制。所有界面组件支持深色/浅色主题切换。