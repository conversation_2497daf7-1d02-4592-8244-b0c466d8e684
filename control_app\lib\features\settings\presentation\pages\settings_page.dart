import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/config/app_constants.dart';
import '../../../../core/providers/auth_provider.dart';

class SettingsPage extends ConsumerStatefulWidget {
  const SettingsPage({super.key});

  @override
  ConsumerState<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends ConsumerState<SettingsPage> {
  final _formKey = GlobalKey<FormState>();

  // 网关设置
  late TextEditingController _gatewayUrlController;
  late TextEditingController _gatewayPortController;

  // 显示设置
  bool _autoRefresh = true;
  int _refreshInterval = 5;
  bool _showNotifications = true;

  // 图片设置
  int _imageQuality = 80;
  bool _autoCompress = true;
  int _maxImageSize = 1920;

  // 网络设置
  int _connectionTimeout = 30;
  int _retryAttempts = 3;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadSettings();
  }

  void _initializeControllers() {
    // 预填充网关配置（用于开发测试）
    _gatewayUrlController = TextEditingController(text: '192.168.1.100');
    _gatewayPortController = TextEditingController(text: '3000');
  }

  Future<void> _loadSettings() async {
    try {
      final storageService = ref.read(storageServiceProvider);

      // 加载网关设置
      _gatewayUrlController.text =
          await storageService.getString('gateway_url') ??
              AppConstants.defaultGatewayUrl;
      _gatewayPortController.text =
          (await storageService.getInt('gateway_port') ??
                  AppConstants.defaultGatewayPort)
              .toString();

      // 加载显示设置
      _autoRefresh = await storageService.getBool('auto_refresh') ?? true;
      _refreshInterval = await storageService.getInt('refresh_interval') ?? 5;
      _showNotifications =
          await storageService.getBool('show_notifications') ?? true;

      // 加载图片设置
      _imageQuality = await storageService.getInt('image_quality') ?? 80;
      _autoCompress = await storageService.getBool('auto_compress') ?? true;
      _maxImageSize = await storageService.getInt('max_image_size') ?? 1920;

      // 加载网络设置
      _connectionTimeout =
          await storageService.getInt('connection_timeout') ?? 30;
      _retryAttempts = await storageService.getInt('retry_attempts') ?? 3;

      setState(() {});
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载设置失败: $e',
                style: TextStyle(color: Theme.of(context).colorScheme.onError)),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    try {
      final storageService = ref.read(storageServiceProvider);

      // 保存网关设置
      await storageService.setString('gateway_url', _gatewayUrlController.text);
      await storageService.setInt(
          'gateway_port', int.parse(_gatewayPortController.text));

      // 保存显示设置
      await storageService.setBool('auto_refresh', _autoRefresh);
      await storageService.setInt('refresh_interval', _refreshInterval);
      await storageService.setBool('show_notifications', _showNotifications);

      // 保存图片设置
      await storageService.setInt('image_quality', _imageQuality);
      await storageService.setBool('auto_compress', _autoCompress);
      await storageService.setInt('max_image_size', _maxImageSize);

      // 保存网络设置
      await storageService.setInt('connection_timeout', _connectionTimeout);
      await storageService.setInt('retry_attempts', _retryAttempts);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('设置已保存',
                style:
                    TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存设置失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _gatewayUrlController.dispose();
    _gatewayPortController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text('设置',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        iconTheme:
            IconThemeData(color: Theme.of(context).colorScheme.onSurface),
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: Text(
              '保存',
              style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(8),
          children: [
            _buildGatewaySection(),
            const SizedBox(height: 12),
            _buildDisplaySection(),
            const SizedBox(height: 24),
            _buildImageSection(),
            const SizedBox(height: 24),
            _buildNetworkSection(),
            const SizedBox(height: 24),
            _buildAccountSection(),
            const SizedBox(height: 24),
            _buildAboutSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildGatewaySection() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '网关设置',
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: _gatewayUrlController,
              style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
              decoration: InputDecoration(
                labelText: '网关地址',
                labelStyle: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6)),
                hintText: '192.168.1.100',
                hintStyle: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6)),
                border: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.primary),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入网关地址';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _gatewayPortController,
              style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
              decoration: InputDecoration(
                labelText: '网关端口',
                labelStyle: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6)),
                hintText: '3000',
                hintStyle: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6)),
                border: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide:
                      BorderSide(color: Theme.of(context).colorScheme.primary),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入网关端口';
                }
                final port = int.tryParse(value);
                if (port == null || port < 1 || port > 65535) {
                  return '请输入有效的端口号 (1-65535)';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplaySection() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '显示设置',
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text('自动刷新',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface)),
              subtitle: Text('自动刷新设备状态和图片',
                  style: TextStyle(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6))),
              value: _autoRefresh,
              activeColor: Theme.of(context).colorScheme.primary,
              onChanged: (value) {
                setState(() {
                  _autoRefresh = value;
                });
              },
            ),
            if (_autoRefresh) ...[
              const SizedBox(height: 8),
              Text(
                '刷新间隔: $_refreshInterval秒',
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
              ),
              Slider(
                value: _refreshInterval.toDouble(),
                min: 1,
                max: 60,
                divisions: 59,
                label: '$_refreshInterval秒',
                activeColor: Theme.of(context).colorScheme.primary,
                inactiveColor:
                    Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                onChanged: (value) {
                  setState(() {
                    _refreshInterval = value.round();
                  });
                },
              ),
            ],
            SwitchListTile(
              title: Text('显示通知',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface)),
              subtitle: Text('显示系统通知和提醒',
                  style: TextStyle(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6))),
              value: _showNotifications,
              activeColor: Theme.of(context).colorScheme.primary,
              onChanged: (value) {
                setState(() {
                  _showNotifications = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '图片设置',
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            const SizedBox(height: 16),
            Text(
              '图片质量: $_imageQuality%',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            Slider(
              value: _imageQuality.toDouble(),
              min: 10,
              max: 100,
              divisions: 90,
              label: '$_imageQuality%',
              activeColor: Theme.of(context).colorScheme.primary,
              inactiveColor:
                  Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _imageQuality = value.round();
                });
              },
            ),
            SwitchListTile(
              title: Text('自动压缩',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface)),
              subtitle: Text('自动压缩大尺寸图片',
                  style: TextStyle(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6))),
              value: _autoCompress,
              activeColor: Theme.of(context).colorScheme.primary,
              onChanged: (value) {
                setState(() {
                  _autoCompress = value;
                });
              },
            ),
            if (_autoCompress) ...[
              const SizedBox(height: 8),
              Text(
                '最大尺寸: $_maxImageSize px',
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium
                    ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
              ),
              Slider(
                value: _maxImageSize.toDouble(),
                min: 720,
                max: 4096,
                divisions: 20,
                label: '$_maxImageSize px',
                activeColor: Theme.of(context).colorScheme.primary,
                inactiveColor:
                    Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
                onChanged: (value) {
                  setState(() {
                    _maxImageSize = value.round();
                  });
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildNetworkSection() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '网络设置',
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            const SizedBox(height: 16),
            Text(
              '连接超时: $_connectionTimeout秒',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            Slider(
              value: _connectionTimeout.toDouble(),
              min: 5,
              max: 120,
              divisions: 23,
              label: '$_connectionTimeout秒',
              activeColor: Theme.of(context).colorScheme.primary,
              inactiveColor:
                  Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _connectionTimeout = value.round();
                });
              },
            ),
            const SizedBox(height: 8),
            Text(
              '重试次数: $_retryAttempts次',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            Slider(
              value: _retryAttempts.toDouble(),
              min: 1,
              max: 10,
              divisions: 9,
              label: '$_retryAttempts次',
              activeColor: Theme.of(context).colorScheme.primary,
              inactiveColor:
                  Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
              onChanged: (value) {
                setState(() {
                  _retryAttempts = value.round();
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSection() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '账户设置',
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            const SizedBox(height: 16),
            Builder(
              builder: (context) {
                final authNotifier = ref.watch(authProvider.notifier);
                return Column(
                  children: [
                    ListTile(
                      leading: Icon(Icons.person,
                          color: Theme.of(context).colorScheme.onSurface),
                      title: Text('当前用户',
                          style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface)),
                      subtitle: Text(authNotifier.currentUser?.name ?? '未登录',
                          style: TextStyle(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurface
                                  .withOpacity(0.6))),
                      contentPadding: EdgeInsets.zero,
                    ),
                    ListTile(
                      leading: Icon(Icons.lock,
                          color: Theme.of(context).colorScheme.onSurface),
                      title: Text('修改密码',
                          style: TextStyle(
                              color: Theme.of(context).colorScheme.onSurface)),
                      trailing: Icon(Icons.arrow_forward_ios,
                          color: Theme.of(context).colorScheme.onSurface),
                      contentPadding: EdgeInsets.zero,
                      onTap: _changePassword,
                    ),
                    ListTile(
                      leading: Icon(Icons.logout,
                          color: Theme.of(context).colorScheme.error),
                      title: Text('退出登录',
                          style: TextStyle(
                              color: Theme.of(context).colorScheme.error)),
                      contentPadding: EdgeInsets.zero,
                      onTap: _logout,
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      color: Theme.of(context).colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '关于',
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(color: Theme.of(context).colorScheme.onSurface),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.info,
                  color: Theme.of(context).colorScheme.onSurface),
              title: Text('应用版本',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface)),
              subtitle: Text('1.0.0',
                  style: TextStyle(
                      color: Theme.of(context)
                          .colorScheme
                          .onSurface
                          .withOpacity(0.6))),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: Icon(Icons.update,
                  color: Theme.of(context).colorScheme.onSurface),
              title: Text('检查更新',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface)),
              trailing: Icon(Icons.arrow_forward_ios,
                  color: Theme.of(context).colorScheme.onSurface),
              contentPadding: EdgeInsets.zero,
            ),
            ListTile(
              leading: Icon(Icons.help,
                  color: Theme.of(context).colorScheme.onSurface),
              title: Text('帮助与支持',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface)),
              trailing: Icon(Icons.arrow_forward_ios,
                  color: Theme.of(context).colorScheme.onSurface),
              contentPadding: EdgeInsets.zero,
              onTap: _showHelp,
            ),
            ListTile(
              leading: Icon(Icons.info,
                  color: Theme.of(context).colorScheme.onSurface),
              title: Text('关于应用',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface)),
              trailing: Icon(Icons.arrow_forward_ios,
                  color: Theme.of(context).colorScheme.onSurface),
              contentPadding: EdgeInsets.zero,
              onTap: _showTerms,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _changePassword() async {
    // TODO: 实现修改密码功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('修改密码功能待实现',
            style: TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Future<void> _logout() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text('确认退出',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        content: Text('确定要退出登录吗？',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('取消',
                style: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6))),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text('退出',
                style:
                    TextStyle(color: Theme.of(context).colorScheme.onSurface)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final authProviderInstance = ref.read(authProvider.notifier);
      await authProviderInstance.logout();

      if (mounted) {
        // 导航到登录页面
      }
    }
  }

  Future<void> _checkForUpdates() async {
    // TODO: 实现检查更新功能
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('当前已是最新版本',
            style: TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text('帮助与支持',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        content: Text(
          '如需帮助，请联系技术支持：\n\n'
          '邮箱: <EMAIL>\n'
          '电话: 400-123-4567\n'
          '工作时间: 周一至周五 9:00-18:00',
          style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭',
                style: TextStyle(color: Theme.of(context).colorScheme.primary)),
          ),
        ],
      ),
    );
  }

  void _showTerms() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text('使用条款',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        content: SingleChildScrollView(
          child: Text(
            '1. 用户协议\n\n'
            '本应用仅供授权用户使用，请遵守相关法律法规。\n\n'
            '2. 隐私政策\n\n'
            '我们重视您的隐私，不会收集或分享您的个人信息。\n\n'
            '3. 免责声明\n\n'
            '本应用按"现状"提供，不承担任何明示或暗示的担保。',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('关闭',
                style: TextStyle(color: Theme.of(context).colorScheme.primary)),
          ),
        ],
      ),
    );
  }
}
