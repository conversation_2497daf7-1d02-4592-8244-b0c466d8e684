name: flutter_imgct_control
description: Flutter图片切换系统 - 控制端应用
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # UI组件
  cupertino_icons: ^1.0.6
  material_design_icons_flutter: ^7.0.7296
  
  # 状态管理
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # 路由管理
  go_router: ^12.1.3
  
  # 网络通信
  http: ^1.1.2
  socket_io_client: ^2.0.3+1
  dio: ^5.4.0
  web_socket_channel: ^2.4.0
  
  # 本地存储
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # 图片处理
  cached_network_image: ^3.3.0
  image_picker: ^1.0.4
  photo_view: ^0.14.0
  flutter_image_compress: ^2.1.0
  image: ^4.1.3
  
  # 文件处理
  path_provider: ^2.1.1
  file_picker: ^8.0.0+1
  
  # UI增强
  flutter_staggered_grid_view: ^0.7.0
  pull_to_refresh: ^2.0.0
  flutter_slidable: ^3.0.1
  
  # 工具库
  intl: ^0.19.0
  uuid: ^4.2.1
  logger: ^2.0.2+1
  equatable: ^2.0.5
  crypto: ^3.0.3
  
  # 权限管理 (临时禁用以解决Windows构建问题)
  # permission_handler: ^11.1.0
  
  # 设备信息
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  
  # 窗口管理
  window_manager: ^0.3.7
  system_tray: ^0.1.1
  
  # 网络检测
  connectivity_plus: ^5.0.2
  
  # 二维码
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1
  getwidget: ^6.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  
  flutter_lints: ^3.0.1
  hive_generator: ^2.0.1
  build_runner: ^2.4.7
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/config/
  
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700