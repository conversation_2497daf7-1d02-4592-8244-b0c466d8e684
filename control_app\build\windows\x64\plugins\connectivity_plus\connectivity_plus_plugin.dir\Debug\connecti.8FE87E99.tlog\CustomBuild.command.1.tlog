^E:\AI_CODEE\IMGCT\CONTROL_APP\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\CONNECTIVITY_PLUS\WINDOWS\CMAKELISTS.TXT
setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-file E:/AI_codeE/imgCT/control_app/build/windows/x64/plugins/connectivity_plus/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
