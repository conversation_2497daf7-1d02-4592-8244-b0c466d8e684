import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/storage_service.dart';
import '../config/app_constants.dart';
import 'auth_provider.dart';

/// 应用状态
class AppState {
  final bool isInitializing;
  final ThemeMode themeMode;
  final Locale locale;
  final bool isFirstLaunch;
  final String version;
  
  const AppState({
    this.isInitializing = true,
    this.themeMode = ThemeMode.system,
    this.locale = const Locale('zh', 'CN'),
    this.isFirstLaunch = true,
    this.version = '1.0.0',
  });
  
  AppState copyWith({
    bool? isInitializing,
    ThemeMode? themeMode,
    Locale? locale,
    bool? isFirstLaunch,
    String? version,
  }) {
    return AppState(
      isInitializing: isInitializing ?? this.isInitializing,
      themeMode: themeMode ?? this.themeMode,
      locale: locale ?? this.locale,
      isFirstLaunch: isFirstLaunch ?? this.isFirstLaunch,
      version: version ?? this.version,
    );
  }
}

/// 应用状态提供者
class AppStateNotifier extends StateNotifier<AppState> {
  final StorageService _storageService;
  
  AppStateNotifier(this._storageService) : super(const AppState()) {
    _initialize();
  }
  
  /// 初始化应用状态
  Future<void> _initialize() async {
    try {
      // 加载保存的设置
      final settings = await _storageService.getMap(AppConstants.appSettingsKey) ?? {};
      
      // 检查是否首次启动
      final isFirstLaunch = await _storageService.getBool('is_first_launch') ?? true;
      
      // 加载主题模式
      final themeModeString = settings['themeMode'] as String? ?? 'system';
      ThemeMode themeMode;
      switch (themeModeString) {
        case 'light':
          themeMode = ThemeMode.light;
          break;
        case 'dark':
          themeMode = ThemeMode.dark;
          break;
        default:
          themeMode = ThemeMode.system;
      }
      
      // 加载语言设置
      final languageCode = settings['languageCode'] as String? ?? 'zh';
      final countryCode = settings['countryCode'] as String? ?? 'CN';
      final locale = Locale(languageCode, countryCode);
      
      // 更新状态
      state = state.copyWith(
        isInitializing: false,
        themeMode: themeMode,
        locale: locale,
        isFirstLaunch: isFirstLaunch,
        version: AppConstants.appVersion,
      );
      
      // 如果是首次启动，标记为已启动
      if (isFirstLaunch) {
        await _storageService.setBool('is_first_launch', false);
      }
    } catch (e) {
      debugPrint('Error initializing app state: $e');
      // 使用默认状态
      state = state.copyWith(isInitializing: false);
    }
  }
  
  /// 设置主题模式
  Future<void> setThemeMode(ThemeMode themeMode) async {
    try {
      state = state.copyWith(themeMode: themeMode);
      
      // 保存设置
      final settings = await _storageService.getMap(AppConstants.appSettingsKey) ?? {};
      settings['themeMode'] = themeMode.name;
      await _storageService.setMap(AppConstants.appSettingsKey, settings);
    } catch (e) {
      debugPrint('Error setting theme mode: $e');
    }
  }
  
  /// 设置语言
  Future<void> setLocale(Locale locale) async {
    try {
      state = state.copyWith(locale: locale);
      
      // 保存设置
      final settings = await _storageService.getMap(AppConstants.appSettingsKey) ?? {};
      settings['languageCode'] = locale.languageCode;
      settings['countryCode'] = locale.countryCode;
      await _storageService.setMap(AppConstants.appSettingsKey, settings);
    } catch (e) {
      debugPrint('Error setting locale: $e');
    }
  }
  
  /// 重置应用状态
  Future<void> reset() async {
    try {
      await _storageService.remove(AppConstants.appSettingsKey);
      await _storageService.setBool('is_first_launch', true);
      
      state = const AppState();
      await _initialize();
    } catch (e) {
      debugPrint('Error resetting app state: $e');
    }
  }
  
  /// 获取当前设置
  Map<String, dynamic> get currentSettings {
    return {
      'themeMode': state.themeMode.name,
      'languageCode': state.locale.languageCode,
      'countryCode': state.locale.countryCode,
      'isFirstLaunch': state.isFirstLaunch,
      'version': state.version,
    };
  }
}

/// 应用状态提供者实例
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  final storageService = ref.watch(storageServiceProvider);
  return AppStateNotifier(storageService);
});

/// 主题模式提供者
final themeModeProvider = Provider<ThemeMode>((ref) {
  final appState = ref.watch(appStateProvider);
  return appState.themeMode;
});

/// 语言提供者
final localeProvider = Provider<Locale>((ref) {
  final appState = ref.watch(appStateProvider);
  return appState.locale;
});

/// 是否首次启动提供者
final isFirstLaunchProvider = Provider<bool>((ref) {
  final appState = ref.watch(appStateProvider);
  return appState.isFirstLaunch;
});

/// 是否正在初始化提供者
final isInitializingProvider = Provider<bool>((ref) {
  final appState = ref.watch(appStateProvider);
  return appState.isInitializing;
});