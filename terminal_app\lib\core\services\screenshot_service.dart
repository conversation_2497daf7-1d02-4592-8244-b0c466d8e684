import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' show ImageByteFormat;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:screenshot/screenshot.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

import 'logger_service.dart';
import 'storage_service.dart';
import 'websocket_service.dart';
import 'image_compression_service.dart';
import 'screenshot_log_service.dart';
import 'screenshot_config_service.dart';

/// 截图配置
class ScreenshotConfig {
  final double pixelRatio;
  final ImageByteFormat format;
  final int quality;
  final bool includeMetadata;
  final String? customPath;
  final bool enableCompression;

  const ScreenshotConfig({
    this.pixelRatio = 1.0,
    this.format = ImageByteFormat.png,
    this.quality = 100,
    this.includeMetadata = true,
    this.customPath,
    this.enableCompression = false,
  });

  factory ScreenshotConfig.fromJson(Map<String, dynamic> json) {
    return ScreenshotConfig(
      pixelRatio: (json['pixelRatio'] as num?)?.toDouble() ?? 1.0,
      format: ImageByteFormat.values.firstWhere(
        (e) => e.name == json['format'],
        orElse: () => ImageByteFormat.png,
      ),
      quality: json['quality'] as int? ?? 100,
      includeMetadata: json['includeMetadata'] as bool? ?? true,
      customPath: json['customPath'] as String?,
      enableCompression: json['enableCompression'] as bool? ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pixelRatio': pixelRatio,
      'format': format.name,
      'quality': quality,
      'includeMetadata': includeMetadata,
      'customPath': customPath,
      'enableCompression': enableCompression,
    };
  }
}

/// 截图结果
class ScreenshotResult {
  final String id;
  final bool success;
  final List<String> filePaths;
  final Uint8List? imageData;
  final String? error;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;

  const ScreenshotResult({
    required this.id,
    required this.success,
    this.filePaths = const [],
    this.imageData,
    this.error,
    this.metadata = const {},
    required this.timestamp,
  });

  /// 主要文件路径（第一个保存的文件）
  String? get filePath => filePaths.isNotEmpty ? filePaths.first : null;

  factory ScreenshotResult.success({
    required String id,
    List<String> filePaths = const [],
    Uint8List? imageData,
    Map<String, dynamic> metadata = const {},
  }) {
    return ScreenshotResult(
      id: id,
      success: true,
      filePaths: filePaths,
      imageData: imageData,
      metadata: metadata,
      timestamp: DateTime.now(),
    );
  }

  factory ScreenshotResult.failure({
    required String id,
    required String error,
    Map<String, dynamic> metadata = const {},
  }) {
    return ScreenshotResult(
      id: id,
      success: false,
      error: error,
      metadata: metadata,
      timestamp: DateTime.now(),
    );
  }
}

/// 截图服务
class ScreenshotService {
  static ScreenshotService? _instance;
  static ScreenshotService get instance => _instance ??= ScreenshotService._();

  ScreenshotService._();

  final ScreenshotController _screenshotController = ScreenshotController();
  final Map<String, Timer> _scheduledScreenshots = {};
  final StreamController<ScreenshotResult> _resultController =
      StreamController<ScreenshotResult>.broadcast();

  /// 截图结果流
  Stream<ScreenshotResult> get resultStream => _resultController.stream;

  /// 初始化截图服务
  Future<void> initialize() async {
    try {
      // 初始化截图日志服务
      await ScreenshotLogService.instance.initialize();

      // 创建默认截图目录
      await _ensureScreenshotDirectory();

      // 监听WebSocket消息
      _listenToWebSocketMessages();

      LoggerService.info('Screenshot service initialized');
    } catch (error) {
      LoggerService.error('Failed to initialize screenshot service', error);
      rethrow;
    }
  }

  /// 监听WebSocket消息
  void _listenToWebSocketMessages() {
    WebSocketService.instance.messageStream.listen((message) {
      final messageType = message['type'] as String?;
      if (messageType == 'capture' || messageType == 'screenshot') {
        _handleScreenshotMessage(message);
      }
    });
  }

  /// 处理截图消息
  Future<void> _handleScreenshotMessage(Map<String, dynamic> message) async {
    try {
      final action = message['action'] as String?;
      switch (action) {
        case 'screenshot_take':
          await _handleTakeScreenshot(message);
          break;
        case 'screenshot_batch':
          await _handleBatchScreenshot(message);
          break;
        case 'screenshot_schedule':
          await _handleScheduleScreenshot(message);
          break;
        default:
          LoggerService.warning('Unknown screenshot action: $action');
      }
    } catch (error) {
      LoggerService.error('Failed to handle screenshot message', error);

      // 发送错误响应
      final errorMessage = {
        'type': 'capture',
        'action': 'error',
        'data': {
          'deviceId': await _getDeviceId(),
          'screenshotId':
              (message['data'] as Map<String, dynamic>?)?['screenshotId'] ??
                  'unknown',
          'error': error.toString(),
        },
        'timestamp': DateTime.now().toIso8601String(),
      };

      await WebSocketService.instance.sendMessage(errorMessage);
    }
  }

  /// 处理单次截图请求
  Future<void> _handleTakeScreenshot(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final screenshotId =
        payload['screenshotId'] as String? ?? _generateScreenshotId();
    final config = ScreenshotConfig.fromJson(
        payload['config'] as Map<String, dynamic>? ?? {});

    LoggerService.info('Taking screenshot: $screenshotId');

    final result = await takeScreenshot(
      id: screenshotId,
      config: config,
    );

    // 发送结果
    await _sendScreenshotResult(result);
  }

  /// 开始批量截图
  Future<void> startBatchScreenshot({int interval = 5000}) async {
    try {
      LoggerService.info(
          'Starting batch screenshot with interval: ${interval}ms');
      // TODO: 实现批量截图逻辑
    } catch (error) {
      LoggerService.error('Failed to start batch screenshot', error);
      rethrow;
    }
  }

  /// 停止批量截图
  Future<void> stopBatchScreenshot() async {
    try {
      LoggerService.info('Stopping batch screenshot');
      // TODO: 实现停止批量截图逻辑
    } catch (error) {
      LoggerService.error('Failed to stop batch screenshot', error);
      rethrow;
    }
  }

  /// 处理批量截图请求
  Future<void> _handleBatchScreenshot(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final batchId = payload['batchId'] as String? ?? _generateScreenshotId();
    final count = payload['count'] as int? ?? 1;
    final interval =
        Duration(milliseconds: payload['interval'] as int? ?? 1000);
    final config = ScreenshotConfig.fromJson(
        payload['config'] as Map<String, dynamic>? ?? {});

    LoggerService.info('Taking batch screenshots: $batchId, count: $count');

    for (int i = 0; i < count; i++) {
      final screenshotId = '${batchId}_${i + 1}';

      final result = await takeScreenshot(
        id: screenshotId,
        config: config,
      );

      await _sendScreenshotResult(result);

      // 等待间隔时间（除了最后一张）
      if (i < count - 1) {
        await Future.delayed(interval);
      }
    }
  }

  /// 处理定时截图请求
  Future<void> _handleScheduleScreenshot(Map<String, dynamic> message) async {
    final payload = message['data'] as Map<String, dynamic>? ?? {};
    final scheduleId =
        payload['scheduleId'] as String? ?? _generateScreenshotId();
    final interval =
        Duration(milliseconds: payload['interval'] as int? ?? 60000);
    final config = ScreenshotConfig.fromJson(
        payload['config'] as Map<String, dynamic>? ?? {});
    final action = payload['action'] as String? ?? 'start';

    if (action == 'start') {
      LoggerService.info('Starting scheduled screenshots: $scheduleId');

      // 取消现有的定时器
      _scheduledScreenshots[scheduleId]?.cancel();

      // 创建新的定时器
      _scheduledScreenshots[scheduleId] =
          Timer.periodic(interval, (timer) async {
        final screenshotId =
            '${scheduleId}_${DateTime.now().millisecondsSinceEpoch}';

        final result = await takeScreenshot(
          id: screenshotId,
          config: config,
        );

        await _sendScreenshotResult(result);
      });
    } else if (action == 'stop') {
      LoggerService.info('Stopping scheduled screenshots: $scheduleId');

      _scheduledScreenshots[scheduleId]?.cancel();
      _scheduledScreenshots.remove(scheduleId);
    }
  }

  /// 执行截图
  Future<ScreenshotResult> takeScreenshot({
    required String id,
    ScreenshotConfig config = const ScreenshotConfig(),
    Widget? widget,
  }) async {
    try {
      LoggerService.debug('Taking screenshot: $id');
      LoggerService.debug(
          'Config: customPath=${config.customPath}, enableCompression=${config.enableCompression}');

      Uint8List? imageData;

      if (widget != null) {
        // 截取指定Widget
        imageData = await _screenshotController.captureFromWidget(
          widget,
          pixelRatio: config.pixelRatio,
        );
      } else {
        // 截取整个屏幕
        imageData = await _captureScreen(config);
      }

      if (imageData == null) {
        throw Exception('Failed to capture screenshot data');
      }

      // 压缩图片（如果需要）
      Uint8List finalImageData = imageData;
      if (config.enableCompression) {
        try {
          final compressionResult =
              await ImageCompressionService.instance.compressScreenshot(
            imageData,
            useCache: false, // 截图通常是唯一的，不需要缓存
          );
          finalImageData = compressionResult.data;

          LoggerService.debug('Screenshot compressed: '
              '${compressionResult.originalSize} -> ${compressionResult.compressedSize} bytes '
              '(${compressionResult.compressionPercentage.toStringAsFixed(1)}% reduction)');
        } catch (error) {
          LoggerService.warning(
              'Failed to compress screenshot, using original', error);
        }
      } else if (config.quality < 100) {
        finalImageData = await _compressImage(imageData, config.quality);
      }

      // 保存到文件
      // 先确保目录存在
      await _ensureScreenshotDirectory(config.customPath);
      final filePaths = await _saveScreenshot(id, finalImageData, config);

      // 创建元数据
      final metadata = <String, dynamic>{
        'size': finalImageData.length,
        'format': config.format.name,
        'quality': config.quality,
        'pixelRatio': config.pixelRatio,
        'compressed': config.enableCompression,
        'timestamp': DateTime.now().toIso8601String(),
        'filePaths': filePaths,
        'saveCount': filePaths.length,
      };

      if (config.includeMetadata) {
        metadata.addAll(await _getSystemMetadata());
      }

      LoggerService.info(
          'Screenshot taken successfully: $id, size: ${finalImageData.length} bytes, saved to ${filePaths.length} locations');

      final result = ScreenshotResult.success(
        id: id,
        filePaths: filePaths,
        imageData: finalImageData,
        metadata: metadata,
      );

      // 记录截图成功日志
      await ScreenshotLogService.instance.logScreenshot(
        id: id,
        action: 'take_screenshot',
        filePath: filePaths.isNotEmpty ? filePaths.first : null,
        success: true,
        metadata: {
          'fileSize': finalImageData.length,
          'format': config.format.name,
          'compressed': config.enableCompression,
          'quality': config.quality,
          'saveCount': filePaths.length,
          'allPaths': filePaths,
        },
      );

      _resultController.add(result);
      return result;
    } catch (error, stackTrace) {
      LoggerService.error('Failed to take screenshot: $id', error, stackTrace);

      final result = ScreenshotResult.failure(
        id: id,
        error: error.toString(),
      );

      // 记录截图失败日志
      await ScreenshotLogService.instance.logScreenshot(
        id: id,
        action: 'take_screenshot',
        success: false,
        error: error.toString(),
        metadata: {
          'errorType': error.runtimeType.toString(),
        },
      );

      _resultController.add(result);
      return result;
    }
  }

  /// 截取屏幕
  Future<Uint8List?> _captureScreen(ScreenshotConfig config) async {
    try {
      // 使用平台特定的截图方法
      if (Platform.isWindows) {
        return await _captureScreenWindows(config);
      } else if (Platform.isLinux) {
        return await _captureScreenLinux(config);
      } else if (Platform.isMacOS) {
        return await _captureScreenMacOS(config);
      } else {
        throw UnsupportedError('Screenshot not supported on this platform');
      }
    } catch (error) {
      LoggerService.error('Failed to capture screen', error);
      return null;
    }
  }

  /// Windows截图
  Future<Uint8List?> _captureScreenWindows(ScreenshotConfig config) async {
    try {
      // 使用Process调用PowerShell截图命令
      final result = await Process.run(
        'powershell',
        [
          '-Command',
          'Add-Type -AssemblyName System.Windows.Forms; '
              'Add-Type -AssemblyName System.Drawing; '
              '\$bounds = [System.Windows.Forms.Screen]::PrimaryScreen.Bounds; '
              '\$bitmap = New-Object System.Drawing.Bitmap \$bounds.Width, \$bounds.Height; '
              '\$graphics = [System.Drawing.Graphics]::FromImage(\$bitmap); '
              '\$graphics.CopyFromScreen(\$bounds.X, \$bounds.Y, 0, 0, \$bounds.Size); '
              '\$tempPath = [System.IO.Path]::GetTempFileName() + ".png"; '
              '\$bitmap.Save(\$tempPath, [System.Drawing.Imaging.ImageFormat]::Png); '
              'Write-Output \$tempPath; '
              '\$graphics.Dispose(); '
              '\$bitmap.Dispose()'
        ],
      );

      if (result.exitCode == 0) {
        final tempPath = result.stdout.toString().trim();
        final file = File(tempPath);

        if (await file.exists()) {
          final imageData = await file.readAsBytes();
          await file.delete(); // 清理临时文件
          return imageData;
        }
      }

      LoggerService.error('PowerShell screenshot failed: ${result.stderr}');
      return null;
    } catch (error) {
      LoggerService.error('Windows screenshot error', error);
      return null;
    }
  }

  /// Linux截图
  Future<Uint8List?> _captureScreenLinux(ScreenshotConfig config) async {
    try {
      // 首先尝试使用gnome-screenshot
      var result = await Process.run('which', ['gnome-screenshot']);

      if (result.exitCode == 0) {
        final tempPath =
            '/tmp/screenshot_${DateTime.now().millisecondsSinceEpoch}.png';
        result = await Process.run('gnome-screenshot', ['-f', tempPath]);

        if (result.exitCode == 0) {
          final file = File(tempPath);
          if (await file.exists()) {
            final imageData = await file.readAsBytes();
            await file.delete();
            return imageData;
          }
        }
      }

      // 尝试使用scrot
      result = await Process.run('which', ['scrot']);
      if (result.exitCode == 0) {
        final tempPath =
            '/tmp/screenshot_${DateTime.now().millisecondsSinceEpoch}.png';
        result = await Process.run('scrot', [tempPath]);

        if (result.exitCode == 0) {
          final file = File(tempPath);
          if (await file.exists()) {
            final imageData = await file.readAsBytes();
            await file.delete();
            return imageData;
          }
        }
      }

      // 尝试使用import (ImageMagick)
      result = await Process.run('which', ['import']);
      if (result.exitCode == 0) {
        final tempPath =
            '/tmp/screenshot_${DateTime.now().millisecondsSinceEpoch}.png';
        result = await Process.run('import', ['-window', 'root', tempPath]);

        if (result.exitCode == 0) {
          final file = File(tempPath);
          if (await file.exists()) {
            final imageData = await file.readAsBytes();
            await file.delete();
            return imageData;
          }
        }
      }

      LoggerService.error('No suitable screenshot tool found on Linux');
      return null;
    } catch (error) {
      LoggerService.error('Linux screenshot error', error);
      return null;
    }
  }

  /// macOS截图
  Future<Uint8List?> _captureScreenMacOS(ScreenshotConfig config) async {
    try {
      final tempPath =
          '/tmp/screenshot_${DateTime.now().millisecondsSinceEpoch}.png';

      // 使用screencapture命令
      final result = await Process.run('screencapture', ['-x', tempPath]);

      if (result.exitCode == 0) {
        final file = File(tempPath);
        if (await file.exists()) {
          final imageData = await file.readAsBytes();
          await file.delete();
          return imageData;
        }
      }

      LoggerService.error('macOS screencapture failed: ${result.stderr}');
      return null;
    } catch (error) {
      LoggerService.error('macOS screenshot error', error);
      return null;
    }
  }

  /// 压缩图片
  Future<Uint8List> _compressImage(Uint8List imageData, int quality) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/temp_screenshot.png');
      await tempFile.writeAsBytes(imageData);

      final compressedData = await FlutterImageCompress.compressWithFile(
        tempFile.absolute.path,
        quality: quality,
      );

      await tempFile.delete();
      return compressedData ?? imageData;
    } catch (error) {
      LoggerService.error('Failed to compress image', error);
      return imageData;
    }
  }

  /// 保存截图
  Future<List<String>> _saveScreenshot(
      String id, Uint8List imageData, ScreenshotConfig config) async {
    final List<String> savedPaths = [];

    try {
      // 获取截图配置服务
      final screenshotConfigService = ScreenshotConfigService.instance;
      await screenshotConfigService.initialize();

      // 获取目标文件夹列表
      final targetFolders = screenshotConfigService.getTargetFolders();

      LoggerService.debug('Target folders count: ${targetFolders.length}');
      LoggerService.debug(
          'Save strategy: ${screenshotConfigService.currentSettings.folderConfig.saveStrategy}');

      if (targetFolders.isEmpty) {
        // 如果没有配置文件夹，使用默认行为
        LoggerService.info('No target folders configured, using default path');
        final filePath = await _saveScreenshotToPath(
            id, imageData, config, config.customPath);
        savedPaths.add(filePath);
      } else {
        // 保存到指定的文件夹
        LoggerService.info('Saving to ${targetFolders.length} target folders');
        for (final folder in targetFolders) {
          if (folder.isEnabled) {
            try {
              final filePath = await _saveScreenshotToPath(
                  id, imageData, config, folder.path);
              savedPaths.add(filePath);

              LoggerService.info('截图已保存到: ${folder.name} ($filePath)');

              // 更新文件夹的最后使用时间
              await screenshotConfigService.updateScreenshotFolder(
                folderId: folder.id,
                isEnabled: folder.isEnabled,
              );
            } catch (error) {
              LoggerService.error('保存截图到文件夹失败: ${folder.name}', error);
              // 继续保存到其他文件夹
            }
          } else {
            LoggerService.debug('Skipping disabled folder: ${folder.name}');
          }
        }
      }

      if (savedPaths.isEmpty) {
        // 如果没有保存成功任何文件，使用默认路径
        LoggerService.warning(
            'No files saved successfully, falling back to default path');
        final filePath = await _saveScreenshotToPath(
            id, imageData, config, config.customPath);
        savedPaths.add(filePath);
      }
    } catch (error) {
      LoggerService.error('多文件夹保存截图失败，使用默认路径', error);
      // 如果出错，使用默认路径保存
      final filePath =
          await _saveScreenshotToPath(id, imageData, config, config.customPath);
      savedPaths.add(filePath);
    }

    LoggerService.info(
        'Screenshot saved to ${savedPaths.length} locations: $savedPaths');
    return savedPaths;
  }

  /// 保存截图到指定路径
  Future<String> _saveScreenshotToPath(String id, Uint8List imageData,
      ScreenshotConfig config, String? customPath) async {
    final directory = await _getScreenshotDirectory(customPath);
    final extension = config.format == ImageByteFormat.png ? 'png' : 'jpg';
    final fileName =
        '${id}_${DateTime.now().millisecondsSinceEpoch}.$extension';
    final filePath = '${directory.path}/$fileName';

    final file = File(filePath);
    await file.writeAsBytes(imageData);

    LoggerService.info('Screenshot saved to: $filePath');
    return filePath;
  }

  /// 发送截图结果
  Future<void> _sendScreenshotResult(ScreenshotResult result) async {
    final message = {
      'type': 'capture',
      'action': result.success ? 'complete' : 'error',
      'data': {
        'deviceId': await _getDeviceId(),
        'screenshotId': result.id,
        'success': result.success,
        if (result.filePath != null) 'imagePath': result.filePath,
        if (result.error != null) 'error': result.error,
        'metadata': result.metadata,
      },
      'timestamp': DateTime.now().toIso8601String(),
    };

    await WebSocketService.instance.sendMessage(message);
  }

  /// 确保截图目录存在
  Future<void> _ensureScreenshotDirectory([String? customPath]) async {
    final directory = await _getScreenshotDirectory(customPath);
    if (!await directory.exists()) {
      await directory.create(recursive: true);
      LoggerService.info('Created screenshot directory: ${directory.path}');
    }
  }

  /// 获取截图目录
  Future<Directory> _getScreenshotDirectory([String? customPath]) async {
    if (customPath != null && customPath.isNotEmpty) {
      LoggerService.debug('Using custom screenshot path: $customPath');
      return Directory(customPath);
    }

    final appDir = await getApplicationDocumentsDirectory();
    final defaultPath = '${appDir.path}/screenshots';
    LoggerService.debug('Using default screenshot path: $defaultPath');
    return Directory(defaultPath);
  }

  /// 获取系统元数据
  Future<Map<String, dynamic>> _getSystemMetadata() async {
    return {
      'platform': Platform.operatingSystem,
      'hostname': Platform.localHostname,
      'version': Platform.operatingSystemVersion,
    };
  }

  /// 获取设备ID
  Future<String> _getDeviceId() async {
    return await StorageService().getString('device_id') ?? 'unknown';
  }

  /// 生成截图ID
  String _generateScreenshotId() {
    return 'screenshot_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// 停止所有定时截图
  void stopAllScheduledScreenshots() {
    for (final timer in _scheduledScreenshots.values) {
      timer.cancel();
    }
    _scheduledScreenshots.clear();
    LoggerService.info('All scheduled screenshots stopped');
  }

  /// 获取活动的定时截图列表
  List<String> getActiveSchedules() {
    return _scheduledScreenshots.keys.toList();
  }

  /// 释放资源
  void dispose() {
    stopAllScheduledScreenshots();
    _resultController.close();
  }
}

/// 截图服务提供者
final screenshotServiceProvider = Provider<ScreenshotService>((ref) {
  return ScreenshotService.instance;
});

/// 截图结果流提供者
final screenshotResultStreamProvider = StreamProvider<ScreenshotResult>((ref) {
  final service = ref.watch(screenshotServiceProvider);
  return service.resultStream;
});
