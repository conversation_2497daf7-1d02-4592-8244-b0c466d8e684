import 'package:flutter/foundation.dart';

/// 截图文件夹配置项
class ScreenshotFolderItem {
  final String id;
  final String name;
  final String path;
  final bool isEnabled;
  final bool isDefault;
  final DateTime createdAt;
  final DateTime? lastUsedAt;

  const ScreenshotFolderItem({
    required this.id,
    required this.name,
    required this.path,
    this.isEnabled = true,
    this.isDefault = false,
    required this.createdAt,
    this.lastUsedAt,
  });

  /// 从JSON创建配置项
  factory ScreenshotFolderItem.fromJson(Map<String, dynamic> json) {
    return ScreenshotFolderItem(
      id: json['id'] as String,
      name: json['name'] as String,
      path: json['path'] as String,
      isEnabled: json['isEnabled'] as bool? ?? true,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt'] as int),
      lastUsedAt: json['lastUsedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['lastUsedAt'] as int)
          : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'path': path,
      'isEnabled': isEnabled,
      'isDefault': isDefault,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'lastUsedAt': lastUsedAt?.millisecondsSinceEpoch,
    };
  }

  /// 创建配置项副本
  ScreenshotFolderItem copyWith({
    String? id,
    String? name,
    String? path,
    bool? isEnabled,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? lastUsedAt,
  }) {
    return ScreenshotFolderItem(
      id: id ?? this.id,
      name: name ?? this.name,
      path: path ?? this.path,
      isEnabled: isEnabled ?? this.isEnabled,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScreenshotFolderItem &&
        other.id == id &&
        other.name == name &&
        other.path == path &&
        other.isEnabled == isEnabled &&
        other.isDefault == isDefault &&
        other.createdAt == createdAt &&
        other.lastUsedAt == lastUsedAt;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      name,
      path,
      isEnabled,
      isDefault,
      createdAt,
      lastUsedAt,
    );
  }

  @override
  String toString() {
    return 'ScreenshotFolderItem('
        'id: $id, '
        'name: $name, '
        'path: $path, '
        'isEnabled: $isEnabled, '
        'isDefault: $isDefault, '
        'createdAt: $createdAt, '
        'lastUsedAt: $lastUsedAt'
        ')';
  }
}

/// 截图文件夹保存策略
enum ScreenshotSaveStrategy {
  /// 保存到默认文件夹
  defaultFolder,

  /// 保存到指定的单个文件夹
  singleFolder,

  /// 保存到所有启用的文件夹
  allFolders,

  /// 轮询保存到不同文件夹
  roundRobin,

  /// 根据时间自动切换文件夹
  timeRotation,
}

/// 截图文件夹配置
class ScreenshotFolderConfig {
  final List<ScreenshotFolderItem> folders;
  final ScreenshotSaveStrategy saveStrategy;
  final String? selectedFolderId;
  final bool enableMultiFolderSave;
  final int rotationIntervalMinutes;

  const ScreenshotFolderConfig({
    this.folders = const [],
    this.saveStrategy = ScreenshotSaveStrategy.defaultFolder,
    this.selectedFolderId,
    this.enableMultiFolderSave = false,
    this.rotationIntervalMinutes = 60,
  });

  /// 从JSON创建配置
  factory ScreenshotFolderConfig.fromJson(Map<String, dynamic> json) {
    final foldersList = json['folders'] as List<dynamic>? ?? [];
    final folders = foldersList
        .map((item) =>
            ScreenshotFolderItem.fromJson(item as Map<String, dynamic>))
        .toList();

    return ScreenshotFolderConfig(
      folders: folders,
      saveStrategy: ScreenshotSaveStrategy.values.firstWhere(
        (strategy) => strategy.name == (json['saveStrategy'] as String?),
        orElse: () => ScreenshotSaveStrategy.defaultFolder,
      ),
      selectedFolderId: json['selectedFolderId'] as String?,
      enableMultiFolderSave: json['enableMultiFolderSave'] as bool? ?? false,
      rotationIntervalMinutes: json['rotationIntervalMinutes'] as int? ?? 60,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'folders': folders.map((folder) => folder.toJson()).toList(),
      'saveStrategy': saveStrategy.name,
      'selectedFolderId': selectedFolderId,
      'enableMultiFolderSave': enableMultiFolderSave,
      'rotationIntervalMinutes': rotationIntervalMinutes,
    };
  }

  /// 创建配置副本
  ScreenshotFolderConfig copyWith({
    List<ScreenshotFolderItem>? folders,
    ScreenshotSaveStrategy? saveStrategy,
    String? selectedFolderId,
    bool? enableMultiFolderSave,
    int? rotationIntervalMinutes,
  }) {
    return ScreenshotFolderConfig(
      folders: folders ?? this.folders,
      saveStrategy: saveStrategy ?? this.saveStrategy,
      selectedFolderId: selectedFolderId ?? this.selectedFolderId,
      enableMultiFolderSave:
          enableMultiFolderSave ?? this.enableMultiFolderSave,
      rotationIntervalMinutes:
          rotationIntervalMinutes ?? this.rotationIntervalMinutes,
    );
  }

  /// 获取启用的文件夹列表
  List<ScreenshotFolderItem> get enabledFolders {
    return folders.where((folder) => folder.isEnabled).toList();
  }

  /// 获取默认文件夹
  ScreenshotFolderItem? get defaultFolder {
    try {
      return folders.firstWhere((folder) => folder.isDefault);
    } catch (e) {
      // 如果没有设置默认文件夹，返回第一个启用的文件夹
      final enabled = enabledFolders;
      return enabled.isNotEmpty ? enabled.first : null;
    }
  }

  /// 获取选中的文件夹
  ScreenshotFolderItem? get selectedFolder {
    if (selectedFolderId == null) return null;
    try {
      return folders.firstWhere((folder) => folder.id == selectedFolderId);
    } catch (e) {
      return null;
    }
  }

  /// 根据策略获取目标文件夹列表
  List<ScreenshotFolderItem> getTargetFolders({DateTime? timestamp}) {
    switch (saveStrategy) {
      case ScreenshotSaveStrategy.defaultFolder:
        final defaultFld = defaultFolder;
        return defaultFld != null ? [defaultFld] : [];

      case ScreenshotSaveStrategy.singleFolder:
        final selectedFld = selectedFolder;
        return selectedFld != null ? [selectedFld] : [];

      case ScreenshotSaveStrategy.allFolders:
        return enabledFolders;

      case ScreenshotSaveStrategy.roundRobin:
        if (enabledFolders.isEmpty) return [];
        // 简单的轮询实现，可以根据时间戳或计数器来选择
        final index = (timestamp?.millisecondsSinceEpoch ??
                DateTime.now().millisecondsSinceEpoch) %
            enabledFolders.length;
        return [enabledFolders[index]];

      case ScreenshotSaveStrategy.timeRotation:
        if (enabledFolders.isEmpty) return [];
        // 根据时间间隔轮换文件夹
        final now = timestamp ?? DateTime.now();
        final intervalMillis = rotationIntervalMinutes * 60 * 1000;
        final index = (now.millisecondsSinceEpoch ~/ intervalMillis) %
            enabledFolders.length;
        return [enabledFolders[index]];
    }
  }

  /// 添加文件夹
  ScreenshotFolderConfig addFolder(ScreenshotFolderItem folder) {
    final updatedFolders = List<ScreenshotFolderItem>.from(folders);
    updatedFolders.add(folder);
    return copyWith(folders: updatedFolders);
  }

  /// 更新文件夹
  ScreenshotFolderConfig updateFolder(
      String folderId, ScreenshotFolderItem folder) {
    final updatedFolders =
        folders.map((f) => f.id == folderId ? folder : f).toList();
    return copyWith(folders: updatedFolders);
  }

  /// 删除文件夹
  ScreenshotFolderConfig removeFolder(String folderId) {
    final updatedFolders = folders.where((f) => f.id != folderId).toList();
    return copyWith(folders: updatedFolders);
  }

  /// 设置默认文件夹
  ScreenshotFolderConfig setDefaultFolder(String folderId) {
    final updatedFolders = folders.map((f) {
      if (f.id == folderId) {
        return f.copyWith(isDefault: true);
      } else if (f.isDefault) {
        return f.copyWith(isDefault: false);
      }
      return f;
    }).toList();
    return copyWith(folders: updatedFolders);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ScreenshotFolderConfig &&
        listEquals(other.folders, folders) &&
        other.saveStrategy == saveStrategy &&
        other.selectedFolderId == selectedFolderId &&
        other.enableMultiFolderSave == enableMultiFolderSave &&
        other.rotationIntervalMinutes == rotationIntervalMinutes;
  }

  @override
  int get hashCode {
    return Object.hash(
      Object.hashAll(folders),
      saveStrategy,
      selectedFolderId,
      enableMultiFolderSave,
      rotationIntervalMinutes,
    );
  }

  @override
  String toString() {
    return 'ScreenshotFolderConfig('
        'folders: $folders, '
        'saveStrategy: $saveStrategy, '
        'selectedFolderId: $selectedFolderId, '
        'enableMultiFolderSave: $enableMultiFolderSave, '
        'rotationIntervalMinutes: $rotationIntervalMinutes'
        ')';
  }
}
