import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:window_manager/window_manager.dart';

import '../../../../core/services/display_service.dart';
import '../../../../core/services/logger_service.dart';
import '../../../../core/providers/service_providers.dart' as service_providers;

/// 显示控制组件
class DisplayControlsWidget extends ConsumerStatefulWidget {
  const DisplayControlsWidget({super.key});
  
  @override
  ConsumerState<DisplayControlsWidget> createState() => _DisplayControlsWidgetState();
}

class _DisplayControlsWidgetState extends ConsumerState<DisplayControlsWidget> {
  bool _isFullScreen = false;
  bool _isVisible = true;
  
  @override
  void initState() {
    super.initState();
    _checkFullScreenStatus();
  }
  
  Future<void> _checkFullScreenStatus() async {
    try {
      final isFullScreen = await windowManager.isFullScreen();
      setState(() {
        _isFullScreen = isFullScreen;
      });
    } catch (error) {
      LoggerService.error('Failed to check fullscreen status', error);
      // Default to false if we can't determine the status
      setState(() {
        _isFullScreen = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    if (!_isVisible || _isFullScreen) {
      return const SizedBox.shrink();
    }
    
    return Positioned(
      top: 16,
      right: 16,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        color: Colors.black.withValues(alpha: 0.8),
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildControlButton(
                icon: Icons.fullscreen,
                tooltip: '全屏',
                onPressed: _toggleFullScreen,
              ),
              const SizedBox(width: 8),
              _buildControlButton(
                icon: Icons.screenshot,
                tooltip: '截图',
                onPressed: _takeScreenshot,
              ),
              const SizedBox(width: 8),
              _buildControlButton(
                icon: Icons.settings,
                tooltip: '设置',
                onPressed: _openSettings,
              ),
              const SizedBox(width: 8),
              _buildControlButton(
                icon: Icons.visibility_off,
                tooltip: '隐藏控制栏',
                onPressed: _hideControls,
              ),
            ],
          ),
        ),
      ),
    );
  }
  
  /// 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(4),
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Icon(
            icon,
            color: Colors.white,
            size: 20,
          ),
        ),
      ),
    );
  }
  
  /// 切换全屏
  Future<void> _toggleFullScreen() async {
    try {
      if (_isFullScreen) {
        await windowManager.setFullScreen(false);
      } else {
        await windowManager.setFullScreen(true);
      }

      await _checkFullScreenStatus();
    } catch (error) {
      LoggerService.error('Failed to toggle fullscreen', error);
      // Fallback: try to set fullscreen without checking current state
      try {
        await windowManager.setFullScreen(!_isFullScreen);
        await _checkFullScreenStatus();
      } catch (fallbackError) {
        LoggerService.error('Fallback fullscreen toggle failed', fallbackError);
      }
    }
  }
  
  /// 截图
  Future<void> _takeScreenshot() async {
    try {
      final screenshotService = ref.read(service_providers.screenshotServiceProvider);
      final result = await screenshotService.takeScreenshot(
        id: 'display_screenshot_${DateTime.now().millisecondsSinceEpoch}',
      );
      
      if (result.success) {
        _showMessage('截图成功');
      } else {
        _showMessage('截图失败: ${result.error}');
      }
    } catch (error) {
      LoggerService.error('Screenshot failed', error);
      _showMessage('截图失败');
    }
  }
  
  /// 打开设置
  void _openSettings() {
    Navigator.of(context).pushNamed('/settings');
  }
  
  /// 隐藏控制栏
  void _hideControls() {
    setState(() {
      _isVisible = false;
    });
    
    // 5秒后自动显示
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _isVisible = true;
        });
      }
    });
  }
  
  /// 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}

/// 显示状态指示器
class DisplayStatusIndicator extends ConsumerWidget {
  const DisplayStatusIndicator({super.key});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final displayService = ref.watch(displayServiceProvider);
    final websocketService = ref.watch(service_providers.webSocketServiceProvider);
    
    return Positioned(
      bottom: 16,
      left: 16,
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(20),
        color: Colors.black.withValues(alpha: 0.7),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 连接状态
              Icon(
                websocketService.isConnected
                    ? Icons.wifi
                    : Icons.wifi_off,
                color: websocketService.isConnected
                    ? Colors.green
                    : Colors.red,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                websocketService.isConnected ? '已连接' : '未连接',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
              
              const SizedBox(width: 16),
              
              // 显示状态
              Icon(
                displayService.isDisplaying
                    ? Icons.visibility
                    : Icons.visibility_off,
                color: displayService.isDisplaying
                    ? Colors.blue
                    : Colors.grey,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                displayService.isDisplaying ? '显示中' : '待机',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 图片信息覆盖层
class ImageInfoOverlay extends StatelessWidget {
  final String? imageName;
  final String? imageSize;
  final int? currentIndex;
  final int? totalCount;
  
  const ImageInfoOverlay({
    super.key,
    this.imageName,
    this.imageSize,
    this.currentIndex,
    this.totalCount,
  });
  
  @override
  Widget build(BuildContext context) {
    if (imageName == null) {
      return const SizedBox.shrink();
    }
    
    return Positioned(
      bottom: 16,
      right: 16,
      child: Material(
        elevation: 4,
        borderRadius: BorderRadius.circular(8),
        color: Colors.black.withValues(alpha: 0.7),
        child: Container(
          padding: const EdgeInsets.all(12),
          constraints: const BoxConstraints(
            maxWidth: 300,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 图片名称
              Text(
                imageName!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              if (imageSize != null) ...[
                const SizedBox(height: 4),
                Text(
                  '大小: $imageSize',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
              
              if (currentIndex != null && totalCount != null) ...[
                const SizedBox(height: 4),
                Text(
                  '${currentIndex! + 1} / $totalCount',
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// 手势控制组件
class GestureControlWidget extends StatelessWidget {
  final VoidCallback? onDoubleTap;
  final VoidCallback? onRightClick;
  final Widget child;
  
  const GestureControlWidget({
    super.key,
    this.onDoubleTap,
    this.onRightClick,
    required this.child,
  });
  
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onDoubleTap: onDoubleTap,
      onSecondaryTap: onRightClick,
      child: child,
    );
  }
}

/// 右键菜单
class ContextMenuWidget extends StatelessWidget {
  final VoidCallback? onFullScreen;
  final VoidCallback? onScreenshot;
  final VoidCallback? onSettings;
  final VoidCallback? onExit;
  
  const ContextMenuWidget({
    super.key,
    this.onFullScreen,
    this.onScreenshot,
    this.onSettings,
    this.onExit,
  });
  
  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 8,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        width: 150,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMenuItem(
              icon: Icons.fullscreen,
              text: '全屏',
              onTap: onFullScreen,
            ),
            _buildMenuItem(
              icon: Icons.screenshot,
              text: '截图',
              onTap: onScreenshot,
            ),
            _buildMenuItem(
              icon: Icons.settings,
              text: '设置',
              onTap: onSettings,
            ),
            const Divider(height: 1),
            _buildMenuItem(
              icon: Icons.exit_to_app,
              text: '退出',
              onTap: onExit,
              textColor: Colors.red,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMenuItem({
    required IconData icon,
    required String text,
    VoidCallback? onTap,
    Color? textColor,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: textColor ?? Colors.grey[700],
            ),
            const SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: textColor ?? Colors.grey[700],
              ),
            ),
          ],
        ),
      ),
    );
  }
}