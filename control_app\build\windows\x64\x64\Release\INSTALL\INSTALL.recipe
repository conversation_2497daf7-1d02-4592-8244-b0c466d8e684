﻿<?xml version="1.0" encoding="utf-8"?>
<Project>
  <ProjectOutputs>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\x64\Release\ZERO_CHECK</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\flutter\x64\Release\flutter_assemble</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\connectivity_plus\Release\connectivity_plus_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\file_selector_windows\Release\file_selector_windows_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\screen_retriever\Release\screen_retriever_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\system_tray\Release\system_tray_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\window_manager\Release\window_manager_plugin.dll</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\Release\flutter_imgct_control.exe</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\x64\Release\ALL_BUILD</FullPath>
    </ProjectOutput>
    <ProjectOutput>
      <FullPath>E:\AI_codeE\imgCT\control_app\build\windows\x64\x64\Release\INSTALL</FullPath>
    </ProjectOutput>
  </ProjectOutputs>
  <ContentFiles />
  <SatelliteDlls />
  <NonRecipeFileRefs />
</Project>