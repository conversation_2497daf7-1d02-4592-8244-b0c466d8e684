{"gateway": {"name": "Flutter Image Control Gateway", "version": "1.1.0", "port": 7777, "description": "统一网关服务 - 实时日志增强版"}, "logging": {"level": "DEBUG", "console": {"enabled": true, "colorful": true, "timestamp": true, "uptime": true}, "file": {"enabled": true, "directory": "./logs", "rotation": "daily", "maxFiles": 30, "maxSize": "10MB"}, "categories": {"SYSTEM": {"enabled": true, "level": "INFO"}, "HTTP": {"enabled": true, "level": "INFO"}, "WebSocket": {"enabled": true, "level": "DEBUG"}, "Terminal": {"enabled": true, "level": "DEBUG"}, "Controller": {"enabled": true, "level": "DEBUG"}, "STATISTICS": {"enabled": true, "level": "INFO"}}}, "monitoring": {"heartbeat": {"interval": 30000, "timeout": 90000}, "statistics": {"enabled": true, "interval": 60000}, "health_check": {"enabled": true, "detailed": true}}, "security": {"cors": {"enabled": true, "origin": "*"}, "rate_limiting": {"enabled": false, "window": 900000, "max_requests": 100}}, "websocket": {"ping_interval": 25000, "ping_timeout": 5000, "compression": false, "max_payload": 1048576}, "development": {"debug_mode": true, "verbose_errors": true, "request_logging": true}}