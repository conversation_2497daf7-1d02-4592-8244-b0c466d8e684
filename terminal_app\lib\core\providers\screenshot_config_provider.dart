import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/screenshot_config_service.dart';

/// 截图配置服务提供者
final screenshotConfigServiceProvider = Provider<ScreenshotConfigService>((ref) {
  return ScreenshotConfigService.instance;
});

/// 截图设置状态提供者
final screenshotSettingsProvider = StateNotifierProvider<ScreenshotSettingsNotifier, ScreenshotSettings>((ref) {
  final configService = ref.watch(screenshotConfigServiceProvider);
  return ScreenshotSettingsNotifier(configService);
});

/// 截图设置状态管理器
class ScreenshotSettingsNotifier extends StateNotifier<ScreenshotSettings> {
  final ScreenshotConfigService _configService;
  
  ScreenshotSettingsNotifier(this._configService) : super(const ScreenshotSettings()) {
    _loadSettings();
  }

  /// 加载设置
  Future<void> _loadSettings() async {
    await _configService.initialize();
    state = _configService.currentSettings;
  }

  /// 更新自定义保存路径
  Future<bool> updateCustomSavePath(String? path) async {
    final success = await _configService.updateCustomSavePath(path);
    if (success) {
      state = _configService.currentSettings;
    }
    return success;
  }

  /// 更新压缩设置
  Future<bool> updateCompressionSettings({
    bool? enableCompression,
    int? compressionQuality,
  }) async {
    final success = await _configService.updateCompressionSettings(
      enableCompression: enableCompression,
      compressionQuality: compressionQuality,
    );
    if (success) {
      state = _configService.currentSettings;
    }
    return success;
  }

  /// 更新图片格式
  Future<bool> updateImageFormat(String format) async {
    final success = await _configService.updateImageFormat(format);
    if (success) {
      state = _configService.currentSettings;
    }
    return success;
  }

  /// 更新自动操作设置
  Future<bool> updateAutoSettings({
    bool? autoOpenFolder,
    bool? showPreview,
  }) async {
    final success = await _configService.updateAutoSettings(
      autoOpenFolder: autoOpenFolder,
      showPreview: showPreview,
    );
    if (success) {
      state = _configService.currentSettings;
    }
    return success;
  }

  /// 更新元数据设置
  Future<bool> updateMetadataSettings(bool saveMetadata) async {
    final success = await _configService.updateMetadataSettings(saveMetadata);
    if (success) {
      state = _configService.currentSettings;
    }
    return success;
  }

  /// 批量更新设置
  Future<bool> updateSettings(ScreenshotSettings settings) async {
    final success = await _configService.updateSettings(settings);
    if (success) {
      state = _configService.currentSettings;
    }
    return success;
  }

  /// 重置为默认设置
  Future<bool> resetToDefaults() async {
    final success = await _configService.resetToDefaults();
    if (success) {
      state = _configService.currentSettings;
    }
    return success;
  }

  /// 刷新设置（从存储重新加载）
  Future<void> refresh() async {
    await _loadSettings();
  }
}