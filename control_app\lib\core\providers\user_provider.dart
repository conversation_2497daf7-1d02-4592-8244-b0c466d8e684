import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user.dart';

/// 当前用户状态提供者
final currentUserProvider = StateProvider<User?>((ref) {
  // 默认返回一个示例用户，实际应用中应该从认证服务获取
  return const User(
    id: '1',
    name: '管理员',
    email: '<EMAIL>',
    role: 'admin',
    isActive: true,
  );
});

/// 用户列表提供者
final usersProvider = StateNotifierProvider<UsersNotifier, List<User>>((ref) {
  return UsersNotifier();
});

/// 用户列表状态管理器
class UsersNotifier extends StateNotifier<List<User>> {
  UsersNotifier() : super([]);

  /// 加载用户列表
  Future<void> loadUsers() async {
    // 模拟加载用户数据
    await Future.delayed(const Duration(milliseconds: 500));
    
    state = [
      const User(
        id: '1',
        name: '管理员',
        email: '<EMAIL>',
        role: 'admin',
        isActive: true,
      ),
      const User(
        id: '2',
        name: '操作员',
        email: '<EMAIL>',
        role: 'operator',
        isActive: true,
      ),
      const User(
        id: '3',
        name: '查看者',
        email: '<EMAIL>',
        role: 'viewer',
        isActive: true,
      ),
    ];
  }

  /// 添加用户
  void addUser(User user) {
    state = [...state, user];
  }

  /// 更新用户
  void updateUser(User updatedUser) {
    state = [
      for (final user in state)
        if (user.id == updatedUser.id) updatedUser else user,
    ];
  }

  /// 删除用户
  void removeUser(String userId) {
    state = state.where((user) => user.id != userId).toList();
  }

  /// 根据ID获取用户
  User? getUserById(String userId) {
    try {
      return state.firstWhere((user) => user.id == userId);
    } catch (e) {
      return null;
    }
  }

  /// 根据邮箱获取用户
  User? getUserByEmail(String email) {
    try {
      return state.firstWhere((user) => user.email == email);
    } catch (e) {
      return null;
    }
  }

  /// 获取活跃用户列表
  List<User> get activeUsers {
    return state.where((user) => user.isActive).toList();
  }

  /// 获取管理员用户列表
  List<User> get adminUsers {
    return state.where((user) => user.isAdmin).toList();
  }

  /// 获取操作员用户列表
  List<User> get operatorUsers {
    return state.where((user) => user.isOperator).toList();
  }

  /// 获取查看者用户列表
  List<User> get viewerUsers {
    return state.where((user) => user.isViewer).toList();
  }
}