import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/logger_service.dart';

/// 应用主题模式
enum ThemeMode {
  light,
  dark,
  system,
}

/// 应用语言
enum AppLanguage {
  chinese,
  english,
}

/// 连接状态
enum ConnectionStatus {
  disconnected,
  connecting,
  connected,
  error,
}

/// 截图状态
enum ScreenshotStatus {
  idle,
  capturing,
  processing,
  uploading,
  completed,
  error,
}

/// 应用状态
enum AppStatus {
  initializing,
  ready,
  connecting,
  connected,
  disconnected,
  error,
  maintenance,
}

/// 应用状态数据类
class AppState {
  final AppStatus status;
  final String? message;
  final String? errorMessage;
  final DateTime lastUpdated;
  final Map<String, dynamic> metadata;

  const AppState({
    required this.status,
    this.message,
    this.errorMessage,
    required this.lastUpdated,
    this.metadata = const {},
  });

  /// 创建初始化状态
  factory AppState.initializing({String? message}) {
    return AppState(
      status: AppStatus.initializing,
      message: message ?? '正在初始化应用...',
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建就绪状态
  factory AppState.ready({String? message}) {
    return AppState(
      status: AppStatus.ready,
      message: message ?? '应用已就绪',
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建连接中状态
  factory AppState.connecting({String? message}) {
    return AppState(
      status: AppStatus.connecting,
      message: message ?? '正在连接...',
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建已连接状态
  factory AppState.connected({String? message}) {
    return AppState(
      status: AppStatus.connected,
      message: message ?? '已连接',
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建断开连接状态
  factory AppState.disconnected({String? message}) {
    return AppState(
      status: AppStatus.disconnected,
      message: message ?? '连接已断开',
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建错误状态
  factory AppState.error({String? message, String? errorMessage}) {
    return AppState(
      status: AppStatus.error,
      message: message ?? '发生错误',
      errorMessage: errorMessage,
      lastUpdated: DateTime.now(),
    );
  }

  /// 创建维护状态
  factory AppState.maintenance({String? message}) {
    return AppState(
      status: AppStatus.maintenance,
      message: message ?? '系统维护中',
      lastUpdated: DateTime.now(),
    );
  }

  /// 复制并更新状态
  AppState copyWith({
    AppStatus? status,
    String? message,
    String? errorMessage,
    DateTime? lastUpdated,
    Map<String, dynamic>? metadata,
  }) {
    return AppState(
      status: status ?? this.status,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AppState &&
        other.status == status &&
        other.message == message;
  }

  @override
  int get hashCode => status.hashCode ^ message.hashCode;

  /// 便捷getter方法
  bool get isInitializing => status == AppStatus.initializing;
  bool get isReady => status == AppStatus.ready;
  bool get isConnecting => status == AppStatus.connecting;
  bool get isConnected => status == AppStatus.connected;
  bool get isDisconnected => status == AppStatus.disconnected;
  bool get isError => status == AppStatus.error;
  bool get isMaintenance => status == AppStatus.maintenance;
  bool get isRegistered =>
      status == AppStatus.connected || status == AppStatus.ready;
  bool get hasError => status == AppStatus.error && errorMessage != null;

  @override
  String toString() {
    return 'AppState(status: $status, message: $message, lastUpdated: $lastUpdated)';
  }
}

/// 应用状态管理Provider
class AppStateProvider extends ChangeNotifier {
  // 主题相关
  ThemeMode _themeMode = ThemeMode.system;
  bool _isDarkMode = false;

  // 语言相关
  AppLanguage _language = AppLanguage.chinese;
  Locale _locale = const Locale('zh', 'CN');

  // 连接状态
  ConnectionStatus _connectionStatus = ConnectionStatus.disconnected;
  String? _gatewayUrl;
  String? _connectionError;

  // 初始化状态
  bool _isInitializing = false;

  // 截图状态
  ScreenshotStatus _screenshotStatus = ScreenshotStatus.idle;
  String? _screenshotError;
  int _screenshotCount = 0;
  DateTime? _lastScreenshotTime;

  // 应用设置
  bool _autoConnect = true;
  bool _autoScreenshot = false;
  bool _showNotifications = true;
  bool _enableSounds = true;
  double _imageQuality = 0.8;
  int _screenshotInterval = 5; // 秒
  String _screenshotFormat = 'PNG';

  // 窗口状态
  bool _isMaximized = false;
  bool _isMinimized = false;
  Size _windowSize = const Size(800, 600);
  Offset _windowPosition = const Offset(100, 100);

  // Getters
  ThemeMode get themeMode => _themeMode;
  bool get isDarkMode => _isDarkMode;
  AppLanguage get language => _language;
  Locale get locale => _locale;
  ConnectionStatus get connectionStatus => _connectionStatus;
  String? get gatewayUrl => _gatewayUrl;
  String? get connectionError => _connectionError;
  ScreenshotStatus get screenshotStatus => _screenshotStatus;
  String? get screenshotError => _screenshotError;
  int get screenshotCount => _screenshotCount;
  DateTime? get lastScreenshotTime => _lastScreenshotTime;
  bool get autoConnect => _autoConnect;
  bool get autoScreenshot => _autoScreenshot;
  bool get showNotifications => _showNotifications;
  bool get enableSounds => _enableSounds;
  double get imageQuality => _imageQuality;
  int get screenshotInterval => _screenshotInterval;
  String get screenshotFormat => _screenshotFormat;
  bool get isMaximized => _isMaximized;
  bool get isMinimized => _isMinimized;
  Size get windowSize => _windowSize;
  Offset get windowPosition => _windowPosition;

  // 便捷getters
  bool get isConnected => _connectionStatus == ConnectionStatus.connected;
  bool get isConnecting => _connectionStatus == ConnectionStatus.connecting;
  bool get hasConnectionError => _connectionStatus == ConnectionStatus.error;
  bool get isScreenshotting => _screenshotStatus == ScreenshotStatus.capturing;
  bool get hasScreenshotError => _screenshotStatus == ScreenshotStatus.error;
  bool get isInitializing => _isInitializing;

  /// 初始化应用状态
  Future<void> initialize() async {
    try {
      LoggerService.info('Initializing app state provider');

      // 设置默认网关URL（用于开发测试）
      _gatewayUrl = 'http://localhost:3001';

      // 从本地存储加载设置
      await _loadSettings();

      // 检测系统主题
      _detectSystemTheme();

      LoggerService.info('App state provider initialized successfully');
    } catch (e) {
      LoggerService.error('Failed to initialize app state provider', e);
    }
  }

  /// 设置主题模式
  void setThemeMode(ThemeMode mode) {
    if (_themeMode != mode) {
      _themeMode = mode;
      _updateDarkMode();
      LoggerService.info('Theme mode changed to: ${mode.name}');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 切换主题
  void toggleTheme() {
    switch (_themeMode) {
      case ThemeMode.light:
        setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        setThemeMode(ThemeMode.light);
        break;
      case ThemeMode.system:
        setThemeMode(_isDarkMode ? ThemeMode.light : ThemeMode.dark);
        break;
    }
  }

  /// 设置语言
  void setLanguage(AppLanguage language) {
    if (_language != language) {
      _language = language;
      _updateLocale();
      LoggerService.info('Language changed to: ${language.name}');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置初始化状态
  void setInitializing(bool initializing) {
    if (_isInitializing != initializing) {
      _isInitializing = initializing;
      LoggerService.info('Initializing status changed to: $initializing');
      notifyListeners();
    }
  }

  /// 设置错误状态
  void setError(String error) {
    setConnectionStatus(ConnectionStatus.error, error: error);
  }

  /// 设置连接状态
  void setConnectionStatus(ConnectionStatus status, {String? error}) {
    if (_connectionStatus != status) {
      _connectionStatus = status;
      _connectionError = error;
      LoggerService.info('Connection status changed to: ${status.name}');
      if (error != null) {
        LoggerService.error('Connection error: $error');
      }
      notifyListeners();
    }
  }

  /// 设置截图状态
  void setScreenshotStatus(ScreenshotStatus status, {String? error}) {
    if (_screenshotStatus != status) {
      _screenshotStatus = status;
      _screenshotError = error;

      if (status == ScreenshotStatus.completed) {
        _screenshotCount++;
        _lastScreenshotTime = DateTime.now();
        LoggerService.info(
            'Screenshot completed. Total count: $_screenshotCount');
      } else if (status == ScreenshotStatus.error && error != null) {
        LoggerService.error('Screenshot error: $error');
      }

      notifyListeners();
    }
  }

  /// 设置网关URL
  void setGatewayUrl(String? url) {
    if (_gatewayUrl != url) {
      _gatewayUrl = url;
      LoggerService.info('Gateway URL set to: $url');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置自动连接
  void setAutoConnect(bool enabled) {
    if (_autoConnect != enabled) {
      _autoConnect = enabled;
      LoggerService.info('Auto connect set to: $enabled');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置自动截图
  void setAutoScreenshot(bool enabled) {
    if (_autoScreenshot != enabled) {
      _autoScreenshot = enabled;
      LoggerService.info('Auto screenshot set to: $enabled');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置通知显示
  void setShowNotifications(bool enabled) {
    if (_showNotifications != enabled) {
      _showNotifications = enabled;
      LoggerService.info('Show notifications set to: $enabled');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置声音启用
  void setEnableSounds(bool enabled) {
    if (_enableSounds != enabled) {
      _enableSounds = enabled;
      LoggerService.info('Enable sounds set to: $enabled');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置图片质量
  void setImageQuality(double quality) {
    final clampedQuality = quality.clamp(0.1, 1.0);
    if (_imageQuality != clampedQuality) {
      _imageQuality = clampedQuality;
      LoggerService.info('Image quality set to: $clampedQuality');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置截图间隔
  void setScreenshotInterval(int interval) {
    final clampedInterval = interval.clamp(1, 60);
    if (_screenshotInterval != clampedInterval) {
      _screenshotInterval = clampedInterval;
      LoggerService.info(
          'Screenshot interval set to: $clampedInterval seconds');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置截图格式
  void setScreenshotFormat(String format) {
    if (_screenshotFormat != format) {
      _screenshotFormat = format;
      LoggerService.info('Screenshot format set to: $format');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 重置截图计数
  void resetScreenshotCount() {
    _screenshotCount = 0;
    _lastScreenshotTime = null;
    LoggerService.info('Screenshot count reset');
    notifyListeners();
  }

  /// 设置窗口最大化状态
  void setMaximized(bool maximized) {
    if (_isMaximized != maximized) {
      _isMaximized = maximized;
      LoggerService.debug('Window maximized state: $maximized');
      notifyListeners();
    }
  }

  /// 设置窗口最小化状态
  void setMinimized(bool minimized) {
    if (_isMinimized != minimized) {
      _isMinimized = minimized;
      LoggerService.debug('Window minimized state: $minimized');
      notifyListeners();
    }
  }

  /// 设置窗口大小
  void setWindowSize(Size size) {
    if (_windowSize != size) {
      _windowSize = size;
      LoggerService.debug(
          'Window size changed to: ${size.width}x${size.height}');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 设置窗口位置
  void setWindowPosition(Offset position) {
    if (_windowPosition != position) {
      _windowPosition = position;
      LoggerService.debug(
          'Window position changed to: (${position.dx}, ${position.dy})');
      notifyListeners();
      _saveSettings();
    }
  }

  /// 重置所有设置
  void resetSettings() {
    LoggerService.info('Resetting all settings to defaults');

    _themeMode = ThemeMode.system;
    _language = AppLanguage.chinese;
    _autoConnect = true;
    _autoScreenshot = false;
    _showNotifications = true;
    _enableSounds = true;
    _imageQuality = 0.8;
    _screenshotInterval = 5;
    _screenshotFormat = 'PNG';
    _windowSize = const Size(800, 600);
    _windowPosition = const Offset(100, 100);

    _updateDarkMode();
    _updateLocale();

    notifyListeners();
    _saveSettings();
  }

  /// 检测系统主题
  void _detectSystemTheme() {
    // 这里可以通过平台API检测系统主题
    // 暂时默认为亮色主题
    _updateDarkMode();
  }

  /// 更新暗色模式状态
  void _updateDarkMode() {
    switch (_themeMode) {
      case ThemeMode.light:
        _isDarkMode = false;
        break;
      case ThemeMode.dark:
        _isDarkMode = true;
        break;
      case ThemeMode.system:
        // 这里应该检测系统主题，暂时默认为亮色
        _isDarkMode = false;
        break;
    }
  }

  /// 更新语言设置
  void _updateLocale() {
    switch (_language) {
      case AppLanguage.chinese:
        _locale = const Locale('zh', 'CN');
        break;
      case AppLanguage.english:
        _locale = const Locale('en', 'US');
        break;
    }
  }

  /// 保存设置到本地存储
  Future<void> _saveSettings() async {
    try {
      // 这里实现设置的本地存储
      // 可以使用 shared_preferences 或其他存储方案
      LoggerService.debug('Settings saved to local storage');
    } catch (e) {
      LoggerService.error('Failed to save settings', e);
    }
  }

  /// 从本地存储加载设置
  Future<void> _loadSettings() async {
    try {
      // 这里实现从本地存储加载设置
      LoggerService.debug('Settings loaded from local storage');
    } catch (e) {
      LoggerService.error('Failed to load settings', e);
    }
  }

  @override
  void dispose() {
    LoggerService.info('AppStateProvider disposed');
    super.dispose();
  }
}

/// 应用状态提供者实例
final appStateProvider = ChangeNotifierProvider<AppStateProvider>((ref) {
  return AppStateProvider();
});
