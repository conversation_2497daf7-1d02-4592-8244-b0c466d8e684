#!/usr/bin/env python3
"""
网关连接测试脚本
验证终端应用与网关服务的WebSocket连接
"""

import asyncio
import websockets
import json
import time

def test_http_connection():
    """测试HTTP连接"""
    import requests
    try:
        response = requests.get('http://localhost:8080', timeout=5)
        print(f"✓ HTTP连接成功: 状态码 {response.status_code}")
        print(f"  响应内容: {response.text[:100]}...")
        return True
    except Exception as e:
        print(f"✗ HTTP连接失败: {e}")
        return False

def test_health_endpoint():
    """测试健康检查端点"""
    import requests
    try:
        response = requests.get('http://localhost:8080/health', timeout=5)
        print(f"✓ 健康检查成功: 状态码 {response.status_code}")
        print(f"  健康状态: {response.json()}")
        return True
    except Exception as e:
        print(f"✗ 健康检查失败: {e}")
        return False

async def test_websocket_connection():
    """测试WebSocket连接"""
    try:
        async with websockets.connect('ws://localhost:8080/ws') as websocket:
            print("✓ WebSocket连接成功")
            
            # 发送测试消息
            test_message = {
                "type": "test",
                "message": "连接测试",
                "timestamp": int(time.time())
            }
            await websocket.send(json.dumps(test_message))
            print("✓ 测试消息发送成功")
            
            # 等待响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=3)
                print(f"✓ 收到响应: {response}")
                return True
            except asyncio.TimeoutError:
                print("⚠ 未收到响应（可能正常）")
                return True
                
    except Exception as e:
        print(f"✗ WebSocket连接失败: {e}")
        return False

def main():
    print("=" * 60)
    print("网关连接测试")
    print("=" * 60)
    
    print("\n1. 测试HTTP连接...")
    http_ok = test_http_connection()
    
    print("\n2. 测试健康检查端点...")
    health_ok = test_health_endpoint()
    
    print("\n3. 测试WebSocket连接...")
    try:
        loop = asyncio.get_event_loop()
        websocket_ok = loop.run_until_complete(test_websocket_connection())
    except Exception as e:
        print(f"✗ WebSocket测试异常: {e}")
        websocket_ok = False
    
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print(f"HTTP连接: {'✓' if http_ok else '✗'}")
    print(f"健康检查: {'✓' if health_ok else '✗'}")
    print(f"WebSocket: {'✓' if websocket_ok else '✗'}")
    
    if http_ok and health_ok and websocket_ok:
        print("\n🎉 所有连接测试成功！网关服务运行正常。")
        print("终端应用应该能够正常连接到网关。")
    else:
        print("\n❌ 部分连接测试失败，请检查网关服务配置。")
    
    print("=" * 60)

if __name__ == "__main__":
    main()