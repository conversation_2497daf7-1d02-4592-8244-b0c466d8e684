import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/foundation.dart';

import 'core/app.dart';
import 'core/services/storage_service.dart';
import 'core/services/logger_service.dart';
import 'core/services/device_service.dart';
import 'core/services/discovery_service.dart';
import 'core/services/display_service.dart';
import 'core/services/websocket_service.dart';
import 'core/services/screenshot_service.dart';
import 'core/services/screenshot_config_service.dart';
import 'core/services/image_compression_service.dart';

// 系统托盘事件常量
const String kSystemTrayEventClick = 'click';
const String kSystemTrayEventRightClick = 'right-click';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 只在桌面平台初始化window manager
  if (!kIsWeb) {
    await _initializeDesktopWindow();
  }
  
  // Initialize logger
  await LoggerService.instance.initialize();
  
  try {
    // Initialize Hive for local storage
    await Hive.initFlutter();
    
    // Initialize storage service
    await StorageService.instance.initialize();
    
    // Initialize screenshot config service
    await ScreenshotConfigService.instance.initialize();
    
    // AppConfig is now static, no need to initialize
    
    // Initialize device service
    await DeviceService.instance.initialize();
    
    // Initialize discovery service (desktop only)
    if (!kIsWeb) {
      await DiscoveryService.instance.startDiscovery();
    }

    // Initialize WebSocket service
    await WebSocketService.instance.initialize();

    // Initialize image compression service
    await ImageCompressionService.instance.initialize();

    // Initialize screenshot service (may have limited functionality on web)
    try {
      await ScreenshotService.instance.initialize();
    } catch (e) {
      LoggerService.warning('Screenshot service initialization failed (expected on web): $e');
    }

    // Initialize display service
    await DisplayService.instance.initialize();
    
    // Setup auto-startup if enabled (desktop only)
    if (!kIsWeb) {
      await _setupAutoStartup();
    }
    
    // Setup system tray
    // await _setupSystemTray(); // 暂时注释掉，等待修复system_tray包问题
    
    LoggerService.info('Terminal app initialization completed successfully');
    
    // Run the app
    runApp(
      const ProviderScope(
        child: FlutterImgctTerminalApp(),
      ),
    );
  } catch (error, stackTrace) {
    LoggerService.error('Failed to initialize terminal app', error, stackTrace);
    
    // Show error dialog or fallback UI
    runApp(
      MaterialApp(
        home: Scaffold(
          backgroundColor: Colors.black,
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Terminal App Initialization Failed',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 初始化桌面窗口（仅桌面平台）
Future<void> _initializeDesktopWindow() async {
  if (kIsWeb) {
    LoggerService.info('Web platform detected, skipping desktop window initialization');
    return; // Web平台直接返回
  }

  // 桌面平台的窗口初始化代码将在桌面版本中实现
  // 这里暂时跳过以避免Web兼容性问题
  LoggerService.info('Desktop platform detected, window initialization skipped for web compatibility');
}

Future<void> _setupAutoStartup() async {
  if (kIsWeb) {
    LoggerService.info('Web platform detected, skipping auto-startup setup');
    return; // Web平台不支持自启动
  }

  LoggerService.info('Desktop platform detected, auto-startup setup skipped for web compatibility');
}

// TODO: 重新实现system_tray功能
// 当前暂时移除system_tray相关代码以减少编译错误
// 需要在后续版本中重新添加系统托盘功能