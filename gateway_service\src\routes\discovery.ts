import { Router, Request, Response } from 'express';
import { DeviceDiscoveryService } from '../services/DeviceDiscoveryService';
import { WebSocketService } from '../services/WebSocketService';
import { databaseService } from '../services/DatabaseService';

const router = Router();

/// 获取设备发现服务状态
router.get('/status', async (req: Request, res: Response) => {
  try {
    const status = DeviceDiscoveryService.getStatus();
    
    res.json({
      success: true,
      data: status,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting discovery status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get discovery status',
      timestamp: new Date().toISOString()
    });
  }
});

/// 获取发现的设备列表
router.get('/devices', async (req: Request, res: Response) => {
  try {
    const devices = DeviceDiscoveryService.getDiscoveredDevices();
    
    // 添加在线状态信息
    const devicesWithStatus = devices.map(device => ({
      ...device,
      isOnline: DeviceDiscoveryService.isDeviceOnline(device.id),
      isConnected: false // Simplified implementation
    }));
    
    res.json({
      success: true,
      data: {
        devices: devicesWithStatus,
        total: devicesWithStatus.length,
        online: devicesWithStatus.filter(d => d.isOnline).length,
        connected: devicesWithStatus.filter(d => d.isConnected).length
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting discovered devices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get discovered devices',
      timestamp: new Date().toISOString()
    });
  }
});

/// 获取特定设备信息
router.get('/devices/:deviceId', async (req: Request, res: Response) => {
  try {
    const { deviceId } = req.params;
    if (!deviceId) {
      res.status(400).json({
        success: false,
        error: 'Device ID is required'
      });
      return;
    }
    const device = DeviceDiscoveryService.getInstance().getDiscoveredDevice(deviceId);
    
    if (!device) {
      return res.status(404).json({
        success: false,
        error: 'Device not found',
        timestamp: new Date().toISOString()
      });
    }
    
    // 添加详细状态信息
    const deviceWithStatus = {
      ...device,
      isOnline: DeviceDiscoveryService.isDeviceOnline(deviceId),
      isConnected: false, // Simplified implementation
      connectionInfo: null // Simplified implementation
    };
    
    res.json({
      success: true,
      data: deviceWithStatus,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting device info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get device info',
      timestamp: new Date().toISOString()
    });
  }
});

/// 手动触发设备发现
router.post('/scan', async (req: Request, res: Response) => {
  try {
    console.log('Manual device discovery triggered');
    
    const devices = await DeviceDiscoveryService.getInstance().discoverDevices();
    
    res.json({
      success: true,
      data: {
        message: 'Device discovery completed',
        devicesFound: devices.length,
        devices: devices.map((device: any) => ({
          id: device.id,
          name: device.name,
          type: device.type,
          platform: device.platform,
          ipAddress: device.ipAddress,
          lastSeen: device.lastSeen.toISOString()
        }))
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error during manual discovery:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to perform device discovery',
      timestamp: new Date().toISOString()
    });
  }
});

/// 更新设备发现配置
router.put('/config', async (req: Request, res: Response) => {
  try {
    const { config } = req.body;
    
    if (!config || typeof config !== 'object') {
      res.status(400).json({
        success: false,
        error: 'Invalid configuration provided',
        timestamp: new Date().toISOString()
      });
    }
    
    DeviceDiscoveryService.getInstance().updateConfig(config);
    
    res.json({
      success: true,
      data: {
        message: 'Configuration updated successfully',
        newConfig: DeviceDiscoveryService.getStatus().config
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error updating discovery config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update configuration',
      timestamp: new Date().toISOString()
    });
  }
});

/// 获取网络信息
router.get('/network', async (req: Request, res: Response) => {
  try {
    const status = DeviceDiscoveryService.getStatus();
    
    res.json({
      success: true,
      data: {
        localIpAddress: status.localIpAddress,
        broadcastAddresses: status.broadcastAddresses,
        listenPort: status.config.listenPort,
        broadcastPort: status.config.broadcastPort
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting network info:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get network information',
      timestamp: new Date().toISOString()
    });
  }
});

/// 获取发现历史记录
router.get('/history', async (req: Request, res: Response) => {
  try {
    const { limit = 50, offset = 0, deviceId } = req.query;
    
    let query = `
      SELECT * FROM discovered_devices 
      WHERE 1=1
    `;
    const params: any[] = [];
    
    if (deviceId) {
      query += ' AND id = ?';
      params.push(deviceId);
    }
    
    query += ' ORDER BY last_seen DESC LIMIT ? OFFSET ?';
    params.push(Number(limit), Number(offset));
    
    const devices = await databaseService.query(query, params);
    
    // 获取总数
    let countQuery = 'SELECT COUNT(*) as total FROM discovered_devices WHERE 1=1';
    const countParams: any[] = [];
    
    if (deviceId) {
      countQuery += ' AND id = ?';
      countParams.push(deviceId);
    }
    
    const countResult = await databaseService.query(countQuery, countParams);
    const total = countResult[0]?.total || 0;
    
    res.json({
      success: true,
      data: {
        devices: devices.map(device => ({
          ...device,
          capabilities: JSON.parse(device.capabilities || '{}'),
          metadata: JSON.parse(device.metadata || '{}')
        })),
        pagination: {
          total,
          limit: Number(limit),
          offset: Number(offset),
          hasMore: Number(offset) + Number(limit) < total
        }
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting discovery history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get discovery history',
      timestamp: new Date().toISOString()
    });
  }
});

/// 清理过期的发现记录
router.delete('/cleanup', async (req: Request, res: Response) => {
  try {
    const { olderThanDays = 30 } = req.body;
    
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - Number(olderThanDays));
    
    const result = await databaseService.query(
      'DELETE FROM discovered_devices WHERE last_seen < ?',
      [cutoffDate.toISOString()]
    );
    
    res.json({
      success: true,
      data: {
        message: `Cleaned up discovery records older than ${olderThanDays} days`,
        deletedCount: (result as any).changes || 0,
        cutoffDate: cutoffDate.toISOString()
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error cleaning up discovery records:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to cleanup discovery records',
      timestamp: new Date().toISOString()
    });
  }
});

/// 获取设备发现统计信息
router.get('/stats', async (req: Request, res: Response) => {
  try {
    // 获取基本统计
    const totalDevices = await databaseService.query(
      'SELECT COUNT(*) as count FROM discovered_devices'
    );
    
    const recentDevices = await databaseService.query(
      'SELECT COUNT(*) as count FROM discovered_devices WHERE last_seen > datetime("now", "-1 hour")'
    );
    
    const devicesByType = await databaseService.query(
      'SELECT type, COUNT(*) as count FROM discovered_devices GROUP BY type'
    );
    
    const devicesByPlatform = await databaseService.query(
      'SELECT platform, COUNT(*) as count FROM discovered_devices GROUP BY platform'
    );
    
    // 获取发现趋势（最近24小时）
    const discoveryTrend = await databaseService.query(`
      SELECT 
        strftime('%H', discovered_at) as hour,
        COUNT(*) as count
      FROM discovered_devices 
      WHERE discovered_at > datetime('now', '-24 hours')
      GROUP BY strftime('%H', discovered_at)
      ORDER BY hour
    `);
    
    res.json({
      success: true,
      data: {
        summary: {
          totalDevices: totalDevices[0]?.count || 0,
          recentDevices: recentDevices[0]?.count || 0,
          currentlyOnline: DeviceDiscoveryService.getDiscoveredDevices().filter(d => 
            DeviceDiscoveryService.isDeviceOnline(d.id)
          ).length
        },
        distribution: {
          byType: devicesByType.reduce((acc, row) => {
            acc[row.type] = row.count;
            return acc;
          }, {} as Record<string, number>),
          byPlatform: devicesByPlatform.reduce((acc, row) => {
            acc[row.platform] = row.count;
            return acc;
          }, {} as Record<string, number>)
        },
        trend: discoveryTrend.map(row => ({
          hour: parseInt(row.hour),
          count: row.count
        }))
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting discovery stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get discovery statistics',
      timestamp: new Date().toISOString()
    });
  }
});

export default router;