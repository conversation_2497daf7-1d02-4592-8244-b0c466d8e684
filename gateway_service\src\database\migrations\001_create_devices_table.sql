-- 创建设备表
CREATE TABLE IF NOT EXISTS devices (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'terminal',
    ip_address TEXT NOT NULL,
    port INTEGER NOT NULL DEFAULT 8080,
    status TEXT NOT NULL DEFAULT 'offline',
    config TEXT NOT NULL DEFAULT '{}', -- JSON格式的设备配置
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_devices_ip_address ON devices(ip_address);
CREATE INDEX IF NOT EXISTS idx_devices_type ON devices(type);
CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status);
CREATE INDEX IF NOT EXISTS idx_devices_created_at ON devices(created_at);

-- 创建触发器以自动更新updated_at字段
CREATE TRIGGER IF NOT EXISTS update_devices_updated_at
    AFTER UPDATE ON devices
    FOR EACH ROW
BEGIN
    UPDATE devices SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 创建图片表
CREATE TABLE IF NOT EXISTS images (
    id TEXT PRIMARY KEY,
    filename TEXT NOT NULL,
    original_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    thumbnail_path TEXT,
    mime_type TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    width INTEGER,
    height INTEGER,
    device_id TEXT,
    metadata TEXT DEFAULT '{}', -- JSON格式的图片元数据
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE SET NULL
);

-- 创建图片表索引
CREATE INDEX IF NOT EXISTS idx_images_device_id ON images(device_id);
CREATE INDEX IF NOT EXISTS idx_images_filename ON images(filename);
CREATE INDEX IF NOT EXISTS idx_images_created_at ON images(created_at);
CREATE INDEX IF NOT EXISTS idx_images_mime_type ON images(mime_type);

-- 创建图片表触发器
CREATE TRIGGER IF NOT EXISTS update_images_updated_at
    AFTER UPDATE ON images
    FOR EACH ROW
BEGIN
    UPDATE images SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 插入一些示例数据（可选）
-- INSERT OR IGNORE INTO devices (id, name, type, ip_address, port, status, config)
-- VALUES 
--     ('example_device_1', 'Example Terminal 1', 'terminal', '*************', 8080, 'offline', '{}'),
--     ('example_device_2', 'Example Terminal 2', 'terminal', '*************', 8080, 'offline', '{}');