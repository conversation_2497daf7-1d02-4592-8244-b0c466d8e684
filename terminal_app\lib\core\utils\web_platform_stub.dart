/// Web平台Platform类的stub实现
/// 用于在Web平台上提供Platform类的兼容接口
library;

class Platform {
  /// 操作系统名称
  static String get operatingSystem => 'web';

  /// 操作系统版本
  static String get operatingSystemVersion => 'Web Browser';

  /// 本地主机名
  static String get localHostname => 'Web-Terminal';

  /// 环境变量
  static Map<String, String> get environment => <String, String>{};

  /// 是否为Windows平台
  static bool get isWindows => false;

  /// 是否为macOS平台
  static bool get isMacOS => false;

  /// 是否为Linux平台
  static bool get isLinux => false;

  /// 是否为Android平台
  static bool get isAndroid => false;

  /// 是否为iOS平台
  static bool get isIOS => false;
}
