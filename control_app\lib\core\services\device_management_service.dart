import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/device_model.dart';
import '../models/websocket_message.dart';
import 'websocket_service.dart';
import 'storage_service.dart';

/// 设备管理服务
/// 负责设备的发现、注册、状态监控和操作管理
class DeviceManagementService {
  static final DeviceManagementService _instance = DeviceManagementService._internal();
  factory DeviceManagementService() => _instance;
  DeviceManagementService._internal();

  // 服务依赖
  final WebSocketService _webSocketService = WebSocketService();
  final StorageService _storageService = StorageService();

  // 设备数据
  final Map<String, Device> _devices = {};
  final Map<String, DeviceGroup> _deviceGroups = {};
  
  // 流控制器
  final StreamController<List<Device>> _devicesController = 
      StreamController<List<Device>>.broadcast();
  final StreamController<Device> _deviceUpdateController = 
      StreamController<Device>.broadcast();
  final StreamController<List<DeviceGroup>> _groupsController = 
      StreamController<List<DeviceGroup>>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();

  // 定时器
  Timer? _statusUpdateTimer;
  Timer? _discoveryTimer;
  
  // 配置
  Duration _statusUpdateInterval = const Duration(seconds: 30);
  Duration _discoveryInterval = const Duration(minutes: 5);
  bool _autoDiscovery = true;
  
  // Getters
  List<Device> get devices => _devices.values.toList();
  List<DeviceGroup> get deviceGroups => _deviceGroups.values.toList();
  Stream<List<Device>> get devicesStream => _devicesController.stream;
  Stream<Device> get deviceUpdateStream => _deviceUpdateController.stream;
  Stream<List<DeviceGroup>> get groupsStream => _groupsController.stream;
  Stream<String> get errorStream => _errorController.stream;
  
  bool get isInitialized => _devices.isNotEmpty || _deviceGroups.isNotEmpty;
  int get deviceCount => _devices.length;
  int get onlineDeviceCount => _devices.values.where((d) => d.isOnline).length;
  int get connectedDeviceCount => _devices.values.where((d) => d.isConnected).length;

  /// 初始化设备管理服务
  Future<void> initialize() async {
    try {
      // 加载本地存储的设备数据
      await _loadDevicesFromStorage();
      await _loadGroupsFromStorage();
      
      // 初始化WebSocket连接
      await _initializeWebSocketConnection();
      
      // 设置WebSocket消息监听
      _setupWebSocketListeners();
      
      // 启动定时任务
      _startPeriodicTasks();
      
      // 请求最新的设备列表
      await refreshDevices();
      
      debugPrint('DeviceManagementService initialized');
    } catch (e) {
      debugPrint('Error initializing DeviceManagementService: $e');
      _errorController.add('Initialization failed: $e');
    }
  }
  
  /// 初始化WebSocket连接
  Future<void> _initializeWebSocketConnection() async {
    try {
      // 获取网关URL - 使用WebSocket端点
      final gatewayUrl = 'ws://localhost:7777/controller/ws';
      
      // 初始化WebSocket服务
      await _webSocketService.initialize(
        gatewayUrl: gatewayUrl,
        clientInfo: {
          'type': 'control',
          'platform': 'windows',
          'version': '1.0.0',
          'name': 'Control App',
        },
      );
      
      debugPrint('WebSocket connection initialized: $gatewayUrl');
    } catch (e) {
      debugPrint('Error initializing WebSocket connection: $e');
      _errorController.add('WebSocket initialization failed: $e');
      rethrow;
    }
  }

  /// 设置WebSocket监听器
  void _setupWebSocketListeners() {
    // 监听设备列表更新
    _webSocketService.deviceListStream.listen(
      (devices) {
        _updateDeviceList(devices);
      },
      onError: (error) {
        debugPrint('Device list stream error: $error');
        _errorController.add('Device list update error: $error');
      },
    );

    // 监听设备状态更新
    _webSocketService.deviceUpdateStream.listen(
      (device) {
        _updateDevice(device);
      },
      onError: (error) {
        debugPrint('Device update stream error: $error');
        _errorController.add('Device update error: $error');
      },
    );

    // 监听WebSocket消息
    _webSocketService.messageStream.listen(
      (message) {
        _handleWebSocketMessage(message);
      },
      onError: (error) {
        debugPrint('WebSocket message stream error: $error');
        _errorController.add('Message handling error: $error');
      },
    );
  }

  /// 处理WebSocket消息
  void _handleWebSocketMessage(WebSocketMessage message) {
    switch (message.type) {
      case WebSocketMessageType.device:
        _handleDeviceMessage(message);
        break;
      case WebSocketMessageType.capture:
        _handleCaptureMessage(message);
        break;
      case WebSocketMessageType.discovery:
        _handleDiscoveryMessage(message);
        break;
      case WebSocketMessageType.error:
        _handleErrorMessage(message);
        break;
      default:
        break;
    }
  }

  /// 处理设备消息
  void _handleDeviceMessage(WebSocketMessage message) {
    switch (message.action) {
      case WebSocketMessageAction.register:
        _handleDeviceRegistration(message);
        break;
      case WebSocketMessageAction.update:
        _handleDeviceUpdate(message);
        break;
      case WebSocketMessageAction.disconnect:
        _handleDeviceDisconnection(message);
        break;
      default:
        break;
    }
  }

  /// 处理设备注册
  void _handleDeviceRegistration(WebSocketMessage message) {
    try {
      final deviceData = message.data;
      if (deviceData != null) {
        final device = Device.fromJson(deviceData);
        _updateDevice(device);
        debugPrint('Device registered: ${device.name} (${device.id})');
      }
    } catch (e) {
      debugPrint('Error handling device registration: $e');
      _errorController.add('Device registration error: $e');
    }
  }

  /// 处理设备更新
  void _handleDeviceUpdate(WebSocketMessage message) {
    try {
      final deviceData = message.data;
      if (deviceData != null) {
        final device = Device.fromJson(deviceData);
        _updateDevice(device);
      }
    } catch (e) {
      debugPrint('Error handling device update: $e');
      _errorController.add('Device update error: $e');
    }
  }

  /// 处理设备断开连接
  void _handleDeviceDisconnection(WebSocketMessage message) {
    try {
      final deviceId = message.deviceId;
      if (deviceId != null && _devices.containsKey(deviceId)) {
        final device = _devices[deviceId]!;
        final updatedDevice = device.copyWith(
          status: DeviceStatus.offline,
          lastSeen: DateTime.now(),
        );
        _updateDevice(updatedDevice);
        debugPrint('Device disconnected: ${device.name} (${device.id})');
      }
    } catch (e) {
      debugPrint('Error handling device disconnection: $e');
      _errorController.add('Device disconnection error: $e');
    }
  }

  /// 处理截图消息
  void _handleCaptureMessage(WebSocketMessage message) {
    final deviceId = message.deviceId;
    if (deviceId != null && _devices.containsKey(deviceId)) {
      final device = _devices[deviceId]!;
      
      switch (message.action) {
        case WebSocketMessageAction.start:
          _updateDeviceStatus(deviceId, DeviceStatus.capturing);
          break;
        case WebSocketMessageAction.complete:
          _updateDeviceStatus(deviceId, DeviceStatus.online);
          break;
        case WebSocketMessageAction.failed:
          _updateDeviceStatus(deviceId, DeviceStatus.error);
          break;
        default:
          break;
      }
    }
  }

  /// 处理发现消息
  void _handleDiscoveryMessage(WebSocketMessage message) {
    switch (message.action) {
      case WebSocketMessageAction.announce:
        _handleDeviceAnnouncement(message);
        break;
      default:
        break;
    }
  }

  /// 处理设备公告
  void _handleDeviceAnnouncement(WebSocketMessage message) {
    try {
      final deviceData = message.data;
      if (deviceData != null) {
        final device = Device.fromJson(deviceData);
        _updateDevice(device);
        debugPrint('Device discovered: ${device.name} (${device.id})');
      }
    } catch (e) {
      debugPrint('Error handling device announcement: $e');
      _errorController.add('Device announcement error: $e');
    }
  }

  /// 处理错误消息
  void _handleErrorMessage(WebSocketMessage message) {
    final errorMessage = message.errorMessage ?? 'Unknown error';
    _errorController.add(errorMessage);
  }

  /// 更新设备列表
  void _updateDeviceList(List<Device> devices) {
    for (final device in devices) {
      _devices[device.id] = device;
    }
    _devicesController.add(_devices.values.toList());
    _saveDevicesToStorage();
  }

  /// 更新单个设备
  void _updateDevice(Device device) {
    final existingDevice = _devices[device.id];
    
    if (existingDevice != null) {
      // 合并设备信息，保留本地配置
      final updatedDevice = existingDevice.copyWith(
        name: device.name,
        type: device.type,
        platform: device.platform,
        version: device.version,
        status: device.status,
        capabilities: device.capabilities,
        ipAddress: device.ipAddress,
        port: device.port,
        lastSeen: device.lastSeen,
      );
      _devices[device.id] = updatedDevice;
    } else {
      _devices[device.id] = device;
    }
    
    _devicesController.add(_devices.values.toList());
    _deviceUpdateController.add(_devices[device.id]!);
    _saveDevicesToStorage();
  }

  /// 更新设备状态
  void _updateDeviceStatus(String deviceId, DeviceStatus status) {
    if (_devices.containsKey(deviceId)) {
      final device = _devices[deviceId]!;
      final updatedDevice = device.copyWith(
        status: status,
        lastSeen: DateTime.now(),
      );
      _updateDevice(updatedDevice);
    }
  }

  /// 刷新设备列表
  Future<void> refreshDevices() async {
    try {
      if (_webSocketService.isConnected) {
        _webSocketService.requestDeviceList();
        _webSocketService.requestDeviceDiscovery();
      } else {
        _errorController.add('WebSocket not connected');
      }
    } catch (e) {
      debugPrint('Error refreshing devices: $e');
      _errorController.add('Refresh devices error: $e');
    }
  }

  /// 获取设备
  Device? getDevice(String deviceId) {
    return _devices[deviceId];
  }

  /// 获取所有设备
  List<Device> getAllDevices() {
    return _devices.values.toList();
  }

  /// 更新设备信息
  Future<void> updateDevice(String deviceId, {String? name, String? address}) async {
    final device = _devices[deviceId];
    if (device == null) {
      throw Exception('Device not found: $deviceId');
    }

    // 解析地址为IP和端口
    String? ipAddress;
    int? port;
    if (address != null && address.contains(':')) {
      final parts = address.split(':');
      if (parts.length == 2) {
        ipAddress = parts[0];
        port = int.tryParse(parts[1]);
      }
    }

    final updatedDevice = device.copyWith(
      name: name,
      ipAddress: ipAddress,
      port: port,
      lastSeen: DateTime.now(),
    );

    _updateDevice(updatedDevice);
  }

  /// 获取在线设备
  List<Device> getOnlineDevices() {
    return _devices.values.where((device) => device.isOnline).toList();
  }

  /// 获取已连接设备
  List<Device> getConnectedDevices() {
    return _devices.values.where((device) => device.isConnected).toList();
  }

  /// 按类型获取设备
  List<Device> getDevicesByType(DeviceType type) {
    return _devices.values.where((device) => device.type == type).toList();
  }

  /// 按平台获取设备
  List<Device> getDevicesByPlatform(String platform) {
    return _devices.values.where((device) => device.platform == platform).toList();
  }

  /// 搜索设备
  List<Device> searchDevices(String query) {
    final lowerQuery = query.toLowerCase();
    return _devices.values.where((device) {
      return device.name.toLowerCase().contains(lowerQuery) ||
             device.id.toLowerCase().contains(lowerQuery) ||
             device.platform.toLowerCase().contains(lowerQuery) ||
             (device.ipAddress?.toLowerCase().contains(lowerQuery) ?? false);
    }).toList();
  }

  /// 请求设备截图
  Future<void> requestCapture(String deviceId, {Map<String, dynamic>? options}) async {
    try {
      final device = getDevice(deviceId);
      if (device == null) {
        throw Exception('Device not found: $deviceId');
      }
      
      if (!device.isOnline) {
        throw Exception('Device is offline: ${device.name}');
      }
      
      if (!device.capabilities.canCapture) {
        throw Exception('Device does not support capture: ${device.name}');
      }
      
      _webSocketService.requestCapture(deviceId, options: options);
      _updateDeviceStatus(deviceId, DeviceStatus.capturing);
      
    } catch (e) {
      debugPrint('Error requesting capture: $e');
      _errorController.add('Capture request error: $e');
      rethrow;
    }
  }

  /// 控制设备显示
  Future<void> controlDisplay(String deviceId, String imagePath, {Map<String, dynamic>? options}) async {
    try {
      final device = getDevice(deviceId);
      if (device == null) {
        throw Exception('Device not found: $deviceId');
      }
      
      if (!device.isOnline) {
        throw Exception('Device is offline: ${device.name}');
      }
      
      if (!device.capabilities.canDisplay) {
        throw Exception('Device does not support display: ${device.name}');
      }
      
      _webSocketService.controlDisplay(deviceId, imagePath, options: options);
      
    } catch (e) {
      debugPrint('Error controlling display: $e');
      _errorController.add('Display control error: $e');
      rethrow;
    }
  }

  /// 创建设备组
  Future<DeviceGroup> createDeviceGroup({
    required String name,
    String? description,
    List<String>? deviceIds,
  }) async {
    final group = DeviceGroup(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      description: description ?? '',
      deviceIds: deviceIds ?? [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    _deviceGroups[group.id] = group;
    _groupsController.add(_deviceGroups.values.toList());
    await _saveGroupsToStorage();
    
    return group;
  }

  /// 更新设备组
  Future<void> updateDeviceGroup(DeviceGroup group) async {
    _deviceGroups[group.id] = group.copyWith(updatedAt: DateTime.now());
    _groupsController.add(_deviceGroups.values.toList());
    await _saveGroupsToStorage();
  }

  /// 删除设备组
  Future<void> deleteDeviceGroup(String groupId) async {
    _deviceGroups.remove(groupId);
    _groupsController.add(_deviceGroups.values.toList());
    await _saveGroupsToStorage();
  }

  /// 添加设备到组
  Future<void> addDeviceToGroup(String groupId, String deviceId) async {
    final group = _deviceGroups[groupId];
    if (group != null && !group.deviceIds.contains(deviceId)) {
      final updatedGroup = group.copyWith(
        deviceIds: [...group.deviceIds, deviceId],
        updatedAt: DateTime.now(),
      );
      await updateDeviceGroup(updatedGroup);
    }
  }

  /// 从组中移除设备
  Future<void> removeDeviceFromGroup(String groupId, String deviceId) async {
    final group = _deviceGroups[groupId];
    if (group != null && group.deviceIds.contains(deviceId)) {
      final updatedGroup = group.copyWith(
        deviceIds: group.deviceIds.where((id) => id != deviceId).toList(),
        updatedAt: DateTime.now(),
      );
      await updateDeviceGroup(updatedGroup);
    }
  }

  /// 获取组中的设备
  List<Device> getDevicesInGroup(String groupId) {
    final group = _deviceGroups[groupId];
    if (group == null) return [];
    
    return group.deviceIds
        .map((deviceId) => _devices[deviceId])
        .where((device) => device != null)
        .cast<Device>()
        .toList();
  }

  /// 批量操作组中的设备
  Future<void> batchOperationOnGroup(String groupId, Future<void> Function(Device device) operation) async {
    final devices = getDevicesInGroup(groupId);
    final futures = devices.map((device) => operation(device));
    await Future.wait(futures);
  }

  /// 启动定时任务
  void _startPeriodicTasks() {
    // 状态更新定时器
    _statusUpdateTimer = Timer.periodic(_statusUpdateInterval, (_) {
      _updateDeviceStatuses();
    });
    
    // 设备发现定时器
    if (_autoDiscovery) {
      _discoveryTimer = Timer.periodic(_discoveryInterval, (_) {
        refreshDevices();
      });
    }
  }

  /// 停止定时任务
  void _stopPeriodicTasks() {
    _statusUpdateTimer?.cancel();
    _discoveryTimer?.cancel();
  }

  /// 更新设备状态
  void _updateDeviceStatuses() {
    final now = DateTime.now();
    bool hasUpdates = false;
    
    for (final device in _devices.values) {
      final timeSinceLastSeen = now.difference(device.lastSeen);
      
      // 如果设备超过2分钟没有响应，标记为离线
      if (timeSinceLastSeen.inMinutes > 2 && device.status != DeviceStatus.offline) {
        final updatedDevice = device.copyWith(status: DeviceStatus.offline);
        _devices[device.id] = updatedDevice;
        hasUpdates = true;
      }
    }
    
    if (hasUpdates) {
      _devicesController.add(_devices.values.toList());
      _saveDevicesToStorage();
    }
  }

  /// 从存储加载设备
  Future<void> _loadDevicesFromStorage() async {
    try {
      final devicesJson = await _storageService.getString('devices');
      if (devicesJson != null) {
        final List<dynamic> devicesList = jsonDecode(devicesJson);
        for (final deviceData in devicesList) {
          final device = Device.fromJson(deviceData);
          _devices[device.id] = device;
        }
        _devicesController.add(_devices.values.toList());
      }
    } catch (e) {
      debugPrint('Error loading devices from storage: $e');
    }
  }

  /// 保存设备到存储
  Future<void> _saveDevicesToStorage() async {
    try {
      final devicesList = _devices.values.map((device) => device.toJson()).toList();
      final devicesJson = jsonEncode(devicesList);
      await _storageService.setString('devices', devicesJson);
    } catch (e) {
      debugPrint('Error saving devices to storage: $e');
    }
  }

  /// 从存储加载设备组
  Future<void> _loadGroupsFromStorage() async {
    try {
      final groupsJson = await _storageService.getString('device_groups');
      if (groupsJson != null) {
        final List<dynamic> groupsList = jsonDecode(groupsJson);
        for (final groupData in groupsList) {
          final group = DeviceGroup.fromJson(groupData);
          _deviceGroups[group.id] = group;
        }
        _groupsController.add(_deviceGroups.values.toList());
      }
    } catch (e) {
      debugPrint('Error loading groups from storage: $e');
    }
  }

  /// 保存设备组到存储
  Future<void> _saveGroupsToStorage() async {
    try {
      final groupsList = _deviceGroups.values.map((group) => group.toJson()).toList();
      final groupsJson = jsonEncode(groupsList);
      await _storageService.setString('device_groups', groupsJson);
    } catch (e) {
      debugPrint('Error saving groups to storage: $e');
    }
  }

  /// 更新配置
  void updateConfig({
    Duration? statusUpdateInterval,
    Duration? discoveryInterval,
    bool? autoDiscovery,
  }) {
    if (statusUpdateInterval != null) {
      _statusUpdateInterval = statusUpdateInterval;
    }
    if (discoveryInterval != null) {
      _discoveryInterval = discoveryInterval;
    }
    if (autoDiscovery != null) {
      _autoDiscovery = autoDiscovery;
    }
    
    // 重启定时任务
    _stopPeriodicTasks();
    _startPeriodicTasks();
  }

  /// 获取统计信息
  Map<String, dynamic> getStatistics() {
    final devicesByType = <String, int>{};
    final devicesByPlatform = <String, int>{};
    final devicesByStatus = <String, int>{};
    
    for (final device in _devices.values) {
      // 按类型统计
      final type = device.type.name;
      devicesByType[type] = (devicesByType[type] ?? 0) + 1;
      
      // 按平台统计
      devicesByPlatform[device.platform] = (devicesByPlatform[device.platform] ?? 0) + 1;
      
      // 按状态统计
      final status = device.status.name;
      devicesByStatus[status] = (devicesByStatus[status] ?? 0) + 1;
    }
    
    return {
      'totalDevices': _devices.length,
      'onlineDevices': onlineDeviceCount,
      'connectedDevices': connectedDeviceCount,
      'totalGroups': _deviceGroups.length,
      'devicesByType': devicesByType,
      'devicesByPlatform': devicesByPlatform,
      'devicesByStatus': devicesByStatus,
      'lastUpdate': DateTime.now().toIso8601String(),
    };
  }

  /// 清理离线设备
  Future<void> cleanupOfflineDevices({Duration? olderThan}) async {
    final cutoff = DateTime.now().subtract(olderThan ?? const Duration(days: 7));
    final toRemove = <String>[];
    
    for (final device in _devices.values) {
      if (device.status == DeviceStatus.offline && device.lastSeen.isBefore(cutoff)) {
        toRemove.add(device.id);
      }
    }
    
    for (final deviceId in toRemove) {
      _devices.remove(deviceId);
    }
    
    if (toRemove.isNotEmpty) {
      _devicesController.add(_devices.values.toList());
      await _saveDevicesToStorage();
      debugPrint('Cleaned up ${toRemove.length} offline devices');
    }
  }

  /// 请求设备截图
  Future<void> requestScreenshot(String deviceId) async {
    try {
      final device = getDevice(deviceId);
      if (device == null) {
        throw Exception('Device not found: $deviceId');
      }
      
      if (!device.isOnline) {
        throw Exception('Device is offline: ${device.name}');
      }
      
      if (!device.capabilities.canCapture) {
        throw Exception('Device does not support capture: ${device.name}');
      }
      
      _webSocketService.requestCapture(deviceId);
      _updateDeviceStatus(deviceId, DeviceStatus.capturing);
      
    } catch (e) {
      debugPrint('Error requesting screenshot: $e');
      _errorController.add('Screenshot request error: $e');
      rethrow;
    }
  }

  /// 测试设备连接
  Future<bool> testConnection(String deviceId) async {
    try {
      final device = getDevice(deviceId);
      if (device == null) {
        throw Exception('Device not found: $deviceId');
      }
      
      debugPrint('Testing connection to device: ${device.name}');
      
      // 模拟连接测试
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 这里可以添加实际的连接测试逻辑
      // 例如：ping设备、检查WebSocket连接等
      
      debugPrint('Connection test successful for device: ${device.name}');
      return true;
    } catch (e) {
      debugPrint('Connection test failed for device $deviceId: $e');
      _errorController.add('Connection test error: $e');
      return false;
    }
  }

  /// 添加设备
  Future<void> addDevice(String name, String ip, int port) async {
    try {
      final deviceId = '${ip}_$port';
      
      // 检查设备是否已存在
      if (_devices.containsKey(deviceId)) {
        throw Exception('设备已存在: $name');
      }
      
      // 创建新设备
      final device = Device(
        id: deviceId,
        name: name,
        ipAddress: ip,
        port: port,
        type: DeviceType.terminal,
        status: DeviceStatus.offline,
        platform: 'unknown',
        version: '1.0.0',
        capabilities: const DeviceCapabilities(
          canCapture: true,
          canDisplay: true,
          canRecord: false,
          canStream: false,
          supportedFormats: ['jpg', 'png'],
          maxResolution: '1920x1080',
          hasCamera: false,
          hasMicrophone: false,
          hasStorage: true,
          hasNetwork: true,
        ),
        config: const DeviceConfig(
          autoCapture: false,
          captureInterval: 30,
          captureQuality: 80,
          captureFormat: 'png',
          displayTimeout: 0,
          enableNotifications: true,
          enableSound: true,
          enableVibration: false,
          theme: 'system',
          language: 'zh-CN',
        ),
        lastSeen: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      // 添加到设备列表
      _devices[deviceId] = device;
      _devicesController.add(_devices.values.toList());
      await _saveDevicesToStorage();
      
      debugPrint('Device added manually: $name ($ip:$port)');
      
    } catch (e) {
      debugPrint('Error adding device: $e');
      _errorController.add('Add device error: $e');
      rethrow;
    }
  }

  /// 移除设备
  Future<void> removeDevice(String deviceId) async {
    try {
      final device = _devices[deviceId];
      if (device == null) {
        throw Exception('Device not found: $deviceId');
      }
      
      // 从所有设备组中移除该设备
      for (final group in _deviceGroups.values) {
        if (group.deviceIds.contains(deviceId)) {
          await removeDeviceFromGroup(group.id, deviceId);
        }
      }
      
      // 从设备列表中移除
      _devices.remove(deviceId);
      _devicesController.add(_devices.values.toList());
      await _saveDevicesToStorage();
      
      debugPrint('Device removed: ${device.name}');
      
    } catch (e) {
      debugPrint('Error removing device: $e');
      _errorController.add('Remove device error: $e');
      rethrow;
    }
  }

  /// 扫描设备
  Future<void> scanForDevices() async {
    try {
      debugPrint('开始扫描设备...');
      
      // 发送设备发现请求
      final message = WebSocketMessage(
        type: WebSocketMessageType.discovery,
        action: WebSocketMessageAction.scan,
        timestamp: DateTime.now(),
      );
      
      _webSocketService.sendMessage(message);
      
      debugPrint('设备扫描请求已发送');
    } catch (e) {
      debugPrint('扫描设备失败: $e');
      _errorController.add('设备扫描失败: $e');
      rethrow;
    }
  }

  /// 停止扫描设备
  Future<void> stopScanning() async {
    try {
      debugPrint('停止扫描设备...');
      
      // 发送停止扫描请求
      final message = WebSocketMessage(
        type: WebSocketMessageType.discovery,
        action: WebSocketMessageAction.stop,
        timestamp: DateTime.now(),
      );
      
      _webSocketService.sendMessage(message);
      
      debugPrint('停止扫描请求已发送');
    } catch (e) {
      debugPrint('停止扫描失败: $e');
      _errorController.add('停止扫描失败: $e');
      rethrow;
    }
  }

  /// 连接到设备
  Future<bool> connectToDevice(String deviceId) async {
    try {
      final device = _devices[deviceId];
      if (device == null) {
        throw Exception('设备不存在');
      }
      
      debugPrint('正在连接设备: ${device.name}');
      
      // 发送连接请求
      final message = WebSocketMessage(
        type: WebSocketMessageType.device,
        action: WebSocketMessageAction.connect,
        data: {'deviceId': deviceId},
        timestamp: DateTime.now(),
      );
      
      _webSocketService.sendMessage(message);
      
      // 更新设备状态
      final updatedDevice = device.copyWith(
        lastSeen: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _updateDevice(updatedDevice);
      
      debugPrint('设备连接请求已发送: ${device.name}');
      return true;
    } catch (e) {
      debugPrint('连接设备失败: $e');
      _errorController.add('连接设备失败: $e');
      return false;
    }
  }

  /// 连接设备
  Future<bool> connectDevice(String deviceId) async {
    try {
      final device = _devices[deviceId];
      if (device == null) {
        throw Exception('设备不存在');
      }
      
      if (device.status == DeviceStatus.offline) {
        debugPrint('设备 ${device.name} 处于离线状态，无法连接');
        return false;
      }
      
      // 更新设备连接状态
      final updatedDevice = device.copyWith(
        lastSeen: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _updateDevice(updatedDevice);
      
      debugPrint('设备 ${device.name} 连接成功');
      return true;
    } catch (e) {
      debugPrint('连接设备失败: $e');
      return false;
    }
  }
  
  /// 断开设备连接
  Future<bool> disconnectDevice(String deviceId) async {
    try {
      final device = _devices[deviceId];
      if (device == null) {
        throw Exception('设备不存在');
      }
      
      // 更新设备连接状态
      final updatedDevice = device.copyWith(
        status: DeviceStatus.offline,
        lastSeen: DateTime.now(),
        updatedAt: DateTime.now(),
      );
      
      _updateDevice(updatedDevice);
      
      debugPrint('设备 ${device.name} 断开连接');
      return true;
    } catch (e) {
      debugPrint('断开设备连接失败: $e');
      return false;
    }
  }

  /// 释放资源
  void dispose() {
    _stopPeriodicTasks();
    _devicesController.close();
    _deviceUpdateController.close();
    _groupsController.close();
    _errorController.close();
  }
}