# Flutter 终端应用启动脚本说明

## 概述
为 imgCT 项目创建了多个终端应用启动脚本，以满足不同的使用场景。

## 脚本文件列表

### 1. start_terminal.bat (主启动脚本)
- **用途**: 完整功能的主启动脚本
- **特性**: 
  - 完整的环境检查和验证
  - 彩色输出和进度提示
  - 网关服务状态检查
  - 自动错误诊断和修复建议
  - 发布模式启动 (--release)
- **适用场景**: 生产环境、首次运行、完整功能验证

### 2. start_terminal_quick.bat (快速启动脚本)
- **用途**: 日常开发使用的快速启动
- **特性**:
  - 简化的环境检查
  - 跳过不必要的验证步骤
  - 热重载模式 (--hot)
  - 快速启动，减少等待时间
- **适用场景**: 日常开发、频繁测试、快速验证

### 3. start_terminal_debug.bat (调试脚本)
- **用途**: 问题排查和调试
- **特性**:
  - 每个步骤后暂停，方便查看详细信息
  - 详细的错误日志和诊断信息
  - 详细模式启动 (--verbose)
  - 完整的环境状态报告
- **适用场景**: 问题排查、环境调试、首次配置

### 4. start_terminal.ps1 (PowerShell版本)
- **用途**: PowerShell环境下的高级启动脚本
- **特性**:
  - 支持命令行参数
  - 更好的错误处理
  - 彩色输出和进度显示
  - 多种启动模式
- **适用场景**: PowerShell用户、自动化集成、CI/CD

## 使用方法

### 基本使用
```batch
# 标准启动
start_terminal.bat

# 快速启动 (推荐日常使用)
start_terminal_quick.bat

# 调试模式
start_terminal_debug.bat
```

### PowerShell版本使用
```powershell
# 标准模式
.\start_terminal.ps1

# 快速模式
.\start_terminal.ps1 -Quick

# 调试模式
.\start_terminal.ps1 -Debug

# 清理后启动
.\start_terminal.ps1 -Clean
```

## 前置条件

### 必需环境
1. **Flutter SDK 3.0+**: 确保已安装并配置在系统PATH中
2. **Visual Studio 2022**: 包含Windows桌面开发组件
3. **Windows 10+**: 支持Windows桌面应用开发

### 环境配置
```batch
# 启用Windows桌面支持
flutter config --enable-windows-desktop

# 检查环境状态
flutter doctor

# 验证设备
flutter devices
```

## 项目结构要求

脚本需要在项目根目录下运行，项目结构应如下：
```
imgCT/
├── terminal_app/
│   ├── pubspec.yaml
│   ├── lib/
│   └── windows/
├── gateway_service/
├── start_terminal.bat
├── start_terminal_quick.bat
├── start_terminal_debug.bat
└── start_terminal.ps1
```

## 网关服务集成

### 自动检查
所有脚本都会自动检查网关服务状态：
- **端口**: 7777
- **健康检查**: http://localhost:7777/health
- **路径**: /terminal/ws (WebSocket连接)

### 网关服务状态
- ✅ **服务运行**: 终端应用将自动连接网关
- ⚠️ **服务未运行**: 应用可独立运行，但无网关通信功能
- ❌ **连接失败**: 脚本会提示用户决定是否继续

## 常见问题解决

### 1. Flutter环境问题
```batch
# 重新安装Flutter
flutter doctor
flutter config --enable-windows-desktop
flutter clean
flutter pub get
```

### 2. 网关服务问题
```batch
# 启动网关服务
cd gateway_service
node final_unified_gateway.js
```

### 3. 依赖问题
```batch
# 清理和重新获取依赖
flutter clean
flutter pub cache repair
flutter pub get
```

### 4. Windows桌面支持
```batch
# 启用Windows桌面支持
flutter config --enable-windows-desktop

# 检查Visual Studio组件
flutter doctor
```

## 脚本特性

### 安全特性
- ✅ 路径验证和目录检查
- ✅ 环境状态验证
- ✅ 自动错误恢复
- ✅ 用户确认机制

### 用户体验
- 🎨 彩色输出和进度提示
- 📊 详细的状态报告
- 🔧 自动化错误诊断
- 💡 智能修复建议

### 开发友好
- 🚀 多种启动模式
- 🔍 详细的调试信息
- ⚡ 快速启动选项
- 🛠️ 灵活的配置选项

## 版本信息
- **版本**: 2.0.0
- **架构**: 统一网关架构
- **支持平台**: Windows 10+
- **Flutter版本**: 3.0+
- **创建日期**: 2025-01-24

## 维护说明

### 更新脚本
当项目配置或依赖发生变化时，可能需要更新脚本：
1. 检查Flutter版本兼容性
2. 更新依赖检查逻辑
3. 调整网关服务配置
4. 验证Windows桌面支持

### 测试建议
在部署新脚本前，建议测试：
1. 全新环境下的首次运行
2. 网关服务启动和停止场景
3. 不同Windows版本的兼容性
4. Flutter版本升级后的兼容性