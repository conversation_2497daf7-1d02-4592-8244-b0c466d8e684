import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';

import '../config/app_constants.dart';
import '../services/storage_service.dart';
import '../../features/auth/domain/entities/user.dart';
import '../../features/auth/domain/repositories/auth_repository.dart';
import '../repositories/auth_repository.dart' as core_auth;

/// 认证状态
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// 认证提供者
class AuthProvider extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  final StorageService _storageService;
  
  User? _currentUser;
  String? _errorMessage;
  
  AuthProvider(this._authRepository, this._storageService) : super(AuthState.initial) {
    _initializeAuth();
  }
  
  /// 获取当前用户
  User? get currentUser => _currentUser;
  
  /// 获取错误消息
  String? get errorMessage => _errorMessage;
  
  /// 是否已认证
  bool get isAuthenticated => state == AuthState.authenticated && _currentUser != null;
  
  /// 是否正在加载
  bool get isLoading => state == AuthState.loading;
  
  /// 初始化认证状态
  Future<void> _initializeAuth() async {
    try {
      state = AuthState.loading;
      
      // 从存储中获取用户信息
      final userJson = await _storageService.getMap(AppConstants.userInfoKey);
      final token = await _storageService.getString(AppConstants.userTokenKey);
      
      if (userJson != null && token != null) {
        _currentUser = User.fromJson(userJson);
        
        // 验证token是否有效
        final isValid = await _authRepository.validateToken(token);
        if (isValid) {
          state = AuthState.authenticated;
        } else {
          await _clearUserData();
          state = AuthState.unauthenticated;
        }
      } else {
        state = AuthState.unauthenticated;
      }
    } catch (e) {
      // 错误信息通过state管理
      state = AuthState.error;
      debugPrint('Error initializing auth: $e');
    }
  }
  
  /// 登录
  Future<bool> login(String username, String password) async {
    try {
      state = AuthState.loading;
      
      final user = await _authRepository.login(username, password);
      
      if (user != null) {
        _currentUser = user;
        
        // 保存用户信息和token
        await _storageService.setMap(AppConstants.userInfoKey, _currentUser!.toJson());
        await _storageService.setString(AppConstants.userTokenKey, user.token);
        
        state = AuthState.authenticated;
        return true;
      } else {
        // 错误信息通过state管理
        state = AuthState.unauthenticated;
        return false;
      }
    } catch (e) {
      // 错误信息通过state管理
      state = AuthState.error;
      debugPrint('Error during login: $e');
      return false;
    }
  }
  
  /// 注册
  Future<bool> register(String username, String email, String password) async {
    try {
      state = AuthState.loading;
      
      final user = await _authRepository.register(username, email, password);
      
      if (user != null) {
        _currentUser = user;
        
        // 保存用户信息和token
        await _storageService.setMap(AppConstants.userInfoKey, _currentUser!.toJson());
        await _storageService.setString(AppConstants.userTokenKey, user.token);
        
        state = AuthState.authenticated;
        return true;
      } else {
        // 错误信息通过state管理
        state = AuthState.unauthenticated;
        return false;
      }
    } catch (e) {
        // 错误信息通过state管理
        state = AuthState.error;
        debugPrint('Error during registration: $e');
        return false;
      }
  }
  
  /// 登出
  Future<void> logout() async {
    try {
      state = AuthState.loading;
      
      // 调用后端登出接口
      if (_currentUser != null) {
        await _authRepository.logout(_currentUser!.token);
      }
      
      // 清除本地数据
      await _clearUserData();
      
      state = AuthState.unauthenticated;
    } catch (e) {
      debugPrint('Error during logout: $e');
      // 即使后端登出失败，也要清除本地数据
      await _clearUserData();
      state = AuthState.unauthenticated;
    }
  }
  
  /// 检查认证状态
  Future<void> checkAuthStatus() async {
    try {
      state = AuthState.loading;
      
      final token = await _storageService.getString(AppConstants.userTokenKey);
      if (token == null) {
        state = AuthState.unauthenticated;
        return;
      }
      
      // 验证token有效性
      final isValid = await _authRepository.validateToken(token);
      if (isValid) {
        // 加载用户信息
        final userJson = await _storageService.getMap(AppConstants.userInfoKey);
        if (userJson != null) {
          _currentUser = User.fromJson(userJson);
        }
        state = AuthState.authenticated;
      } else {
        // Token无效，尝试刷新
        await refreshToken();
      }
    } catch (e) {
      debugPrint('Auth status check failed: $e');
      state = AuthState.unauthenticated;
    }
  }
  
  /// 刷新token
  Future<bool> refreshToken() async {
    try {
      final currentToken = await _storageService.getString(AppConstants.userTokenKey);
      if (currentToken == null) return false;
      
      final newToken = await _authRepository.refreshToken(currentToken);
      
      if (newToken != null) {
        await _storageService.setString(AppConstants.userTokenKey, newToken);
        
        // 更新当前用户的token
        if (_currentUser != null) {
          _currentUser = _currentUser!.copyWith(token: newToken);
          await _storageService.setMap(AppConstants.userInfoKey, _currentUser!.toJson());
        }
        
        return true;
      } else {
        await _clearUserData();
        state = AuthState.unauthenticated;
        return false;
      }
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      await _clearUserData();
      state = AuthState.unauthenticated;
      return false;
    }
  }
  
  /// 更新用户信息
  Future<bool> updateUserInfo(Map<String, dynamic> updates) async {
    try {
      if (_currentUser == null) return false;
      
      state = AuthState.loading;
      
      final result = await _authRepository.updateUserInfo(_currentUser!.id, updates);
      
      if (result['success'] == true) {
        // 从result['user']获取更新的数据，但需要保持当前用户的其他信息
        final userData = result['user'] as Map<String, dynamic>?;
        if (userData != null) {
          _currentUser = User.fromJson(userData);
        }
        await _storageService.setMap(AppConstants.userInfoKey, _currentUser!.toJson());
        
        state = AuthState.authenticated;
        return true;
      } else {
        // 错误信息通过state管理
        state = AuthState.authenticated; // 保持认证状态
        return false;
      }
    } catch (e) {
      // 错误信息通过state管理
      state = AuthState.authenticated; // 保持认证状态
      debugPrint('Error updating user info: $e');
      return false;
    }
  }
  
  /// 修改密码
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      if (_currentUser == null) return false;
      
      state = AuthState.loading;
      
      final token = await _storageService.getString(AppConstants.userTokenKey);
      if (token == null) {
        state = AuthState.unauthenticated;
        return false;
      }
      
      final result = await _authRepository.changePassword(
        token: token,
        oldPassword: oldPassword,
        newPassword: newPassword,
      );
      
      state = AuthState.authenticated;
      return result;
    } catch (e) {
      // 错误信息通过state管理
      state = AuthState.authenticated; // 保持认证状态
      debugPrint('Error changing password: $e');
      return false;
    }
  }
  
  /// 清除用户数据
  Future<void> _clearUserData() async {
    _currentUser = null;
    await _storageService.remove(AppConstants.userInfoKey);
    await _storageService.remove(AppConstants.userTokenKey);
  }
  
  /// 清除错误消息
  void clearError() {
    // 错误信息通过state管理
  }
}

/// 认证提供者实例
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepositoryImpl(baseUrl: 'http://localhost:3000');
});

/// Core认证仓库提供者
final coreAuthRepositoryProvider = Provider<core_auth.AuthRepository>((ref) {
  return core_auth.AuthRepository();
});

final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService.instance;
});

final authProvider = StateNotifierProvider<AuthProvider, AuthState>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  final storageService = ref.watch(storageServiceProvider);
  return AuthProvider(authRepository, storageService);
});

/// 当前用户提供者
final currentUserProvider = Provider<User?>((ref) {
  final authNotifier = ref.watch(authProvider.notifier);
  return authNotifier.currentUser;
});

/// 认证状态提供者
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authNotifier = ref.watch(authProvider.notifier);
  return authNotifier.isAuthenticated;
});