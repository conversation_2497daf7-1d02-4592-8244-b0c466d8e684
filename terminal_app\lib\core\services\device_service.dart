import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/device_model.dart' show Device, DeviceCapabilities, DeviceType, DeviceStatus;
import '../models/device_info.dart' show DeviceInfo;
import '../config/app_config.dart';
import 'logger_service.dart';

/// 设备服务
class DeviceService {
  static DeviceService? _instance;
  static DeviceService get instance => _instance ??= DeviceService._();
  
  DeviceService._();

  DeviceInfo? _deviceInfo;
  Timer? _heartbeatTimer;
  bool _isInitialized = false;

  /// 设备信息
  DeviceInfo? get deviceInfo => _deviceInfo;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化设备服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      LoggerService.info('初始化设备服务...');
      
      // 加载或创建设备信息
      await _loadOrCreateDeviceInfo();
      
      _isInitialized = true;
      LoggerService.info('设备服务初始化完成');
    } catch (e, stackTrace) {
      LoggerService.error('设备服务初始化失败: $e', stackTrace);
      rethrow;
    }
  }

  /// 加载或创建设备信息
  Future<void> _loadOrCreateDeviceInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final deviceInfoJson = prefs.getString('device_info');
      
      if (deviceInfoJson != null) {
        // 加载已保存的设备信息
        final deviceData = json.decode(deviceInfoJson);
        _deviceInfo = DeviceInfo.fromJson(deviceData);
        LoggerService.info('加载已保存的设备信息: ${_deviceInfo!.id}');
      } else {
        // 创建新的设备信息
        await _createNewDeviceInfo();
      }
      
      // 更新设备状态
      _deviceInfo = _deviceInfo!.copyWith(
        updatedAt: DateTime.now(),
        status: DeviceStatus.online,
      );
      
      // 保存设备信息
      await _saveDeviceInfo();
    } catch (e) {
      LoggerService.error('加载设备信息失败: $e');
      // 创建默认设备信息
      await _createNewDeviceInfo();
    }
  }

  /// 创建新的设备信息
  Future<void> _createNewDeviceInfo() async {
    try {
      final hostname = Platform.localHostname;
      final platform = AppConstants.platformName;
      final deviceId = AppConstants.deviceId;
      
      final now = DateTime.now();
      _deviceInfo = DeviceInfo(
        id: deviceId,
        name: hostname,
        type: DeviceType.terminal,
        platform: platform,
        platformVersion: Platform.operatingSystemVersion,
        model: Platform.operatingSystem,
        manufacturer: 'Unknown',
        capabilities: const DeviceCapabilities(
          canCapture: true,
          canDisplay: true,
          canRecord: false,
          canStream: false,
          supportedFormats: ['png', 'jpg', 'jpeg', 'bmp', 'gif'],
          maxResolution: '1920x1080',
          hasCamera: false,
          hasMicrophone: false,
          hasStorage: true,
          hasNetwork: true,
        ),
        status: DeviceStatus.offline,
        createdAt: now,
        updatedAt: now,
        metadata: {
          'hostname': hostname,
          'platform': platform,
          'app_version': AppConstants.appVersion,
          'created_at': now.toIso8601String(),
        },
      );
      
      LoggerService.info('创建新设备信息: $deviceId');
    } catch (e) {
      LoggerService.error('创建设备信息失败: $e');
      rethrow;
    }
  }

  /// 获取设备能力
  List<String> _getDeviceCapabilities() {
    final capabilities = <String>[
      'display_image',
      'slideshow',
      'fullscreen',
      'remote_control',
    ];
    
    if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
      capabilities.addAll([
        'system_tray',
        'window_management',
        'file_system_access',
      ]);
    }
    
    return capabilities;
  }

  /// 保存设备信息
  Future<void> _saveDeviceInfo() async {
    if (_deviceInfo == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final deviceInfoJson = json.encode(_deviceInfo!.toJson());
      await prefs.setString('device_info', deviceInfoJson);
      LoggerService.debug('设备信息已保存');
    } catch (e) {
      LoggerService.error('保存设备信息失败: $e');
    }
  }

  /// 更新设备状态
  Future<void> updateStatus(DeviceStatus status) async {
    if (_deviceInfo == null) return;
    
    _deviceInfo = _deviceInfo!.copyWith(
      status: status,
      updatedAt: DateTime.now(),
    );
    
    await _saveDeviceInfo();
    LoggerService.info('设备状态更新为: $status');
  }

  /// 更新设备元数据
  Future<void> updateMetadata(Map<String, dynamic>? metadata) async {
    if (_deviceInfo == null) return;
    
    final newMetadata = Map<String, dynamic>.from(_deviceInfo!.metadata ?? {});
    if (metadata != null) {
      newMetadata.addAll(metadata);
    }
    newMetadata['updated_at'] = DateTime.now().toIso8601String();
    
    _deviceInfo = _deviceInfo!.copyWith(
      metadata: newMetadata,
      updatedAt: DateTime.now(),
    );
    
    await _saveDeviceInfo();
    LoggerService.debug('设备元数据已更新');
  }

  /// 开始心跳
  void startHeartbeat() {
    _heartbeatTimer?.cancel();
    
    _heartbeatTimer = Timer.periodic(
      AppConstants.heartbeatInterval,
      (_) => _sendHeartbeat(),
    );
    
    LoggerService.info('设备心跳已启动');
  }

  /// 停止心跳
  void stopHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
    LoggerService.info('设备心跳已停止');
  }

  /// 发送心跳
  Future<void> _sendHeartbeat() async {
    if (_deviceInfo == null) return;
    
    try {
      // 更新最后活跃时间
      _deviceInfo = _deviceInfo!.copyWith(
        updatedAt: DateTime.now(),
      );
      
      // 这里可以添加向网关发送心跳的逻辑
      LoggerService.debug('发送设备心跳');
    } catch (e) {
      LoggerService.error('发送心跳失败: $e');
    }
  }

  /// 获取设备统计信息
  Map<String, dynamic> getDeviceStats() {
    if (_deviceInfo == null) return {};
    
    return {
      'device_id': _deviceInfo!.id,
      'device_name': _deviceInfo!.name,
      'status': _deviceInfo!.status.toString(),
      'is_online': _deviceInfo!.status == DeviceStatus.online,
      'last_seen': _deviceInfo!.updatedAt.toIso8601String(),
      'uptime': DateTime.now().difference(_deviceInfo!.updatedAt).inSeconds,
      'capabilities': _deviceInfo!.capabilities,
      'platform': _deviceInfo!.platform,
      'model': _deviceInfo!.model,
    };
  }

  /// 重置设备信息
  Future<void> resetDevice() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('device_info');
      
      _deviceInfo = null;
      _isInitialized = false;
      
      stopHeartbeat();
      
      LoggerService.info('设备信息已重置');
    } catch (e) {
      LoggerService.error('重置设备失败: $e');
    }
  }

  /// 测试连接
  Future<bool> testConnection() async {
    try {
      LoggerService.info('开始测试设备连接...');
      
      // 模拟连接测试
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 这里可以添加实际的连接测试逻辑
      // 例如：ping网关服务器、检查网络连接等
      
      LoggerService.info('设备连接测试成功');
      return true;
    } catch (e) {
      LoggerService.error('设备连接测试失败: $e');
      return false;
    }
  }

  /// 释放资源
  void dispose() {
    stopHeartbeat();
    _deviceInfo = null;
    _isInitialized = false;
    LoggerService.info('设备服务已释放');
  }
}