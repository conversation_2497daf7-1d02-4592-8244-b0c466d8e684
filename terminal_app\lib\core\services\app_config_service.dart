import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import 'logger_service.dart';

/// 应用配置服务
class AppConfigService {
  static AppConfigService? _instance;
  static AppConfigService get instance => _instance ??= AppConfigService._();

  AppConfigService._();

  AppConfig? _config;
  bool _isInitialized = false;

  /// 当前配置
  AppConfig? get config => _config;

  /// 是否已初始化
  bool get isInitialized => _isInitialized;

  /// 初始化配置服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      LoggerService.info('初始化应用配置服务...');

      // 加载配置
      await _loadConfig();

      _isInitialized = true;
      LoggerService.info('应用配置服务初始化完成');
    } catch (e, stackTrace) {
      LoggerService.error('应用配置服务初始化失败: $e', stackTrace);
      rethrow;
    }
  }

  /// 获取配置
  Future<AppConfig> getConfig() async {
    if (!_isInitialized) {
      await initialize();
    }
    return _config ?? _getDefaultConfig();
  }

  /// 加载配置
  Future<void> _loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = prefs.getString('app_config');

      if (configJson != null) {
        // 加载已保存的配置
        final configData = json.decode(configJson);
        _config = AppConfig.fromJson(configData);
        LoggerService.info('加载已保存的应用配置');
      } else {
        // 使用默认配置
        _config = _getDefaultConfig();
        await _saveConfig();
        LoggerService.info('使用默认应用配置');
      }
    } catch (e) {
      LoggerService.error('加载应用配置失败: $e');
      // 使用默认配置
      _config = _getDefaultConfig();
    }
  }

  /// 获取默认配置
  AppConfig _getDefaultConfig() {
    return const AppConfig(
      deviceName: 'Flutter终端设备',
      serverPort: 8080,
      autoStart: false,
      enableCompression: true,
      fullScreenOnStart: false,
      showControlOverlay: true,
      compressionQuality: 80,
      theme: 'system',
      language: 'zh_CN',
      useCustomImageFolder: false,
      customImageFolderPath: null,
    );
  }

  /// 保存配置
  Future<void> _saveConfig() async {
    if (_config == null) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final configJson = json.encode(_config!.toJson());
      await prefs.setString('app_config', configJson);
      LoggerService.debug('应用配置已保存');
    } catch (e) {
      LoggerService.error('保存应用配置失败: $e');
    }
  }

  /// 更新配置
  Future<void> updateConfig(AppConfig newConfig) async {
    _config = newConfig;
    await _saveConfig();
    LoggerService.info('应用配置已更新');
  }

  /// 更新设备名称
  Future<void> updateDeviceName(String deviceName) async {
    if (_config == null) return;

    _config = _config!.copyWith(deviceName: deviceName);
    await _saveConfig();
    LoggerService.info('设备名称已更新: $deviceName');
  }

  /// 更新服务器端口
  Future<void> updateServerPort(int serverPort) async {
    if (_config == null) return;

    _config = _config!.copyWith(serverPort: serverPort);
    await _saveConfig();
    LoggerService.info('服务器端口已更新: $serverPort');
  }

  /// 更新自动启动设置
  Future<void> updateAutoStart(bool autoStart) async {
    if (_config == null) return;

    _config = _config!.copyWith(autoStart: autoStart);
    await _saveConfig();
    LoggerService.info('自动启动设置已更新: $autoStart');
  }

  /// 更新压缩设置
  Future<void> updateCompressionSettings({
    bool? enableCompression,
    int? compressionQuality,
  }) async {
    if (_config == null) return;

    _config = _config!.copyWith(
      enableCompression: enableCompression ?? _config!.enableCompression,
      compressionQuality: compressionQuality ?? _config!.compressionQuality,
    );
    await _saveConfig();
    LoggerService.info('压缩设置已更新');
  }

  /// 更新显示设置
  Future<void> updateDisplaySettings({
    bool? fullScreenOnStart,
    bool? showControlOverlay,
  }) async {
    if (_config == null) return;

    _config = _config!.copyWith(
      fullScreenOnStart: fullScreenOnStart ?? _config!.fullScreenOnStart,
      showControlOverlay: showControlOverlay ?? _config!.showControlOverlay,
    );
    await _saveConfig();
    LoggerService.info('显示设置已更新');
  }

  /// 重置配置
  Future<void> resetConfig() async {
    _config = _getDefaultConfig();
    await _saveConfig();
    LoggerService.info('应用配置已重置为默认值');
  }

  /// 清理资源
  void dispose() {
    LoggerService.info('AppConfigService disposed');
  }
}
