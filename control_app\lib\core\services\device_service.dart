import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/device_model.dart';
import '../models/websocket_message.dart';
import 'websocket_service.dart';
import 'logger_service.dart';
import 'storage_service.dart';

/// 设备管理服务
class DeviceService {
  static DeviceService? _instance;
  static DeviceService get instance => _instance ??= DeviceService._();
  
  DeviceService._();

  final Map<String, Device> _devices = {};
  final Map<String, DeviceGroup> _deviceGroups = {};
  
  // 流控制器
  final _devicesController = StreamController<List<Device>>.broadcast();
  final _deviceGroupsController = StreamController<List<DeviceGroup>>.broadcast();
  final _deviceStatusController = StreamController<Device>.broadcast();
  
  // 公开的流
  Stream<List<Device>> get devicesStream => _devicesController.stream;
  Stream<List<DeviceGroup>> get deviceGroupsStream => _deviceGroupsController.stream;
  Stream<Device> get deviceStatusStream => _deviceStatusController.stream;
  
  Timer? _discoveryTimer;
  Timer? _statusCheckTimer;
  
  bool _isInitialized = false;
  
  /// 初始化设备服务
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    LoggerService.info('Initializing DeviceService');
    
    try {
      // 加载本地存储的设备信息
      await _loadDevicesFromStorage();
      await _loadDeviceGroupsFromStorage();
      
      // 监听WebSocket消息
      _listenToWebSocketMessages();
      
      // 开始定期设备发现
      _startPeriodicDiscovery();
      
      // 开始设备状态检查
      _startStatusCheck();
      
      _isInitialized = true;
      LoggerService.info('DeviceService initialized successfully');
      
    } catch (error) {
      LoggerService.error('Failed to initialize DeviceService', error);
      rethrow;
    }
  }
  
  /// 获取所有设备
  List<Device> get devices => _devices.values.toList();
  
  /// 获取所有设备组
  List<DeviceGroup> get deviceGroups => _deviceGroups.values.toList();
  
  /// 获取在线设备
  List<Device> get onlineDevices {
    return _devices.values.where((device) => device.isOnline).toList();
  }
  
  /// 获取离线设备
  List<Device> get offlineDevices {
    return _devices.values.where((device) => device.isOffline).toList();
  }
  
  /// 根据ID获取设备
  Device? getDevice(String deviceId) {
    return _devices[deviceId];
  }
  
  /// 根据ID获取设备组
  DeviceGroup? getDeviceGroup(String groupId) {
    return _deviceGroups[groupId];
  }
  
  /// 根据组ID获取设备列表
  List<Device> getDevicesByGroup(String groupId) {
    return _devices.values
        .where((device) => device.groupId == groupId)
        .toList();
  }
  
  /// 发现设备
  Future<void> discoverDevices() async {
    LoggerService.info('Starting device discovery');
    
    try {
      // 通过WebSocket请求设备发现
      WebSocketService.instance.requestDeviceDiscovery();
      
      // 同时进行UDP广播发现
      await _performUdpDiscovery();
      
    } catch (error) {
      LoggerService.error('Device discovery failed', error);
    }
  }
  
  /// UDP广播设备发现
  Future<void> _performUdpDiscovery() async {
    try {
      final socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, 0);
      socket.broadcastEnabled = true;
      
      final discoveryMessage = {
        'type': 'device_discovery',
        'action': 'broadcast',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'sender': 'control_app',
      };
      
      final data = utf8.encode(jsonEncode(discoveryMessage));
      socket.send(data, InternetAddress('***************'), 8888);
      
      LoggerService.debug('UDP discovery broadcast sent');
      
      // 监听响应
      socket.listen((RawSocketEvent event) {
        if (event == RawSocketEvent.read) {
          final datagram = socket.receive();
          if (datagram != null) {
            _handleUdpDiscoveryResponse(datagram);
          }
        }
      });
      
      // 5秒后关闭socket
      Timer(const Duration(seconds: 5), () {
        socket.close();
      });
      
    } catch (error) {
      LoggerService.error('UDP discovery failed', error);
    }
  }
  
  /// 处理UDP发现响应
  void _handleUdpDiscoveryResponse(Datagram datagram) {
    try {
      final message = utf8.decode(datagram.data);
      final json = jsonDecode(message);
      
      if (json['type'] == 'device_announcement') {
        final deviceInfo = json['deviceInfo'];
        final device = Device.fromJson(deviceInfo);
        addOrUpdateDevice(device);
        LoggerService.info('Device discovered via UDP: ${device.name}');
      }
    } catch (error) {
      LoggerService.error('Failed to parse UDP discovery response', error);
    }
  }
  
  /// 添加或更新设备
  void addOrUpdateDevice(Device device) {
    final existingDevice = _devices[device.id];
    
    if (existingDevice != null) {
      // 更新现有设备
      final updatedDevice = existingDevice.copyWith(
        name: device.name,
        ipAddress: device.ipAddress,
        platform: device.platform,
        version: device.version,
        capabilities: device.capabilities,
        status: device.status,
        lastSeen: device.lastSeen,
        config: device.config,
        systemInfo: device.systemInfo,
      );
      
      _devices[device.id] = updatedDevice;
      _deviceStatusController.add(updatedDevice);
      
      LoggerService.debug('Device updated: ${device.name}');
    } else {
      // 添加新设备
      _devices[device.id] = device;
      LoggerService.info('New device added: ${device.name}');
    }
    
    // 广播设备列表更新
    _devicesController.add(devices);
    
    // 保存到本地存储
    _saveDevicesToStorage();
  }
  
  /// 移除设备
  void removeDevice(String deviceId) {
    final device = _devices.remove(deviceId);
    if (device != null) {
      LoggerService.info('Device removed: ${device.name}');
      _devicesController.add(devices);
      _saveDevicesToStorage();
    }
  }
  
  /// 更新设备状态
  void updateDeviceStatus(String deviceId, DeviceStatus status) {
    final device = _devices[deviceId];
    if (device != null) {
      final updatedDevice = device.copyWith(
        status: status,
        lastSeen: DateTime.now(),
      );
      
      _devices[deviceId] = updatedDevice;
      _deviceStatusController.add(updatedDevice);
      _devicesController.add(devices);
      
      LoggerService.debug('Device status updated: ${device.name} -> $status');
    }
  }
  
  /// 更新设备配置
  Future<bool> updateDeviceConfig(String deviceId, Map<String, dynamic> config) async {
    final device = _devices[deviceId];
    if (device == null) return false;
    
    try {
      // 发送配置更新消息
      final message = WebSocketMessage(
        type: 'device_config',
        action: 'update',
        payload: {
          'deviceId': deviceId,
          'config': config,
        },
      );
      
      await WebSocketService.instance.sendMessage(message);
      
      // 更新本地设备配置
      final updatedDevice = device.copyWith(
        config: {...device.config, ...config},
      );
        
        _devices[deviceId] = updatedDevice;
        _deviceStatusController.add(updatedDevice);
        _devicesController.add(devices);
        _saveDevicesToStorage();
        
        LoggerService.info('Device config updated: ${device.name}');
      }
      
      return success;
    } catch (error) {
      LoggerService.error('Failed to update device config', error);
      return false;
    }
  }
  
  /// 创建设备组
  Future<DeviceGroup?> createDeviceGroup({
    required String name,
    required String description,
    Map<String, dynamic>? defaultConfig,
  }) async {
    try {
      final groupId = 'group_${DateTime.now().millisecondsSinceEpoch}';
      
      final group = DeviceGroup(
        id: groupId,
        name: name,
        description: description,
        defaultConfig: defaultConfig ?? {},
        createdAt: DateTime.now(),
      );
      
      _deviceGroups[groupId] = group;
      _deviceGroupsController.add(deviceGroups);
      _saveDeviceGroupsToStorage();
      
      LoggerService.info('Device group created: $name');
      return group;
      
    } catch (error) {
      LoggerService.error('Failed to create device group', error);
      return null;
    }
  }
  
  /// 删除设备组
  Future<bool> deleteDeviceGroup(String groupId) async {
    try {
      final group = _deviceGroups.remove(groupId);
      if (group != null) {
        // 将组内设备移到默认组
        final devicesInGroup = getDevicesByGroup(groupId);
        for (final device in devicesInGroup) {
          final updatedDevice = device.copyWith(groupId: null);
          _devices[device.id] = updatedDevice;
        }
        
        _deviceGroupsController.add(deviceGroups);
        _devicesController.add(devices);
        _saveDeviceGroupsToStorage();
        _saveDevicesToStorage();
        
        LoggerService.info('Device group deleted: ${group.name}');
        return true;
      }
      return false;
    } catch (error) {
      LoggerService.error('Failed to delete device group', error);
      return false;
    }
  }
  
  /// 将设备添加到组
  Future<bool> addDeviceToGroup(String deviceId, String groupId) async {
    final device = _devices[deviceId];
    final group = _deviceGroups[groupId];
    
    if (device == null || group == null) return false;
    
    try {
      final updatedDevice = device.copyWith(groupId: groupId);
      _devices[deviceId] = updatedDevice;
      
      _deviceStatusController.add(updatedDevice);
      _devicesController.add(devices);
      _saveDevicesToStorage();
      
      LoggerService.info('Device ${device.name} added to group ${group.name}');
      return true;
    } catch (error) {
      LoggerService.error('Failed to add device to group', error);
      return false;
    }
  }
  
  /// 从组中移除设备
  Future<bool> removeDeviceFromGroup(String deviceId) async {
    final device = _devices[deviceId];
    if (device == null) return false;
    
    try {
      final updatedDevice = device.copyWith(groupId: null);
      _devices[deviceId] = updatedDevice;
      
      _deviceStatusController.add(updatedDevice);
      _devicesController.add(devices);
      _saveDevicesToStorage();
      
      LoggerService.info('Device ${device.name} removed from group');
      return true;
    } catch (error) {
      LoggerService.error('Failed to remove device from group', error);
      return false;
    }
  }
  
  /// 监听WebSocket消息
  void _listenToWebSocketMessages() {
    WebSocketService.instance.messageStream.listen((message) {
      _handleWebSocketMessage(message);
    });
    
    WebSocketService.instance.deviceListStream.listen((deviceList) {
      for (final device in deviceList) {
        addOrUpdateDevice(device);
      }
    });
  }
  
  /// 处理WebSocket消息
  void _handleWebSocketMessage(WebSocketMessage message) {
    switch (message.type) {
      case 'device_status':
        _handleDeviceStatusMessage(message);
        break;
      case 'device_announcement':
        _handleDeviceAnnouncementMessage(message);
        break;
      case 'device_disconnected':
        _handleDeviceDisconnectedMessage(message);
        break;
    }
  }
  
  /// 处理设备状态消息
  void _handleDeviceStatusMessage(WebSocketMessage message) {
    final deviceId = message.payload['deviceId'] as String?;
    final statusStr = message.payload['status'] as String?;
    
    if (deviceId != null && statusStr != null) {
      final status = Device._parseStatus(statusStr);
      updateDeviceStatus(deviceId, status);
    }
  }
  
  /// 处理设备公告消息
  void _handleDeviceAnnouncementMessage(WebSocketMessage message) {
    final deviceInfo = message.payload['deviceInfo'];
    if (deviceInfo != null) {
      final device = Device.fromJson(deviceInfo);
      addOrUpdateDevice(device);
    }
  }
  
  /// 处理设备断开消息
  void _handleDeviceDisconnectedMessage(WebSocketMessage message) {
    final deviceId = message.payload['deviceId'] as String?;
    if (deviceId != null) {
      updateDeviceStatus(deviceId, DeviceStatus.offline);
    }
  }
  
  /// 开始定期设备发现
  void _startPeriodicDiscovery() {
    _discoveryTimer?.cancel();
    _discoveryTimer = Timer.periodic(const Duration(minutes: 2), (timer) {
      discoverDevices();
    });
  }
  
  /// 开始设备状态检查
  void _startStatusCheck() {
    _statusCheckTimer?.cancel();
    _statusCheckTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkDeviceStatus();
    });
  }
  
  /// 检查设备状态
  void _checkDeviceStatus() {
    final now = DateTime.now();
    bool hasUpdates = false;
    
    for (final device in _devices.values) {
      if (device.isOnline && device.isStale) {
        final updatedDevice = device.copyWith(status: DeviceStatus.offline);
        _devices[device.id] = updatedDevice;
        _deviceStatusController.add(updatedDevice);
        hasUpdates = true;
        
        LoggerService.warning('Device marked as offline due to inactivity: ${device.name}');
      }
    }
    
    if (hasUpdates) {
      _devicesController.add(devices);
    }
  }
  
  /// 从本地存储加载设备
  Future<void> _loadDevicesFromStorage() async {
    try {
      final devicesJson = await StorageService.instance.getString('devices');
      if (devicesJson != null) {
        final devicesList = jsonDecode(devicesJson) as List;
        for (final deviceJson in devicesList) {
          final device = Device.fromJson(deviceJson);
          _devices[device.id] = device;
        }
        LoggerService.info('Loaded ${_devices.length} devices from storage');
      }
    } catch (error) {
      LoggerService.error('Failed to load devices from storage', error);
    }
  }
  
  /// 保存设备到本地存储
  Future<void> _saveDevicesToStorage() async {
    try {
      final devicesList = _devices.values.map((device) => device.toJson()).toList();
      final devicesJson = jsonEncode(devicesList);
      await StorageService.instance.setString('devices', devicesJson);
    } catch (error) {
      LoggerService.error('Failed to save devices to storage', error);
    }
  }
  
  /// 从本地存储加载设备组
  Future<void> _loadDeviceGroupsFromStorage() async {
    try {
      final groupsJson = await StorageService.instance.getString('device_groups');
      if (groupsJson != null) {
        final groupsList = jsonDecode(groupsJson) as List;
        for (final groupJson in groupsList) {
          final group = DeviceGroup.fromJson(groupJson);
          _deviceGroups[group.id] = group;
        }
        LoggerService.info('Loaded ${_deviceGroups.length} device groups from storage');
      }
    } catch (error) {
      LoggerService.error('Failed to load device groups from storage', error);
    }
  }
  
  /// 保存设备组到本地存储
  Future<void> _saveDeviceGroupsToStorage() async {
    try {
      final groupsList = _deviceGroups.values.map((group) => group.toJson()).toList();
      final groupsJson = jsonEncode(groupsList);
      await StorageService.instance.setString('device_groups', groupsJson);
    } catch (error) {
      LoggerService.error('Failed to save device groups to storage', error);
    }
  }
  
  /// 释放资源
  void dispose() {
    _discoveryTimer?.cancel();
    _statusCheckTimer?.cancel();
    _devicesController.close();
    _deviceGroupsController.close();
    _deviceStatusController.close();
  }
}

/// 设备服务的Riverpod Provider
final deviceServiceProvider = Provider<DeviceService>((ref) {
  return DeviceService.instance;
});

/// 设备列表的Riverpod Provider
final devicesProvider = StreamProvider<List<Device>>((ref) {
  final service = ref.watch(deviceServiceProvider);
  return service.devicesStream;
});

/// 设备组列表的Riverpod Provider
final deviceGroupsProvider = StreamProvider<List<DeviceGroup>>((ref) {
  final service = ref.watch(deviceServiceProvider);
  return service.deviceGroupsStream;
});

/// 在线设备列表的Riverpod Provider
final onlineDevicesProvider = Provider<List<Device>>((ref) {
  final devicesAsync = ref.watch(devicesProvider);
  return devicesAsync.when(
    data: (devices) => devices.where((device) => device.isOnline).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

/// 设备状态更新的Riverpod Provider
final deviceStatusProvider = StreamProvider<Device>((ref) {
  final service = ref.watch(deviceServiceProvider);
  return service.deviceStatusStream;
});