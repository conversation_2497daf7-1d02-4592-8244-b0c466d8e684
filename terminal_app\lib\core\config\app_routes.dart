import 'package:flutter/material.dart';
import '../../features/display/display_screen.dart';
import '../../features/settings/presentation/pages/settings_page.dart';
import '../../features/splash/presentation/pages/splash_page.dart';

/// 应用路由配置
class AppRoutes {
  // 路由名称常量
  static const String splash = '/';
  static const String display = '/display';
  static const String settings = '/settings';

  /// 生成路由
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.splash:
        return MaterialPageRoute(
          builder: (_) => const SplashPage(),
          settings: settings,
        );

      case AppRoutes.display:
        return MaterialPageRoute(
          builder: (_) => const DisplayScreen(),
          settings: settings,
        );
      
      case AppRoutes.settings:
        return MaterialPageRoute(
          builder: (_) => const SettingsPage(),
          settings: settings,
        );
      
      default:
        return MaterialPageRoute(
          builder: (_) => const Scaffold(
            body: Center(
              child: Text(
                '页面未找到',
                style: TextStyle(fontSize: 18),
              ),
            ),
          ),
          settings: settings,
        );
    }
  }

  /// 导航到显示页面
  static void toDisplay(BuildContext context) {
    Navigator.of(context).pushReplacementNamed(display);
  }

  /// 导航到设置页面
  static void toSettings(BuildContext context) {
    Navigator.of(context).pushNamed(settings);
  }

  /// 返回上一页
  static void goBack(BuildContext context) {
    Navigator.of(context).pop();
  }

  /// 清空路由栈并导航到指定页面
  static void pushAndClearStack(BuildContext context, String routeName) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      routeName,
      (route) => false,
    );
  }
}