# 🖼️ Flutter图片切换系统

一个基于Flutter和Node.js的分布式图片切换控制系统，采用统一网关架构，支持多终端设备管理和远程控制。

## 🎉 v2.0.0 重大更新
- ✨ **Material 3界面**: 全新现代化UI设计
- 🔧 **兼容性修复**: 解决GetWidget版本问题
- 🚀 **性能优化**: 使用Flutter原生组件
- 🎨 **统一主题**: 完整的设计系统

## 🏗️ 系统架构

### 统一网关架构 v2.0.0

```
                    端口 7777
┌─────────────────────────────────────────┐
│           统一网关服务                    │
│                                         │
│  📱 /terminal/ws    🎮 /controller/ws   │
│     终端设备路径        控制端路径        │
│                                         │
│  📊 /health         📋 /terminal/status │
│     健康检查           终端状态          │
│                                         │
│  🎯 /controller/status  🏠 /           │
│     控制端状态           服务信息        │
└─────────────────────────────────────────┘
```

## ✨ 主要特性

### 🎨 UI界面 (v2.0.0)
- 🎯 **Material 3设计**: 现代化Flutter原生界面
- 🎨 **统一主题**: AppTheme自定义颜色和样式系统
- 📱 **响应式布局**: 完美适配不同屏幕尺寸
- ✨ **流畅动画**: 原生组件提供的流畅交互体验

### 🌐 网关服务
- 🚀 **统一网关**: 单端口7777提供所有服务
- 🔀 **路由分离**: 通过路径区分终端设备和控制端
- ⚡ **实时通信**: 原生WebSocket高性能通信
- 📱 **设备管理**: 完整的设备注册、发现和管理
- 🎮 **远程控制**: 控制端到终端设备的实时命令传输
- 💓 **心跳保活**: 自动连接保活机制
- 🌐 **HTTP API**: 完整的REST API接口
- 🛡️ **错误处理**: 完善的错误处理和恢复机制

## 📦 项目结构

```
imgCT/
├── gateway_service/                 # 网关服务
│   ├── final_unified_gateway.js    # 主要网关服务 (生产版本)
│   ├── package.json               # 依赖配置
│   ├── config/                    # 配置文件
│   ├── data/                      # 数据文件
│   ├── logs/                      # 日志文件
│   └── uploads/                   # 上传文件
│
├── terminal_app/                   # Flutter终端应用
│   ├── lib/                       # 应用源码
│   ├── pubspec.yaml              # Flutter依赖
│   ├── android/                   # Android平台文件
│   ├── windows/                   # Windows平台文件
│   └── web/                       # Web平台文件
│
├── controller_app/                 # 控制端应用
│   ├── simple_controller.js       # 控制端应用
│   └── package.json              # 依赖配置
│
├── control_app/                    # Flutter控制端应用 (备用)
│   ├── lib/                       # 应用源码
│   └── pubspec.yaml              # Flutter依赖
│
├── api/                           # API服务 (可选)
│   ├── app.ts                     # API应用
│   └── routes/                    # 路由定义
│
├── docs/                          # 📚 文档中心
│   ├── README.md                 # 文档索引和导航
│   ├── START_GUIDE.md            # 详细启动指南
│   ├── UNIFIED_GATEWAY_GUIDE.md  # 统一网关架构指南
│   ├── STARTUP_SCRIPTS_GUIDE.md  # 启动脚本使用指南
│   ├── PROJECT_COMPLETION_SUMMARY.md # 项目完成总结
│   └── VERSION_MANAGEMENT.md     # 版本管理文档
│
├── mock_terminal.js               # 模拟终端设备 (测试用)
├── start_system.bat              # 一键启动脚本 (Windows)
├── start_system.sh               # 一键启动脚本 (Linux/macOS)
├── quick_start.bat               # 快速启动脚本 (Windows)
├── stop_system.bat               # 停止服务脚本 (Windows)
└── README.md                     # 项目说明 (本文件)
```

## 🚀 快速开始

### 方法1: 一键启动 (推荐)

**Windows用户:**
```cmd
# 快速启动 (基础模式) - 推荐日常使用
quick_start.bat

# 完整启动 (包含所有选项) - 首次使用或完整测试
start_system.bat
```

**注意**: 脚本已优化编码兼容性，避免中文字符显示问题

**Linux/macOS用户:**
```bash
# 一键启动 (包含所有选项)
./start_system.sh
```

### 方法2: 手动启动

#### 1. 启动统一网关服务

```cmd
cd gateway_service
node final_unified_gateway.js
```

#### 2. 启动Flutter终端应用

```cmd
cd terminal_app
flutter run -d windows
```

#### 3. 启动控制端应用 (可选)

```cmd
cd controller_app
node simple_controller.js
```

### 停止系统

**Windows用户:**
```cmd
stop_system.bat
```

**Linux/macOS用户:**
```bash
# 停止各个服务
pkill -f "final_unified_gateway.js"
pkill -f "flutter run"
pkill -f "simple_controller.js"
```

## 📋 系统要求

### 网关服务
- Node.js 14.0+
- npm 6.0+
- 依赖: ws, cors

### Flutter终端应用
- Flutter 3.0+
- Dart 2.17+
- Windows 10+ (当前支持平台)

### 控制端应用
- Node.js 14.0+
- 依赖: ws, readline

## 🔧 配置说明

### 网关服务配置 (v2.0.0)
- **端口**: 7777 (已更新)
- **终端设备路径**: `/terminal/ws`
- **控制端路径**: `/controller/ws`
- **健康检查**: `/health`

### Flutter应用配置
```dart
// terminal_app/lib/core/config/app_config.dart
static const int defaultGatewayPort = 7777;  // 已更新
static const int defaultGatewayWsPort = 7777; // 已更新
```

### UI主题配置 (v2.0.0新增)
```dart
// terminal_app/lib/core/theme/app_theme.dart
static const Color primaryColor = Color(0xFF2196F3);    // 主色调
static const Color successColor = Color(0xFF4CAF50);    // 成功色
static const Color errorColor = Color(0xFFF44336);      // 错误色
```

## 🧪 测试验证

### HTTP接口测试 (v2.0.0)
```cmd
# 健康检查
curl http://localhost:7777/health

# 服务信息
curl http://localhost:7777/

# 终端设备状态
curl http://localhost:7777/terminal/status

# 控制端状态
curl http://localhost:7777/controller/status
```

### WebSocket连接测试
```cmd
# 运行统一网关测试
node test_unified_gateway.js

# 运行模拟终端设备
node mock_terminal.js
```

## 📚 文档中心

所有详细文档都位于 **[docs/](docs/)** 文件夹中：

- 📖 [启动指南](docs/START_GUIDE.md) - 系统启动和基本使用
- 🏗️ [统一网关指南](docs/UNIFIED_GATEWAY_GUIDE.md) - 网关架构详细说明
- 🚀 [启动脚本指南](docs/STARTUP_SCRIPTS_GUIDE.md) - 一键启动脚本使用方法
- 📋 [项目完成总结](docs/PROJECT_COMPLETION_SUMMARY.md) - 项目成果和特性
- 🔄 [版本管理文档](docs/VERSION_MANAGEMENT.md) - 版本历史和迁移指南

**快速访问**: 查看 [docs/README.md](docs/README.md) 获取完整的文档导航

## 🎯 版本信息

**当前版本**: 统一网关架构 v1.0.0
**状态**: ✅ 生产就绪
**发布日期**: 2025-08-19

### 版本特性
- ✅ 单端口多服务架构
- ✅ 路由分离和服务隔离
- ✅ 原生WebSocket通信
- ✅ 完整设备管理功能
- ✅ 实时命令传输
- ✅ 完善错误处理

## � 更新记录

### v2.0.0 (2025-08-20) - 重大UI改造
- 📖 [详细更新记录](CHANGELOG.md) - 完整的技术变更日志
- 📝 [更新说明](UPDATE_NOTES.md) - 用户友好的更新指南

### 主要变更
- ✨ Material 3界面设计
- 🔧 解决GetWidget兼容性问题
- 🚀 性能和稳定性提升
- 🎨 统一主题系统

## �📞 技术支持

如果遇到问题或需要帮助：

1. 查看 [更新说明](UPDATE_NOTES.md) 了解最新变更
2. 查看 [详细文档](docs/) 获取技术指南
3. 检查系统日志和配置设置
4. 运行测试脚本验证功能

### 常见问题
- **连接问题**: 确保网关服务在端口7777运行
- **界面异常**: 尝试重启应用或清理缓存
- **构建失败**: 运行 `flutter clean` 后重新构建

---

**Flutter图片切换系统 v2.0.0** - 现代化界面，让图片切换变得更简单而强大 🚀
