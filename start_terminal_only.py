#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flutter 系统 - 仅启动终端应用
版本: v1.0.0
前提条件: 网关服务必须已经在运行
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """仅启动Flutter终端应用"""
    project_root = Path(__file__).parent
    terminal_path = project_root / "terminal_app"
    
    print("\n" + "="*60)
    print("   Flutter 系统 - 终端应用启动器")
    print("="*60)
    print("   前提: 网关服务必须已在运行 (端口7777)")
    print("="*60)
    
    # 检查项目结构
    print("\n[1/3] 检查项目结构...")
    if not terminal_path.exists():
        print("❌ terminal_app 目录不存在")
        return False
    
    pubspec = terminal_path / "pubspec.yaml"
    if not pubspec.exists():
        print("❌ pubspec.yaml 文件不存在")
        return False
    
    print("✓ 项目结构检查通过")
    
    # 检查网关服务
    print("\n[2/3] 检查网关服务状态...")
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', 7777))
        sock.close()
        
        if result == 0:
            print("✓ 网关服务正在运行 (端口7777)")
        else:
            print("❌ 网关服务未运行，请先启动网关服务")
            print("💡 运行: python start_gateway_only.py")
            return False
    except:
        print("⚠ 无法检查网关服务状态，继续执行...")
    
    # 启动Flutter应用
    print("\n[3/3] 启动Flutter终端应用...")
    try:
        print("📱 准备启动Flutter应用...")
        print("🔗 连接网关: ws://localhost:7777/terminal/ws")
        print("\n💡 提示: 按 Ctrl+C 退出应用")
        print("-" * 60)
        
        # 尝试使用不同的Flutter命令路径
        flutter_commands = [
            'flutter',
            'flutter.bat',
            os.path.expandvars('%USERPROFILE%\\AppData\\Local\\Pub\\Cache\\bin\\flutter.bat'),
            'C:\\flutter\\bin\\flutter.bat',
            'C:\\tools\\flutter\\bin\\flutter.bat'
        ]
        
        flutter_cmd = None
        for cmd in flutter_commands:
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, timeout=5)
                if result.returncode == 0:
                    flutter_cmd = cmd
                    print(f"✓ 找到Flutter命令: {cmd}")
                    break
            except:
                continue
        
        if not flutter_cmd:
            print("❌ 未找到Flutter命令")
            print("\n手动启动方法:")
            print("1. 打开命令提示符")
            print(f"2. cd {terminal_path}")
            print("3. flutter clean")
            print("4. flutter pub get")
            print("5. flutter run -d windows")
            return False
        
        # 启动应用
        subprocess.run([flutter_cmd, 'run', '-d', 'windows'], 
                      cwd=terminal_path)
        
    except KeyboardInterrupt:
        print("\n\n⚠ 用户退出应用")
    except Exception as e:
        print(f"\n❌ 应用启动错误: {e}")
        print("\n手动启动方法:")
        print("1. 打开命令提示符")
        print(f"2. cd {terminal_path}")
        print("3. flutter run -d windows")
        return False
    
    print("\n✓ Flutter应用已退出")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 终端应用脚本执行完成！")
        else:
            print("\n💥 终端应用启动失败")
    except KeyboardInterrupt:
        print("\n\n⚠ 用户中断")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
    
    input("\n按 Enter 退出...")