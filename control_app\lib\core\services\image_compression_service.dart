import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image/image.dart' as img;
import 'package:path/path.dart' as path;
import 'package:crypto/crypto.dart';

import 'logger_service.dart';
import 'storage_service.dart';

/// 压缩质量级别
enum CompressionQuality {
  /// 低质量 (高压缩)
  low(30),

  /// 中等质量
  medium(60),

  /// 高质量 (低压缩)
  high(85),

  /// 原始质量 (无压缩)
  original(100);

  const CompressionQuality(this.value);
  final int value;
}

/// 压缩配置
class CompressionConfig {
  final CompressionQuality quality;
  final int? maxWidth;
  final int? maxHeight;
  final bool maintainAspectRatio;
  final String format; // 'jpeg', 'png', 'webp'
  final bool enableProgressive;

  const CompressionConfig({
    this.quality = CompressionQuality.medium,
    this.maxWidth,
    this.maxHeight,
    this.maintainAspectRatio = true,
    this.format = 'jpeg',
    this.enableProgressive = true,
  });

  factory CompressionConfig.fromJson(Map<String, dynamic> json) {
    return CompressionConfig(
      quality: CompressionQuality.values.firstWhere(
        (q) => q.name == json['quality'],
        orElse: () => CompressionQuality.medium,
      ),
      maxWidth: json['maxWidth'] as int?,
      maxHeight: json['maxHeight'] as int?,
      maintainAspectRatio: json['maintainAspectRatio'] as bool? ?? true,
      format: json['format'] as String? ?? 'jpeg',
      enableProgressive: json['enableProgressive'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'quality': quality.name,
      'maxWidth': maxWidth,
      'maxHeight': maxHeight,
      'maintainAspectRatio': maintainAspectRatio,
      'format': format,
      'enableProgressive': enableProgressive,
    };
  }
}

/// 压缩结果
class CompressionResult {
  final Uint8List data;
  final int originalSize;
  final int compressedSize;
  final double compressionRatio;
  final int width;
  final int height;
  final String format;
  final String hash;

  const CompressionResult({
    required this.data,
    required this.originalSize,
    required this.compressedSize,
    required this.compressionRatio,
    required this.width,
    required this.height,
    required this.format,
    required this.hash,
  });

  /// 压缩率百分比
  double get compressionPercentage => (1 - compressionRatio) * 100;

  /// 节省的字节数
  int get savedBytes => originalSize - compressedSize;
}

/// 图片压缩服务
class ImageCompressionService {
  static ImageCompressionService? _instance;
  static ImageCompressionService get instance =>
      _instance ??= ImageCompressionService._();

  ImageCompressionService._();

  final Map<String, CompressionResult> _compressionCache = {};
  CompressionConfig _defaultConfig = const CompressionConfig();

  /// 初始化压缩服务
  Future<void> initialize() async {
    try {
      // 加载默认配置
      await _loadDefaultConfig();

      LoggerService.info('Image compression service initialized');
    } catch (error) {
      LoggerService.error(
          'Failed to initialize image compression service', error);
      rethrow;
    }
  }

  /// 压缩图片文件
  Future<CompressionResult> compressFile(
    String filePath, {
    CompressionConfig? config,
    bool useCache = true,
  }) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw FileSystemException('File not found', filePath);
      }

      final originalData = await file.readAsBytes();
      return await compressBytes(
        originalData,
        config: config,
        useCache: useCache,
        fileName: path.basename(filePath),
      );
    } catch (error) {
      LoggerService.error('Failed to compress file: $filePath', error);
      rethrow;
    }
  }

  /// 压缩图片字节数据
  Future<CompressionResult> compressBytes(
    Uint8List originalData, {
    CompressionConfig? config,
    bool useCache = true,
    String? fileName,
  }) async {
    try {
      final compressionConfig = config ?? _defaultConfig;

      // 生成缓存键
      final cacheKey = _generateCacheKey(originalData, compressionConfig);

      // 检查缓存
      if (useCache && _compressionCache.containsKey(cacheKey)) {
        LoggerService.debug('Using cached compression result: $cacheKey');
        return _compressionCache[cacheKey]!;
      }

      // 解码原始图片
      final originalImage = img.decodeImage(originalData);
      if (originalImage == null) {
        throw const FormatException('Unable to decode image data');
      }

      LoggerService.info(
          'Compressing image: ${originalImage.width}x${originalImage.height}, '
          'original size: ${originalData.length} bytes');

      // 调整图片尺寸
      final resizedImage = _resizeImage(originalImage, compressionConfig);

      // 压缩图片
      final compressedData = _compressImage(resizedImage, compressionConfig);

      // 计算压缩结果
      final result = CompressionResult(
        data: compressedData,
        originalSize: originalData.length,
        compressedSize: compressedData.length,
        compressionRatio: compressedData.length / originalData.length,
        width: resizedImage.width,
        height: resizedImage.height,
        format: compressionConfig.format,
        hash: _calculateHash(compressedData),
      );

      // 缓存结果
      if (useCache) {
        _compressionCache[cacheKey] = result;
      }

      LoggerService.info('Image compression completed: '
          '${result.originalSize} -> ${result.compressedSize} bytes '
          '(${result.compressionPercentage.toStringAsFixed(1)}% reduction)');

      return result;
    } catch (error) {
      LoggerService.error('Failed to compress image bytes', error);
      rethrow;
    }
  }

  /// 批量压缩图片
  Future<List<CompressionResult>> compressMultiple(
    List<String> filePaths, {
    CompressionConfig? config,
    bool useCache = true,
    void Function(int completed, int total)? onProgress,
  }) async {
    final results = <CompressionResult>[];

    for (int i = 0; i < filePaths.length; i++) {
      try {
        final result = await compressFile(
          filePaths[i],
          config: config,
          useCache: useCache,
        );
        results.add(result);

        onProgress?.call(i + 1, filePaths.length);
      } catch (error) {
        LoggerService.error('Failed to compress file: ${filePaths[i]}', error);
        // 继续处理其他文件
      }
    }

    return results;
  }

  /// 压缩并保存图片
  Future<String> compressAndSave(
    String inputPath,
    String outputPath, {
    CompressionConfig? config,
    bool overwrite = false,
  }) async {
    try {
      final outputFile = File(outputPath);

      // 检查输出文件是否存在
      if (!overwrite && await outputFile.exists()) {
        throw FileSystemException('Output file already exists', outputPath);
      }

      // 压缩图片
      final result = await compressFile(inputPath, config: config);

      // 确保输出目录存在
      final outputDir = Directory(path.dirname(outputPath));
      if (!await outputDir.exists()) {
        await outputDir.create(recursive: true);
      }

      // 保存压缩后的图片
      await outputFile.writeAsBytes(result.data);

      LoggerService.info('Compressed image saved: $outputPath');
      return outputPath;
    } catch (error) {
      LoggerService.error('Failed to compress and save image', error);
      rethrow;
    }
  }

  /// 调整图片尺寸
  img.Image _resizeImage(img.Image image, CompressionConfig config) {
    if (config.maxWidth == null && config.maxHeight == null) {
      return image;
    }

    int targetWidth = image.width;
    int targetHeight = image.height;

    // 计算目标尺寸
    if (config.maxWidth != null && targetWidth > config.maxWidth!) {
      if (config.maintainAspectRatio) {
        final ratio = config.maxWidth! / targetWidth;
        targetWidth = config.maxWidth!;
        targetHeight = (targetHeight * ratio).round();
      } else {
        targetWidth = config.maxWidth!;
      }
    }

    if (config.maxHeight != null && targetHeight > config.maxHeight!) {
      if (config.maintainAspectRatio) {
        final ratio = config.maxHeight! / targetHeight;
        targetHeight = config.maxHeight!;
        targetWidth = (targetWidth * ratio).round();
      } else {
        targetHeight = config.maxHeight!;
      }
    }

    // 如果尺寸没有变化，返回原图
    if (targetWidth == image.width && targetHeight == image.height) {
      return image;
    }

    // 调整尺寸
    return img.copyResize(
      image,
      width: targetWidth,
      height: targetHeight,
      interpolation: img.Interpolation.cubic,
    );
  }

  /// 压缩图片
  Uint8List _compressImage(img.Image image, CompressionConfig config) {
    switch (config.format.toLowerCase()) {
      case 'jpeg':
      case 'jpg':
        return Uint8List.fromList(
          img.encodeJpg(
            image,
            quality: config.quality.value,
          ),
        );

      case 'png':
        return Uint8List.fromList(
          img.encodePng(
            image,
            level: _getPngCompressionLevel(config.quality),
          ),
        );

      case 'webp':
        // WebP编码暂不支持，使用JPEG替代
        return Uint8List.fromList(
          img.encodeJpg(
            image,
            quality: config.quality.value,
          ),
        );

      default:
        throw ArgumentError('Unsupported format: ${config.format}');
    }
  }

  /// 获取PNG压缩级别
  int _getPngCompressionLevel(CompressionQuality quality) {
    switch (quality) {
      case CompressionQuality.low:
        return 9; // 最高压缩
      case CompressionQuality.medium:
        return 6;
      case CompressionQuality.high:
        return 3;
      case CompressionQuality.original:
        return 0; // 无压缩
    }
  }

  /// 生成缓存键
  String _generateCacheKey(Uint8List data, CompressionConfig config) {
    final dataHash = sha256.convert(data).toString();
    final configHash =
        sha256.convert(config.toJson().toString().codeUnits).toString();
    return '${dataHash}_$configHash';
  }

  /// 计算数据哈希
  String _calculateHash(Uint8List data) {
    return sha256.convert(data).toString();
  }

  /// 加载默认配置
  Future<void> _loadDefaultConfig() async {
    try {
      final configJson =
          await StorageService.instance.getString('compression_config');
      if (configJson != null) {
        final configMap = Map<String, dynamic>.from(
          StorageService.decodeJson(configJson),
        );
        _defaultConfig = CompressionConfig.fromJson(configMap);
      }
    } catch (error) {
      LoggerService.warning(
          'Failed to load compression config, using defaults', error);
    }
  }

  /// 保存默认配置
  Future<void> saveDefaultConfig(CompressionConfig config) async {
    try {
      _defaultConfig = config;
      await StorageService.instance.setString(
        'compression_config',
        StorageService.encodeJson(config.toJson()),
      );

      LoggerService.info('Compression config saved');
    } catch (error) {
      LoggerService.error('Failed to save compression config', error);
    }
  }

  /// 获取默认配置
  CompressionConfig get defaultConfig => _defaultConfig;

  /// 清除压缩缓存
  void clearCache() {
    _compressionCache.clear();
    LoggerService.info('Compression cache cleared');
  }

  /// 获取缓存统计
  Map<String, dynamic> getCacheStats() {
    return {
      'cachedItems': _compressionCache.length,
      'totalOriginalSize': _compressionCache.values.fold<int>(
        0,
        (sum, result) => sum + result.originalSize,
      ),
      'totalCompressedSize': _compressionCache.values.fold<int>(
        0,
        (sum, result) => sum + result.compressedSize,
      ),
    };
  }

  /// 获取支持的格式
  static List<String> get supportedFormats => ['jpeg', 'jpg', 'png', 'webp'];

  /// 检查格式是否支持
  static bool isSupportedFormat(String format) {
    return supportedFormats.contains(format.toLowerCase());
  }

  /// 获取推荐的压缩配置
  static CompressionConfig getRecommendedConfig({
    required int imageWidth,
    required int imageHeight,
    required int fileSize,
    String purpose = 'display', // 'display', 'thumbnail', 'storage'
  }) {
    switch (purpose) {
      case 'thumbnail':
        return const CompressionConfig(
          quality: CompressionQuality.medium,
          maxWidth: 300,
          maxHeight: 300,
          format: 'jpeg',
        );

      case 'storage':
        return const CompressionConfig(
          quality: CompressionQuality.high,
          maxWidth: 1920,
          maxHeight: 1080,
          format: 'jpeg',
        );

      case 'display':
      default:
        // 根据图片大小动态调整
        if (fileSize > 5 * 1024 * 1024) {
          // 大于5MB
          return const CompressionConfig(
            quality: CompressionQuality.medium,
            maxWidth: 1920,
            maxHeight: 1080,
            format: 'jpeg',
          );
        } else if (fileSize > 1 * 1024 * 1024) {
          // 大于1MB
          return const CompressionConfig(
            quality: CompressionQuality.high,
            maxWidth: 2560,
            maxHeight: 1440,
            format: 'jpeg',
          );
        } else {
          return const CompressionConfig(
            quality: CompressionQuality.high,
            format: 'jpeg',
          );
        }
    }
  }
}

/// 图片压缩服务提供者
final imageCompressionServiceProvider =
    Provider<ImageCompressionService>((ref) {
  return ImageCompressionService.instance;
});

/// 默认压缩配置提供者
final defaultCompressionConfigProvider = Provider<CompressionConfig>((ref) {
  final service = ref.watch(imageCompressionServiceProvider);
  return service.defaultConfig;
});
