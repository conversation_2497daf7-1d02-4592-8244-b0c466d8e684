﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{8ACE0CD1-DAAF-314C-8DC2-475159586B90}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\ba85999c94e6d25b4d94bd2862074b37\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/AI_codeE/imgCT/control_app/build/windows/x64/flutter_imgct_control.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeCXXCompiler.cmake;E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeRCCompiler.cmake;E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeSystem.cmake;E:\AI_codeE\imgCT\control_app\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\generated_config.cmake;E:\AI_codeE\imgCT\control_app\windows\flutter\generated_plugins.cmake;E:\AI_codeE\imgCT\control_app\windows\runner\CMakeLists.txt;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\flutter\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\connectivity_plus\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\file_selector_windows\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\screen_retriever\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\system_tray\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\window_manager\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/AI_codeE/imgCT/control_app/build/windows/x64/flutter_imgct_control.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeCXXCompiler.cmake;E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeRCCompiler.cmake;E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeSystem.cmake;E:\AI_codeE\imgCT\control_app\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\generated_config.cmake;E:\AI_codeE\imgCT\control_app\windows\flutter\generated_plugins.cmake;E:\AI_codeE\imgCT\control_app\windows\runner\CMakeLists.txt;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\flutter\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\connectivity_plus\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\file_selector_windows\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\screen_retriever\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\system_tray\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\window_manager\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file E:/AI_codeE/imgCT/control_app/build/windows/x64/flutter_imgct_control.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeCXXCompiler.cmake;E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeRCCompiler.cmake;E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\3.29.5-msvc4\CMakeSystem.cmake;E:\AI_codeE\imgCT\control_app\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\CMakeLists.txt;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\generated_config.cmake;E:\AI_codeE\imgCT\control_app\windows\flutter\generated_plugins.cmake;E:\AI_codeE\imgCT\control_app\windows\runner\CMakeLists.txt;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCXXInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeCommonLanguageInclude.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeGenericSystem.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeInitializeConfigs.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeLanguageInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeRCInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInformation.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\CMakeSystemSpecificInitialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC-CXX.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Compiler\MSVC.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-Initialize.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows-MSVC.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\Windows.cmake;E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\share\cmake-3.29\Modules\Platform\WindowsPaths.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\flutter\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\connectivity_plus\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\file_selector_windows\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\screen_retriever\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\system_tray\CMakeFiles\generate.stamp;E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\window_manager\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>