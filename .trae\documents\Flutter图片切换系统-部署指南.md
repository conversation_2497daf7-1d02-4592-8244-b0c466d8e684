# Flutter图片切换系统 - 部署指南

## 1. 系统要求

### 1.1 控制端要求
- **操作系统**：Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **内存**：最低4GB，推荐8GB
- **存储空间**：最低2GB可用空间
- **网络**：千兆以太网连接（推荐）
- **显示器**：1920x1080分辨率或更高

### 1.2 终端设备要求
- **操作系统**：Windows 10/11, macOS 10.14+, Ubuntu 18.04+
- **内存**：最低2GB，推荐4GB
- **存储空间**：最低1GB可用空间
- **网络**：稳定的网络连接
- **显示器**：支持目标显示分辨率

### 1.3 网关服务器要求
- **操作系统**：Windows Server 2019+, Ubuntu 18.04+, CentOS 7+
- **内存**：最低4GB，推荐8GB
- **存储空间**：最低10GB可用空间（用于图片存储）
- **网络**：千兆以太网，固定IP地址
- **Node.js**：版本18.0或更高

## 2. 安装包结构

### 2.1 发布包内容
```
imgct_release_v1.0/
├── control_app/
│   ├── imgct_control.exe          # Windows控制端
│   ├── imgct_control.app/          # macOS控制端
│   ├── imgct_control_linux         # Linux控制端
│   └── config/
│       └── app_config.json
├── terminal_app/
│   ├── imgct_terminal.exe          # Windows终端
│   ├── imgct_terminal.app/         # macOS终端
│   ├── imgct_terminal_linux        # Linux终端
│   └── config/
│       └── terminal_config.json
├── gateway_service/
│   ├── imgct_gateway.exe           # Windows服务
│   ├── imgct_gateway_linux         # Linux服务
│   ├── config/
│   │   └── gateway_config.json
│   └── web/
│       └── static/
├── mobile_app/
│   ├── imgct_control.apk           # Android应用
│   └── imgct_control.ipa           # iOS应用
├── scripts/
│   ├── install_windows.bat
│   ├── install_linux.sh
│   ├── start_services.bat
│   └── stop_services.bat
└── docs/
    ├── 部署指南.md
    ├── 用户手册.md
    └── 故障排除.md
```

## 3. 快速部署

### 3.1 Windows环境一键部署

**步骤1：运行安装脚本**
```batch
@echo off
echo 开始部署Flutter图片切换系统...

:: 创建安装目录
mkdir "C:\Program Files\ImgCT" 2>nul
cd /d "C:\Program Files\ImgCT"

:: 复制文件
echo 复制应用程序文件...
xcopy "%~dp0control_app\*" "control\" /E /I /Y
xcopy "%~dp0terminal_app\*" "terminal\" /E /I /Y
xcopy "%~dp0gateway_service\*" "gateway\" /E /I /Y

:: 创建桌面快捷方式
echo 创建桌面快捷方式...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\ImgCT控制台.lnk'); $Shortcut.TargetPath = 'C:\Program Files\ImgCT\control\imgct_control.exe'; $Shortcut.Save()"

:: 配置防火墙
echo 配置防火墙规则...
netsh advfirewall firewall add rule name="ImgCT Gateway" dir=in action=allow protocol=TCP localport=3000
netsh advfirewall firewall add rule name="ImgCT WebSocket" dir=in action=allow protocol=TCP localport=8080
netsh advfirewall firewall add rule name="ImgCT Discovery" dir=in action=allow protocol=UDP localport=8888

:: 启动网关服务
echo 启动网关服务...
start "ImgCT Gateway" /D "C:\Program Files\ImgCT\gateway" imgct_gateway.exe

echo 部署完成！请运行桌面上的"ImgCT控制台"开始使用。
pause
```

### 3.2 Linux环境部署

```bash
#!/bin/bash
# install_linux.sh

echo "开始部署Flutter图片切换系统..."

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo权限运行此脚本"
    exit 1
fi

# 创建安装目录
INSTALL_DIR="/opt/imgct"
mkdir -p $INSTALL_DIR
cd $INSTALL_DIR

# 复制文件
echo "复制应用程序文件..."
cp -r ./control_app/* ./control/
cp -r ./terminal_app/* ./terminal/
cp -r ./gateway_service/* ./gateway/

# 设置执行权限
chmod +x ./control/imgct_control_linux
chmod +x ./terminal/imgct_terminal_linux
chmod +x ./gateway/imgct_gateway_linux

# 创建systemd服务
echo "创建系统服务..."
cat > /etc/systemd/system/imgct-gateway.service << EOF
[Unit]
Description=ImgCT Gateway Service
After=network.target

[Service]
Type=simple
User=imgct
WorkingDirectory=/opt/imgct/gateway
ExecStart=/opt/imgct/gateway/imgct_gateway_linux
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# 创建用户
useradd -r -s /bin/false imgct
chown -R imgct:imgct $INSTALL_DIR

# 配置防火墙
echo "配置防火墙..."
ufw allow 3000/tcp comment "ImgCT Gateway"
ufw allow 8080/tcp comment "ImgCT WebSocket"
ufw allow 8888/udp comment "ImgCT Discovery"

# 启动服务
systemctl daemon-reload
systemctl enable imgct-gateway
systemctl start imgct-gateway

echo "部署完成！服务已启动。"
echo "控制端程序位置：$INSTALL_DIR/control/imgct_control_linux"
echo "终端程序位置：$INSTALL_DIR/terminal/imgct_terminal_linux"
```

## 4. 详细配置

### 4.1 网关服务配置

**gateway_config.json**：
```json
{
  "server": {
    "port": 3000,
    "host": "0.0.0.0",
    "cors": {
      "origin": "*",
      "credentials": true
    }
  },
  "websocket": {
    "port": 8080,
    "pingInterval": 25000,
    "pingTimeout": 5000
  },
  "discovery": {
    "port": 8888,
    "interval": 30000,
    "timeout": 5000
  },
  "storage": {
    "imageDir": "./uploads/images",
    "thumbnailDir": "./uploads/thumbnails",
    "maxFileSize": "50MB",
    "allowedFormats": ["jpg", "jpeg", "png", "bmp"]
  },
  "database": {
    "type": "sqlite",
    "path": "./data/imgct.db",
    "backup": {
      "enabled": true,
      "interval": "24h",
      "keepDays": 7
    }
  },
  "security": {
    "enableAuth": true,
    "jwtSecret": "your-secret-key-here",
    "sessionTimeout": "24h",
    "maxLoginAttempts": 5
  },
  "performance": {
    "maxConcurrentCaptures": 10,
    "imageCompressionQuality": 85,
    "thumbnailSize": 200,
    "cacheSize": "100MB"
  },
  "logging": {
    "level": "info",
    "file": "./logs/gateway.log",
    "maxSize": "10MB",
    "maxFiles": 5
  }
}
```

### 4.2 控制端配置

**app_config.json**：
```json
{
  "gateway": {
    "host": "*************",
    "port": 3000,
    "websocketPort": 8080,
    "timeout": 10000,
    "retryAttempts": 3
  },
  "ui": {
    "theme": "light",
    "language": "zh-CN",
    "autoRefreshInterval": 5000,
    "gridColumns": 4,
    "thumbnailSize": 150
  },
  "capture": {
    "defaultQuality": 90,
    "defaultFormat": "png",
    "autoSave": true,
    "saveDirectory": "./captures"
  },
  "display": {
    "defaultMode": "fullscreen",
    "transitionDuration": 500,
    "autoHideControls": true
  },
  "shortcuts": {
    "captureAll": "Ctrl+Shift+C",
    "showImage": "Ctrl+Shift+S",
    "hideImage": "Ctrl+Shift+H",
    "refreshDevices": "F5"
  }
}
```

### 4.3 终端配置

**terminal_config.json**：
```json
{
  "gateway": {
    "autoDiscover": true,
    "manualHost": "",
    "reconnectInterval": 5000,
    "heartbeatInterval": 30000
  },
  "device": {
    "name": "Terminal-001",
    "description": "会议室显示终端",
    "group": "meeting-room",
    "capabilities": ["capture", "display", "fullscreen"]
  },
  "capture": {
    "quality": 90,
    "format": "png",
    "region": {
      "enabled": false,
      "x": 0,
      "y": 0,
      "width": 1920,
      "height": 1080
    }
  },
  "display": {
    "autoFullscreen": true,
    "hideTaskbar": true,
    "disableScreensaver": true,
    "brightness": 100
  },
  "system": {
    "startWithWindows": true,
    "minimizeToTray": true,
    "enableLogging": true,
    "logLevel": "info"
  }
}
```

## 5. 网络配置

### 5.1 端口配置

| 服务 | 端口 | 协议 | 用途 |
|------|------|------|------|
| 网关HTTP服务 | 3000 | TCP | REST API和文件上传 |
| WebSocket服务 | 8080 | TCP | 实时通信 |
| 设备发现 | 8888 | UDP | 自动设备发现 |
| 文件传输 | 3001 | TCP | 大文件传输（可选） |

### 5.2 防火墙配置

**Windows防火墙**：
```batch
:: 允许入站连接
netsh advfirewall firewall add rule name="ImgCT-HTTP" dir=in action=allow protocol=TCP localport=3000
netsh advfirewall firewall add rule name="ImgCT-WebSocket" dir=in action=allow protocol=TCP localport=8080
netsh advfirewall firewall add rule name="ImgCT-Discovery" dir=in action=allow protocol=UDP localport=8888

:: 允许出站连接
netsh advfirewall firewall add rule name="ImgCT-Client" dir=out action=allow program="C:\Program Files\ImgCT\control\imgct_control.exe"
netsh advfirewall firewall add rule name="ImgCT-Terminal" dir=out action=allow program="C:\Program Files\ImgCT\terminal\imgct_terminal.exe"
```

**Linux iptables**：
```bash
# 允许必要端口
iptables -A INPUT -p tcp --dport 3000 -j ACCEPT
iptables -A INPUT -p tcp --dport 8080 -j ACCEPT
iptables -A INPUT -p udp --dport 8888 -j ACCEPT

# 保存规则
iptables-save > /etc/iptables/rules.v4
```

## 6. 多机部署方案

### 6.1 典型部署架构

```
网络拓扑：

[控制室]
├── 主控制端 (************)
├── 备用控制端 (************)
└── 移动控制端 (WiFi)

[服务器机房]
└── 网关服务器 (*************)
    ├── 主服务 (端口3000, 8080)
    ├── 备份服务 (端口3002, 8082)
    └── 文件存储 (/data/imgct)

[显示终端]
├── 会议室A (*************-205)
├── 会议室B (*************-215)
├── 监控中心 (*************-230)
└── 展厅区域 (*************-240)
```

### 6.2 批量部署脚本

**批量终端部署 (PowerShell)**：
```powershell
# deploy_terminals.ps1
param(
    [string[]]$TargetIPs,
    [string]$Username,
    [string]$Password
)

$SecurePassword = ConvertTo-SecureString $Password -AsPlainText -Force
$Credential = New-Object System.Management.Automation.PSCredential($Username, $SecurePassword)

foreach ($IP in $TargetIPs) {
    Write-Host "部署到 $IP..."
    
    try {
        # 复制文件到目标机器
        $Session = New-PSSession -ComputerName $IP -Credential $Credential
        Copy-Item -Path "./terminal_app/*" -Destination "C:\Program Files\ImgCT\terminal\" -ToSession $Session -Recurse -Force
        
        # 远程执行安装
        Invoke-Command -Session $Session -ScriptBlock {
            # 配置终端
            $config = Get-Content "C:\Program Files\ImgCT\terminal\config\terminal_config.json" | ConvertFrom-Json
            $config.device.name = "Terminal-$($env:COMPUTERNAME)"
            $config.gateway.manualHost = "*************"
            $config | ConvertTo-Json -Depth 10 | Set-Content "C:\Program Files\ImgCT\terminal\config\terminal_config.json"
            
            # 创建服务
            New-Service -Name "ImgCT-Terminal" -BinaryPathName "C:\Program Files\ImgCT\terminal\imgct_terminal.exe" -StartupType Automatic
            Start-Service "ImgCT-Terminal"
        }
        
        Remove-PSSession $Session
        Write-Host "$IP 部署成功" -ForegroundColor Green
    }
    catch {
        Write-Host "$IP 部署失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}
```

## 7. 服务管理

### 7.1 Windows服务管理

**创建Windows服务**：
```batch
:: 安装网关服务
sc create "ImgCT Gateway" binPath="C:\Program Files\ImgCT\gateway\imgct_gateway.exe" start=auto
sc description "ImgCT Gateway" "ImgCT图片切换系统网关服务"

:: 安装终端服务
sc create "ImgCT Terminal" binPath="C:\Program Files\ImgCT\terminal\imgct_terminal.exe" start=auto
sc description "ImgCT Terminal" "ImgCT图片切换系统终端服务"

:: 启动服务
net start "ImgCT Gateway"
net start "ImgCT Terminal"
```

**服务监控脚本**：
```powershell
# monitor_services.ps1
while ($true) {
    $services = @("ImgCT Gateway", "ImgCT Terminal")
    
    foreach ($serviceName in $services) {
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
        
        if ($service -eq $null) {
            Write-Host "$serviceName 服务不存在" -ForegroundColor Red
        }
        elseif ($service.Status -ne "Running") {
            Write-Host "$serviceName 服务已停止，正在重启..." -ForegroundColor Yellow
            Start-Service -Name $serviceName
        }
        else {
            Write-Host "$serviceName 服务运行正常" -ForegroundColor Green
        }
    }
    
    Start-Sleep -Seconds 60
}
```

### 7.2 Linux服务管理

**systemd服务配置**：
```ini
# /etc/systemd/system/imgct-gateway.service
[Unit]
Description=ImgCT Gateway Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=imgct
Group=imgct
WorkingDirectory=/opt/imgct/gateway
ExecStart=/opt/imgct/gateway/imgct_gateway_linux
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
KillMode=mixed
TimeoutStopSec=30

# 环境变量
Environment=NODE_ENV=production
Environment=PORT=3000

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
```

**服务管理命令**：
```bash
# 重新加载配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start imgct-gateway
sudo systemctl start imgct-terminal

# 设置开机自启
sudo systemctl enable imgct-gateway
sudo systemctl enable imgct-terminal

# 查看服务状态
sudo systemctl status imgct-gateway
sudo systemctl status imgct-terminal

# 查看服务日志
sudo journalctl -u imgct-gateway -f
sudo journalctl -u imgct-terminal -f
```

## 8. 监控和维护

### 8.1 健康检查

**健康检查脚本**：
```bash
#!/bin/bash
# health_check.sh

GATEWAY_HOST="*************"
GATEWAY_PORT="3000"
WS_PORT="8080"

echo "ImgCT系统健康检查 - $(date)"
echo "================================"

# 检查网关HTTP服务
if curl -s -f "http://$GATEWAY_HOST:$GATEWAY_PORT/api/health" > /dev/null; then
    echo "✓ 网关HTTP服务正常"
else
    echo "✗ 网关HTTP服务异常"
fi

# 检查WebSocket服务
if nc -z $GATEWAY_HOST $WS_PORT; then
    echo "✓ WebSocket服务正常"
else
    echo "✗ WebSocket服务异常"
fi

# 检查磁盘空间
DISK_USAGE=$(df /opt/imgct | tail -1 | awk '{print $5}' | sed 's/%//')
if [ $DISK_USAGE -lt 80 ]; then
    echo "✓ 磁盘空间充足 ($DISK_USAGE%)"
else
    echo "⚠ 磁盘空间不足 ($DISK_USAGE%)"
fi

# 检查内存使用
MEM_USAGE=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
if [ $MEM_USAGE -lt 80 ]; then
    echo "✓ 内存使用正常 ($MEM_USAGE%)"
else
    echo "⚠ 内存使用过高 ($MEM_USAGE%)"
fi

echo "================================"
```

### 8.2 日志管理

**日志轮转配置 (logrotate)**：
```
# /etc/logrotate.d/imgct
/opt/imgct/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 imgct imgct
    postrotate
        systemctl reload imgct-gateway
    endscript
}
```

### 8.3 备份策略

**自动备份脚本**：
```bash
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup/imgct"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="imgct_backup_$DATE.tar.gz"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库和配置
tar -czf "$BACKUP_DIR/$BACKUP_FILE" \
    /opt/imgct/gateway/data/ \
    /opt/imgct/gateway/config/ \
    /opt/imgct/gateway/uploads/ \
    /opt/imgct/logs/

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "imgct_backup_*.tar.gz" -mtime +7 -delete

echo "备份完成: $BACKUP_DIR/$BACKUP_FILE"
```

## 9. 故障排除

### 9.1 常见问题

**问题1：设备无法发现**
- 检查网络连通性：`ping *************`
- 检查防火墙设置：确保UDP 8888端口开放
- 检查服务状态：`systemctl status imgct-gateway`

**问题2：截图失败**
- 检查权限：确保应用有屏幕录制权限
- 检查磁盘空间：确保有足够存储空间
- 查看错误日志：`tail -f /opt/imgct/logs/terminal.log`

**问题3：显示异常**
- 检查显示器连接
- 确认图片文件完整性
- 重启显示服务

### 9.2 诊断工具

**网络诊断脚本**：
```bash
#!/bin/bash
# network_diag.sh

echo "网络诊断工具"
echo "============"

# 检查网络接口
echo "网络接口:"
ip addr show

# 检查路由
echo "\n路由表:"
ip route show

# 检查端口监听
echo "\n端口监听:"
netstat -tlnp | grep -E ':(3000|8080|8888)'

# 检查连接状态
echo "\n活动连接:"
netstat -an | grep -E ':(3000|8080)' | head -10
```

通过以上详细的部署指南，管理员可以快速、安全地在各种环境中部署Flutter图片切换系统，并进行有效的运维管理。