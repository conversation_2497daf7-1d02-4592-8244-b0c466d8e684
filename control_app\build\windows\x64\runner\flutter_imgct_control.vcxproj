﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A7888545-8DE0-3904-AF1C-52F29BFE81D4}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.22621.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>flutter_imgct_control</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">flutter_imgct_control.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">flutter_imgct_control</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\Profile\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">flutter_imgct_control.dir\Profile\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">flutter_imgct_control</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">flutter_imgct_control.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">flutter_imgct_control</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;_HAS_EXCEPTIONS=0;_DEBUG;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Debug\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\connectivity_plus\Debug\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Debug\file_selector_windows_plugin.lib;..\plugins\screen_retriever\Debug\screen_retriever_plugin.lib;..\plugins\system_tray\Debug\system_tray_plugin.lib;..\plugins\window_manager\Debug\window_manager_plugin.lib;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/Debug/flutter_imgct_control.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/Debug/flutter_imgct_control.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>E:\AI_codeE\imgCT\control_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Profile"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Profile\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Profile\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\connectivity_plus\Profile\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Profile\file_selector_windows_plugin.lib;..\plugins\screen_retriever\Profile\screen_retriever_plugin.lib;..\plugins\system_tray\Profile\system_tray_plugin.lib;..\plugins\window_manager\Profile\window_manager_plugin.lib;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/Profile/flutter_imgct_control.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/Profile/flutter_imgct_control.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>E:\AI_codeE\imgCT\control_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <DisableSpecificWarnings>"4100"</DisableSpecificWarnings>
      <ExceptionHandling>Sync</ExceptionHandling>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <TreatWarningAsError>true</TreatWarningAsError>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level4</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION="1.0.0+1";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;_HAS_EXCEPTIONS=0;FLUTTER_VERSION=\"1.0.0+1\";FLUTTER_VERSION_MAJOR=1;FLUTTER_VERSION_MINOR=0;FLUTTER_VERSION_PATCH=0;FLUTTER_VERSION_BUILD=1;NOMINMAX;UNICODE;_UNICODE;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\AI_codeE\imgCT\control_app\windows;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\cpp_client_wrapper\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\connectivity_plus\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\file_selector_windows\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\screen_retriever\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\system_tray\windows\include;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\.plugin_symlinks\window_manager\windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>..\flutter\Release\flutter_wrapper_app.lib;dwmapi.lib;..\plugins\connectivity_plus\Release\connectivity_plus_plugin.lib;..\plugins\file_selector_windows\Release\file_selector_windows_plugin.lib;..\plugins\screen_retriever\Release\screen_retriever_plugin.lib;..\plugins\system_tray\Release\system_tray_plugin.lib;..\plugins\window_manager\Release\window_manager_plugin.lib;E:\AI_codeE\imgCT\control_app\windows\flutter\ephemeral\flutter_windows.dll.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/Release/flutter_imgct_control.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/Release/flutter_imgct_control.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
    <Manifest>
      <AdditionalManifestFiles>E:\AI_codeE\imgCT\control_app\windows\runner\runner.exe.manifest;</AdditionalManifestFiles>
    </Manifest>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\AI_codeE\imgCT\control_app\windows\runner\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/AI_codeE/imgCT/control_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-file E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule E:/AI_codeE/imgCT/control_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-file E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/AI_codeE/imgCT/control_app/windows/runner/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"E:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SE:/AI_codeE/imgCT/control_app/windows -BE:/AI_codeE/imgCT/control_app/build/windows/x64 --check-stamp-file E:/AI_codeE/imgCT/control_app/build/windows/x64/runner/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\AI_codeE\imgCT\control_app\build\windows\x64\runner\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\AI_codeE\imgCT\control_app\windows\runner\flutter_window.cpp" />
    <ClCompile Include="E:\AI_codeE\imgCT\control_app\windows\runner\main.cpp" />
    <ClCompile Include="E:\AI_codeE\imgCT\control_app\windows\runner\utils.cpp" />
    <ClCompile Include="E:\AI_codeE\imgCT\control_app\windows\runner\win32_window.cpp" />
    <ClCompile Include="E:\AI_codeE\imgCT\control_app\windows\flutter\generated_plugin_registrant.cc" />
    <ResourceCompile Include="E:\AI_codeE\imgCT\control_app\windows\runner\Runner.rc" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{8ACE0CD1-DAAF-314C-8DC2-475159586B90}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\connectivity_plus\connectivity_plus_plugin.vcxproj">
      <Project>{8FE87E99-2263-3D05-A049-608FAB3F60EF}</Project>
      <Name>connectivity_plus_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\file_selector_windows\file_selector_windows_plugin.vcxproj">
      <Project>{F3FD35BF-F8F7-3C27-B754-B31D771EC58C}</Project>
      <Name>file_selector_windows_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\flutter\flutter_assemble.vcxproj">
      <Project>{76674415-1AAF-3434-91DE-9D8937F3B327}</Project>
      <Name>flutter_assemble</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\flutter\flutter_wrapper_app.vcxproj">
      <Project>{DDD02087-EDEA-3F58-8ED1-70EECC001A61}</Project>
      <Name>flutter_wrapper_app</Name>
    </ProjectReference>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\screen_retriever\screen_retriever_plugin.vcxproj">
      <Project>{3E3059B8-53EF-362D-8479-E6FD8CB82AFF}</Project>
      <Name>screen_retriever_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\system_tray\system_tray_plugin.vcxproj">
      <Project>{B3C7B524-CE2E-3110-8B43-21918BCC146C}</Project>
      <Name>system_tray_plugin</Name>
    </ProjectReference>
    <ProjectReference Include="E:\AI_codeE\imgCT\control_app\build\windows\x64\plugins\window_manager\window_manager_plugin.vcxproj">
      <Project>{DBB9A71E-99A2-3998-87B9-8D914721C01D}</Project>
      <Name>window_manager_plugin</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>