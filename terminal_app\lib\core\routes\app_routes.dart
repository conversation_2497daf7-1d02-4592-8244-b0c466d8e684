import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/splash/presentation/pages/splash_page.dart';
import '../../features/screenshot/presentation/pages/screenshot_page.dart';
import '../../features/display/presentation/pages/display_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';

/// 应用路由配置类
class AppRoutes {
  // 路由路径常量
  static const String splash = '/';
  static const String login = '/login';
  static const String screenshot = '/screenshot';
  static const String display = '/display';
  static const String settings = '/settings';

  /// 路由配置
  static final GoRouter router = GoRouter(
    initialLocation: splash,
    debugLogDiagnostics: true,
    routes: [
      // 启动页
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // 登录页
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),

      // 主要功能页面
      ShellRoute(
        builder: (context, state, child) {
          return MainShell(child: child);
        },
        routes: [
          // 截图服务
          GoRoute(
            path: screenshot,
            name: 'screenshot',
            builder: (context, state) => const ScreenshotPage(),
          ),

          // 图片显示
          GoRoute(
            path: display,
            name: 'display',
            builder: (context, state) => const DisplayPage(),
          ),

          // 设置
          GoRoute(
            path: settings,
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
    ],

    // 错误页面
    errorBuilder: (context, state) => ErrorPage(
      error: state.error.toString(),
    ),

    // 路由重定向逻辑
    redirect: (context, state) {
      // 这里可以添加认证检查逻辑
      // 例如：检查用户是否已登录
      return null; // 暂时不重定向
    },
  );
}

/// 主应用外壳 - 包含导航栏等公共UI
class MainShell extends StatelessWidget {
  final Widget child;

  const MainShell({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // GetWidget 侧边导航栏
          Container(
            width: 280,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.blue.shade600,
                  Colors.blue.shade800,
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(2, 0),
                ),
              ],
            ),
            child: Column(
              children: [
                // 头部区域
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.white,
                        radius: 35,
                        child: Icon(
                          Icons.computer,
                          size: 35,
                          color: Colors.blue.shade600,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        '图片切换终端',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Terminal App',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),

                // 导航菜单
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    children: [
                      _buildNavItem(
                        context,
                        icon: Icons.screenshot,
                        title: '截图服务',
                        subtitle: '设备截图管理',
                        index: 0,
                      ),
                      const SizedBox(height: 8),
                      _buildNavItem(
                        context,
                        icon: Icons.display_settings,
                        title: '图片显示',
                        subtitle: '图片展示控制',
                        index: 1,
                      ),
                      const SizedBox(height: 8),
                      _buildNavItem(
                        context,
                        icon: Icons.settings,
                        title: '系统设置',
                        subtitle: '应用配置管理',
                        index: 2,
                      ),

                      const SizedBox(height: 20),

                      // 分割线
                      Divider(
                        color: Colors.white.withOpacity(0.2),
                        thickness: 1,
                      ),

                      const SizedBox(height: 12),

                      // 状态信息
                      Card(
                        margin: const EdgeInsets.all(0),
                        color: Colors.white.withOpacity(0.1),
                        elevation: 0,
                        child: Padding(
                          padding: const EdgeInsets.all(12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Icons.circle,
                                    color: Colors.green,
                                    size: 8,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    '系统状态',
                                    style: TextStyle(
                                      color: Colors.white.withOpacity(0.9),
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '运行正常',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.7),
                                  fontSize: 10,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 底部信息
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Text(
                    'Version 1.0.0',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.6),
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // 主内容区域
          Expanded(child: child),
        ],
      ),
    );
  }

  /// 构建导航项
  Widget _buildNavItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required int index,
  }) {
    final isSelected = _getSelectedIndex(context) == index;

    return Container(
      margin: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        leading: CircleAvatar(
          backgroundColor:
              isSelected ? Colors.white : Colors.white.withOpacity(0.2),
          radius: 20,
          child: Icon(
            icon,
            color: isSelected
                ? Colors.blue.shade600
                : Colors.white.withOpacity(0.8),
            size: 20,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.9),
            fontSize: 14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: isSelected
                ? Colors.white.withOpacity(0.8)
                : Colors.white.withOpacity(0.6),
            fontSize: 11,
          ),
        ),
        onTap: () => _onDestinationSelected(context, index),
      ),
    );
  }

  /// 获取当前选中的导航项索引
  int _getSelectedIndex(BuildContext context) {
    final location = GoRouterState.of(context).uri.toString();
    switch (location) {
      case AppRoutes.screenshot:
        return 0;
      case AppRoutes.display:
        return 1;
      case AppRoutes.settings:
        return 2;
      default:
        return 0;
    }
  }

  /// 处理导航项选择
  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.screenshot);
        break;
      case 1:
        context.go(AppRoutes.display);
        break;
      case 2:
        context.go(AppRoutes.settings);
        break;
    }
  }
}

/// 错误页面
class ErrorPage extends StatelessWidget {
  final String error;

  const ErrorPage({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('页面错误'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '页面加载出错',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.screenshot),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}

/// 路由扩展方法
extension AppRouterExtension on BuildContext {
  /// 导航到截图页面
  void goToScreenshot() => go(AppRoutes.screenshot);

  /// 导航到显示页面
  void goToDisplay() => go(AppRoutes.display);

  /// 导航到设置页面
  void goToSettings() => go(AppRoutes.settings);

  /// 导航到登录页面
  void goToLogin() => go(AppRoutes.login);
}
