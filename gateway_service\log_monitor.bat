@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ╔═══════════════════════════════════════════════════════════════╗
echo ║                                                               ║
echo ║           Flutter 网关服务 - 日志监控工具                    ║
echo ║           Version: 1.0.0                                     ║
echo ║                                                               ║
echo ╚═══════════════════════════════════════════════════════════════╝
echo.

if not exist "logs" (
    echo [ERROR] 日志目录不存在
    echo [HINT] 请先启动网关服务生成日志文件
    pause
    exit /b 1
)

echo [INFO] 检测到日志目录: %CD%\logs\
echo.

REM 显示菜单
:menu
echo ═══════════════════════════════════════════════════════════════
echo                        日志监控菜单                           
echo ═══════════════════════════════════════════════════════════════
echo.
echo [1] 查看今日运行日志
echo [2] 查看今日错误日志  
echo [3] 实时监控运行日志
echo [4] 实时监控错误日志
echo [5] 显示日志统计信息
echo [6] 清理历史日志
echo [7] 搜索日志内容
echo [0] 退出
echo.
set /p choice="请选择操作 (0-7): "

if "%choice%"=="1" goto view_today_log
if "%choice%"=="2" goto view_today_error
if "%choice%"=="3" goto tail_log
if "%choice%"=="4" goto tail_error
if "%choice%"=="5" goto show_stats
if "%choice%"=="6" goto cleanup_logs
if "%choice%"=="7" goto search_logs
if "%choice%"=="0" goto exit
goto menu

:view_today_log
set today=%date:~0,4%-%date:~5,2%-%date:~8,2%
set log_file=logs\gateway-%today%.log
echo.
echo [INFO] 查看今日运行日志: %log_file%
echo ───────────────────────────────────────────────────────────────
if exist "%log_file%" (
    type "%log_file%"
) else (
    echo [WARNING] 今日运行日志文件不存在
)
echo.
pause
goto menu

:view_today_error
set today=%date:~0,4%-%date:~5,2%-%date:~8,2%
set error_file=logs\error-%today%.log
echo.
echo [INFO] 查看今日错误日志: %error_file%
echo ───────────────────────────────────────────────────────────────
if exist "%error_file%" (
    type "%error_file%"
) else (
    echo [INFO] 今日无错误日志 (这是好事!)
)
echo.
pause
goto menu

:tail_log
set today=%date:~0,4%-%date:~5,2%-%date:~8,2%
set log_file=logs\gateway-%today%.log
echo.
echo [INFO] 实时监控运行日志: %log_file%
echo [HINT] 按 Ctrl+C 退出监控
echo ───────────────────────────────────────────────────────────────
if exist "%log_file%" (
    powershell -Command "Get-Content '%log_file%' -Wait -Tail 20"
) else (
    echo [WARNING] 今日运行日志文件不存在
    echo [HINT] 请先启动网关服务
)
echo.
pause
goto menu

:tail_error
set today=%date:~0,4%-%date:~5,2%-%date:~8,2%
set error_file=logs\error-%today%.log
echo.
echo [INFO] 实时监控错误日志: %error_file%
echo [HINT] 按 Ctrl+C 退出监控
echo ───────────────────────────────────────────────────────────────
if exist "%error_file%" (
    powershell -Command "Get-Content '%error_file%' -Wait -Tail 10"
) else (
    echo [INFO] 今日无错误日志文件
    echo [INFO] 将等待错误日志产生...
    timeout /t 2 >nul
    goto tail_error
)
echo.
pause
goto menu

:show_stats
echo.
echo [INFO] 日志统计信息
echo ═══════════════════════════════════════════════════════════════
echo.

REM 统计日志文件数量
for /f %%i in ('dir logs\*.log /B 2^>nul ^| find /C ".log"') do set log_count=%%i
echo 📁 日志文件总数: %log_count%
echo.

REM 显示日志文件列表
echo 📋 日志文件列表:
dir logs\*.log /O-D 2>nul | findstr "gateway\|error"
echo.

REM 显示今日日志大小
set today=%date:~0,4%-%date:~5,2%-%date:~8,2%
if exist "logs\gateway-%today%.log" (
    for %%A in ("logs\gateway-%today%.log") do (
        echo 📊 今日运行日志大小: %%~zA 字节
    )
)

if exist "logs\error-%today%.log" (
    for %%A in ("logs\error-%today%.log") do (
        echo ❌ 今日错误日志大小: %%~zA 字节
    )
) else (
    echo ✅ 今日无错误日志 (系统运行正常)
)

echo.

REM 统计今日日志条数
if exist "logs\gateway-%today%.log" (
    for /f %%i in ('type "logs\gateway-%today%.log" 2^>nul ^| find /C "][" 2^>nul') do (
        echo 📈 今日日志条数: %%i
    )
)

REM 分析日志级别分布
if exist "logs\gateway-%today%.log" (
    echo.
    echo 📊 今日日志级别分布:
    for /f %%i in ('type "logs\gateway-%today%.log" 2^>nul ^| find /C "[DEBUG]" 2^>nul') do echo   DEBUG: %%i 条
    for /f %%i in ('type "logs\gateway-%today%.log" 2^>nul ^| find /C "[INFO]" 2^>nul') do echo   INFO:  %%i 条
    for /f %%i in ('type "logs\gateway-%today%.log" 2^>nul ^| find /C "[WARN]" 2^>nul') do echo   WARN:  %%i 条
    for /f %%i in ('type "logs\gateway-%today%.log" 2^>nul ^| find /C "[ERROR]" 2^>nul') do echo   ERROR: %%i 条
)

echo.
pause
goto menu

:cleanup_logs
echo.
echo [WARNING] 清理历史日志
echo ═══════════════════════════════════════════════════════════════
echo.
echo 这将删除7天前的日志文件，释放磁盘空间
echo.
set /p confirm="确定要清理历史日志吗? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo.
echo [INFO] 正在清理7天前的日志文件...

REM 计算7天前的日期 (简化版，实际应该用更复杂的日期计算)
for /f "tokens=1-3 delims=- " %%a in ("%date%") do (
    set /a year=%%a
    set /a month=%%b
    set /a day=%%c-7
)

if %day% LEQ 0 (
    set /a month-=1
    set /a day+=30
)

if %month% LEQ 0 (
    set /a year-=1
    set /a month+=12
)

echo [INFO] 保留 %year%-%month%-%day% 之后的日志文件
echo [INFO] 清理策略: 保留最近7天的日志

REM 显示将要删除的文件
echo.
echo 将要删除的日志文件:
dir logs\*.log /O-D | more +10 2>nul

echo.
set /p final_confirm="确认删除以上文件? (y/N): "
if /i not "%final_confirm%"=="y" goto menu

REM 执行清理 (简化版 - 实际应该根据日期判断)
echo [INFO] 执行清理操作...
cd logs
for /f "skip=10 tokens=*" %%F in ('dir *.log /B /O-D 2^>nul') do (
    echo 删除: %%F
    del "%%F" 2>nul
)
cd ..

echo [SUCCESS] 历史日志清理完成
echo.
pause
goto menu

:search_logs
echo.
echo [INFO] 搜索日志内容
echo ═══════════════════════════════════════════════════════════════
echo.
set /p search_term="请输入搜索关键词: "
if "%search_term%"=="" goto menu

echo.
echo [INFO] 搜索关键词: %search_term%
echo [INFO] 搜索范围: 所有日志文件
echo ───────────────────────────────────────────────────────────────

REM 在所有日志文件中搜索
findstr /i /n "%search_term%" logs\*.log 2>nul

if errorlevel 1 (
    echo [INFO] 未找到相关日志记录
) else (
    echo.
    echo [SUCCESS] 搜索完成
)

echo.
pause
goto menu

:exit
echo.
echo [INFO] 感谢使用日志监控工具
echo [INFO] 日志文件位置: %CD%\logs\
echo.
pause
exit /b 0
