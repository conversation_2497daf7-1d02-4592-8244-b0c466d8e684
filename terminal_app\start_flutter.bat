@echo off
chcp 65001 >nul
title Flutter应用 - 图片切换系统

echo.
echo ================================================================
echo.
echo    Flutter图片切换系统 - 应用启动器 v2.0.0
echo.
echo ================================================================
echo.

:: 清理Flutter进程
echo [INFO] 清理Flutter进程...
taskkill /f /im flutter_imgct_terminal.exe >nul 2>&1
taskkill /f /im dart.exe >nul 2>&1

echo [SUCCESS] 进程清理完成
echo.

:: 设置Flutter路径
set FLUTTER_PATH=E:\flutter\bin
set PATH=%FLUTTER_PATH%;%PATH%

:: 检查Flutter是否可用
echo [INFO] 检查Flutter安装...
"%FLUTTER_PATH%\flutter.bat" --version
if %errorlevel% neq 0 (
    echo [ERROR] Flutter未找到，请确认Flutter已安装在 E:\flutter
    echo [INFO] 如果Flutter安装在其他位置，请修改此脚本中的FLUTTER_PATH变量
    pause
    exit /b 1
)

:: 检查依赖
echo [INFO] 检查Flutter依赖...
"%FLUTTER_PATH%\flutter.bat" pub get

echo.
echo [INFO] 启动Flutter应用...
echo.
echo ================================================================
echo Flutter热重载命令:
echo    r  - 热重载 (Hot Reload)
echo    R  - 热重启 (Hot Restart)
echo    q  - 退出应用
echo    h  - 显示帮助
echo ================================================================
echo.

:: 启动应用
"%FLUTTER_PATH%\flutter.bat" run -d windows

:: 如果应用退出
echo.
echo [WARN] Flutter应用已停止
pause
