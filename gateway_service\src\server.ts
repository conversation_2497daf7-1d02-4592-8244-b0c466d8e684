import express from 'express';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import path from 'path';
import fs from 'fs/promises';

// Import services
import { databaseService } from './services/DatabaseService';
import { WebSocketService } from './services/WebSocketService';
import { NativeWebSocketService } from './services/NativeWebSocketService';
import { DeviceDiscoveryService } from './services/DeviceDiscoveryService';
import { imageService } from './services/ImageService';

// Import routes
import deviceRoutes from './routes/devices';
import imageRoutes from './routes/images';
import authRoutes from './routes/auth';
import discoveryRoutes from './routes/discovery';

// Import configuration
import config, {
  validateConfig,
  getConfigSummary,
  securityHeaders,
  rateLimitConfig,
  websocketConfig,
  getCorsOrigins,
  getUploadPaths
} from './config';

// Import types
import { SystemStats } from './models';

class GatewayServer {
  private app: express.Application;
  private server: any;
  private isShuttingDown = false;
  private startTime: Date;

  constructor() {
    this.app = express();
    this.startTime = new Date();
  }

  public async start(): Promise<void> {
    try {
      console.log('🚀 Starting Flutter Image Control Gateway...');
      
      // Validate configuration
      validateConfig();
      console.log('✅ Configuration validated');
      
      // Log configuration summary
      console.log('📋 Configuration:', JSON.stringify(getConfigSummary(), null, 2));
      
      // Ensure required directories exist
      await this.ensureDirectories();
      
      // Initialize services
      await this.initializeServices();
      
      // Setup Express middleware
      this.setupMiddleware();
      
      // Setup routes
      this.setupRoutes();
      
      // Setup error handling
      this.setupErrorHandling();
      
      // Create HTTP server
      this.server = createServer(this.app);
      
      // Create Socket.IO server with simplified configuration
      const io = new SocketIOServer(this.server, {
        cors: {
          origin: "*",
          methods: ["GET", "POST"],
          credentials: false
        },
        pingTimeout: 60000,
        pingInterval: 25000,
        allowEIO3: true,
        transports: ['polling'], // 只允许polling传输
        // 添加连接超时设置
        connectTimeout: 45000,
        // 允许所有来源的连接
        allowRequest: (req, callback) => {
          console.log('🔍 Socket.IO allowRequest called for:', req.url);
          console.log('   Headers:', req.headers);
          callback(null, true);
        },
        allowEIO3: true // 兼容Engine.IO 3
      });
      
      // 添加详细的连接调试日志
      io.engine.on('initial_headers', (headers: any, req: any) => {
        console.log('🔍 Socket.IO initial headers from:', req.connection.remoteAddress);
        console.log('   Request URL:', req.url);
        console.log('   Request method:', req.method);
        console.log('   Request headers:', req.headers);
      });
      
      io.engine.on('connection_error', (err: any) => {
        console.log('❌ Socket.IO connection error:', err);
      });
      
      // 添加 Engine.IO 连接事件
      io.engine.on('connection', (socket: any) => {
        console.log('🔗 Engine.IO connection established:', socket.id);
        console.log('   Transport:', socket.transport.name);
        console.log('   Protocol:', socket.protocol);
      });
      
      // 添加握手事件
      io.on('connection', (socket: any) => {
        console.log('🤝 Socket.IO handshake completed for:', socket.id);
      });
      
      // Initialize WebSocket services
      WebSocketService.getInstance().initialize(io);
      NativeWebSocketService.getInstance().initialize(this.server);
      
      // Start device discovery
      await DeviceDiscoveryService.getInstance().start();
      
      // Setup graceful shutdown
      this.setupGracefulShutdown();
      
      // Start listening
      await this.listen();
      
      console.log('🎉 Gateway server started successfully!');
      console.log(`📡 Server running on http://${config.server.host}:${config.server.port}`);
      console.log(`🔍 Device discovery on UDP port ${config.discovery.port}`);
      
    } catch (error) {
      console.error('❌ Failed to start gateway server:', error);
      process.exit(1);
    }
  }

  private async ensureDirectories(): Promise<void> {
    const paths = getUploadPaths();
    const directories = [
      paths.base,
      paths.images,
      paths.thumbnails,
      paths.temp,
      path.join(process.cwd(), 'data'),
      path.join(process.cwd(), 'logs')
    ];

    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        console.warn(`Warning: Could not create directory ${dir}:`, error);
      }
    }
    
    console.log('📁 Required directories ensured');
  }

  private async initializeServices(): Promise<void> {
    console.log('🔧 Initializing services...');
    
    // Initialize database
    await databaseService.initialize();
    console.log('✅ Database service initialized');
    
    // Initialize image service
      await imageService.initialize();
    console.log('✅ Image service initialized');
    
    console.log('✅ All services initialized');
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet(securityHeaders));
    
    // CORS middleware
    this.app.use(cors({
      origin: getCorsOrigins(),
      credentials: config.server.cors.credentials,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));
    
    // Rate limiting
    this.app.use('/api/auth', rateLimit(rateLimitConfig.auth));
    this.app.use('/api/images/upload', rateLimit(rateLimitConfig.upload));
    this.app.use('/api', rateLimit(rateLimitConfig.general));
    
    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    
    // Request logging middleware
    this.app.use((req, res, next) => {
      const start = Date.now();
      res.on('finish', () => {
        const duration = Date.now() - start;
        console.log(`${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
      });
      next();
    });
    
    // Static file serving for uploads
    const uploadPaths = getUploadPaths();
    this.app.use('/uploads/images', express.static(uploadPaths.images));
    this.app.use('/uploads/thumbnails', express.static(uploadPaths.thumbnails));
    
    console.log('✅ Middleware configured');
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - this.startTime.getTime(),
        version: process.env.npm_package_version || '1.0.0'
      });
    });
    
    // System stats endpoint
    this.app.get('/api/stats', async (req, res) => {
      try {
        const stats = await this.getSystemStats();
        res.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Error getting system stats:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get system stats',
          timestamp: new Date().toISOString()
        });
      }
    });
    
    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/devices', deviceRoutes);
    this.app.use('/api/images', imageRoutes);
    this.app.use('/api/discovery', discoveryRoutes);
    
    // Catch-all route for undefined endpoints
    this.app.use('/api/*', (req, res) => {
      res.status(404).json({
        success: false,
        error: 'API endpoint not found',
        timestamp: new Date().toISOString()
      });
    });
    
    // Root endpoint - only respond to non-Socket.IO requests
    this.app.get('/', (req, res, next) => {
      // Check if this is a Socket.IO polling request
      if (req.query.EIO || req.query.transport) {
        return next(); // Let Socket.IO handle it
      }
      
      res.json({
        name: 'Flutter Image Control Gateway',
        version: process.env.npm_package_version || '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        endpoints: {
          health: '/health',
          stats: '/api/stats',
          auth: '/api/auth',
          devices: '/api/devices',
          images: '/api/images',
          websocket: '/socket.io'
        }
      });
    });
    
    console.log('✅ Routes configured');
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      console.error('Unhandled error:', err);
      
      // Don't leak error details in production
      const isDev = process.env.NODE_ENV === 'development';
      
      res.status(err.statusCode || 500).json({
        success: false,
        error: err.message || 'Internal server error',
        ...(isDev && { stack: err.stack }),
        timestamp: new Date().toISOString()
      });
    });
    
    // 404 handler - exclude Socket.IO paths
    this.app.use((req, res, next) => {
      // Don't handle Socket.IO requests
      if (req.path.startsWith('/socket.io/')) {
        return next();
      }
      
      res.status(404).json({
        success: false,
        error: 'Resource not found',
        timestamp: new Date().toISOString()
      });
    });
    
    console.log('✅ Error handling configured');
  }

  private async listen(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server.listen(config.server.port, config.server.host, (err?: Error) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      if (this.isShuttingDown) {
        return;
      }
      
      this.isShuttingDown = true;
      console.log(`\n🛑 Received ${signal}, starting graceful shutdown...`);
      
      try {
        // Stop accepting new connections
        if (this.server) {
          this.server.close();
        }
        
        // Stop device discovery
        await DeviceDiscoveryService.getInstance().stop();
        console.log('✅ Device discovery stopped');
        
        // Close database connection
        if (databaseService.isConnected()) {
      await databaseService.close();
          console.log('✅ Database connection closed');
        }
        
        console.log('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    };
    
    // Handle different shutdown signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGUSR2', () => shutdown('SIGUSR2')); // nodemon restart
    
    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('❌ Uncaught exception:', error);
      shutdown('uncaughtException');
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      console.error('❌ Unhandled rejection at:', promise, 'reason:', reason);
      shutdown('unhandledRejection');
    });
  }

  private async getSystemStats(): Promise<SystemStats> {
    try {
      // Get device stats
      const deviceStats = await databaseService.query(`
        SELECT 
          COUNT(*) as total,
          SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online,
          SUM(CASE WHEN status = 'offline' THEN 1 ELSE 0 END) as offline
        FROM devices
      `);
      
      const devicePlatforms = await databaseService.query(`
        SELECT platform, COUNT(*) as count 
        FROM devices 
        GROUP BY platform
      `);
      
      // Get image stats
      const imageStats = await imageService.getImageStats();
      
      // Get task stats
      const captureTaskStats = await databaseService.query(`
        SELECT 
          status,
          COUNT(*) as count
        FROM capture_tasks 
        WHERE created_at > datetime('now', '-24 hours')
        GROUP BY status
      `);
      
      const displayTaskStats = await databaseService.query(`
        SELECT 
          status,
          COUNT(*) as count
        FROM display_tasks 
        WHERE created_at > datetime('now', '-24 hours')
        GROUP BY status
      `);
      
      // Build platform distribution
      const byPlatform: Record<string, number> = {};
      devicePlatforms.forEach(row => {
        byPlatform[row.platform] = row.count;
      });
      
      // Build task stats
      const captureStats = { pending: 0, running: 0, completed: 0, failed: 0 };
      captureTaskStats.forEach(row => {
        captureStats[row.status as keyof typeof captureStats] = row.count;
      });
      
      const displayStats = { pending: 0, active: 0, completed: 0, cancelled: 0 };
      displayTaskStats.forEach(row => {
        displayStats[row.status as keyof typeof displayStats] = row.count;
      });
      
      return {
        devices: {
          total: deviceStats[0]?.total || 0,
          online: deviceStats[0]?.online || 0,
          offline: deviceStats[0]?.offline || 0,
          byPlatform
        },
        images: {
          total: imageStats.totalImages,
          totalSize: imageStats.totalSize,
          averageSize: imageStats.averageSize,
          byFormat: imageStats.formatDistribution,
          byDevice: imageStats.deviceDistribution
        },
        tasks: {
          capture: captureStats,
          display: displayStats
        },
        connections: {
          devices: WebSocketService.getConnectedDevicesCount(),
          clients: WebSocketService.getConnectedClientsCount()
        },
        uptime: Date.now() - this.startTime.getTime()
      };
    } catch (error) {
      console.error('Error getting system stats:', error);
      throw error;
    }
  }

  public getApp(): express.Application {
    return this.app;
  }

  public getServer(): any {
    return this.server;
  }
}

// Create and export server instance
const gatewayServer = new GatewayServer();

// Start server if this file is run directly
if (require.main === module) {
  gatewayServer.start().catch((error) => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  });
}

export default gatewayServer;
export { GatewayServer };