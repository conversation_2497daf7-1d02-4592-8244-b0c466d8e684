import 'package:device_info_plus/device_info_plus.dart';
import 'package:network_info_plus/network_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'device_model.dart' show DeviceCapabilities, DeviceType, DeviceStatus;

// 条件导入：只在非Web平台导入dart:io
import 'dart:io' if (dart.library.html) '../utils/web_platform_stub.dart' as io;

/// 设备信息类
class DeviceInfo {
  /// 设备唯一标识符
  final String id;
  
  /// 设备名称
  final String name;
  
  /// 设备类型
  final DeviceType type;
  
  /// 操作系统
  final String platform;
  
  /// 操作系统版本
  final String platformVersion;
  
  /// 设备型号
  final String model;
  
  /// 设备制造商
  final String manufacturer;
  
  /// IP地址
  final String? ipAddress;
  
  /// MAC地址
  final String? macAddress;
  
  /// 网络名称(WiFi SSID)
  final String? networkName;
  
  /// 屏幕分辨率
  final ScreenResolution? screenResolution;
  
  /// 设备能力
  final DeviceCapabilities capabilities;
  
  /// 设备状态
  final DeviceStatus status;
  
  /// 创建时间
  final DateTime createdAt;
  
  /// 最后更新时间
  final DateTime updatedAt;
  
  /// 额外信息
  final Map<String, dynamic>? metadata;

  const DeviceInfo({
    required this.id,
    required this.name,
    required this.type,
    required this.platform,
    required this.platformVersion,
    required this.model,
    required this.manufacturer,
    this.ipAddress,
    this.macAddress,
    this.networkName,
    this.screenResolution,
    required this.capabilities,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  /// 从JSON创建设备信息
  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      type: DeviceType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => DeviceType.terminal,
      ),
      platform: json['platform'] as String,
      platformVersion: json['platformVersion'] as String,
      model: json['model'] as String,
      manufacturer: json['manufacturer'] as String,
      ipAddress: json['ipAddress'] as String?,
      macAddress: json['macAddress'] as String?,
      networkName: json['networkName'] as String?,
      screenResolution: json['screenResolution'] != null
          ? ScreenResolution.fromJson(json['screenResolution'])
          : null,
      capabilities: DeviceCapabilities.fromJson(json['capabilities']),
      status: DeviceStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => DeviceStatus.offline,
      ),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'platform': platform,
      'platformVersion': platformVersion,
      'model': model,
      'manufacturer': manufacturer,
      if (ipAddress != null) 'ipAddress': ipAddress,
      if (macAddress != null) 'macAddress': macAddress,
      if (networkName != null) 'networkName': networkName,
      if (screenResolution != null) 'screenResolution': screenResolution!.toJson(),
      'capabilities': capabilities.toJson(),
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      if (metadata != null) 'metadata': metadata,
    };
  }

  /// 获取当前设备信息
  static Future<DeviceInfo> getCurrentDevice() async {
    debugPrint('🔍 开始获取设备信息...');

    try {
      final deviceInfoPlugin = DeviceInfoPlugin();
      final networkInfo = NetworkInfo();

      String deviceId = '';
      String deviceName = '';
      String model = '';
      String manufacturer = '';
      DeviceType deviceType = DeviceType.terminal;
      ScreenResolution? screenResolution;

      final platform = _getSafeOperatingSystem();
      debugPrint('📱 检测平台: $platform');

      if (kIsWeb) {
        // Web平台处理
        debugPrint('🌐 Web平台设备信息处理...');
        deviceId = 'web_${_getSafeHostname()}_terminal';
        deviceName = _getSafeHostname();
        model = 'Web Browser';
        manufacturer = 'Browser';
        deviceType = DeviceType.terminal;
        debugPrint('✅ Web设备信息设置成功: $deviceName');
      } else if (!kIsWeb && io.Platform.isWindows) {
        try {
          debugPrint('🖥️ 获取Windows设备信息...');
          final windowsInfo = await deviceInfoPlugin.windowsInfo;
          deviceId = windowsInfo.deviceId;
          deviceName = windowsInfo.computerName;
          model = windowsInfo.productName;
          manufacturer = windowsInfo.registeredOwner;
          deviceType = DeviceType.terminal;
          debugPrint('✅ Windows设备信息获取成功: $deviceName');
        } catch (e) {
          debugPrint('❌ 获取Windows设备信息失败: $e');
          // 使用更好的默认值
          deviceId = _generateDeviceId();
          deviceName = _getSafeHostname();
          model = 'Windows PC';
          manufacturer = 'Microsoft';
          debugPrint('🔧 使用默认Windows设备信息: $deviceName');
        }
    } else if (!kIsWeb && io.Platform.isLinux) {
      try {
        final linuxInfo = await deviceInfoPlugin.linuxInfo;
        deviceId = linuxInfo.machineId ?? '';
        deviceName = linuxInfo.name;
        model = linuxInfo.prettyName;
        manufacturer = linuxInfo.id;
        deviceType = DeviceType.terminal;
      } catch (e) {
        debugPrint('获取Linux设备信息失败: $e');
        deviceId = _generateDeviceId();
        deviceName = 'Linux Terminal';
        model = 'Linux PC';
        manufacturer = 'Unknown';
      }
    } else if (!kIsWeb && io.Platform.isMacOS) {
      try {
        final macInfo = await deviceInfoPlugin.macOsInfo;
        deviceId = macInfo.systemGUID ?? '';
        deviceName = macInfo.computerName;
        model = macInfo.model;
        manufacturer = 'Apple';
        deviceType = DeviceType.terminal;
      } catch (e) {
        debugPrint('获取macOS设备信息失败: $e');
        deviceId = _generateDeviceId();
        deviceName = 'macOS Terminal';
        model = 'Mac';
        manufacturer = 'Apple';
      }
    } else if (!kIsWeb && io.Platform.isAndroid) {
      try {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceId = androidInfo.id;
        deviceName = androidInfo.device;
        model = androidInfo.model;
        manufacturer = androidInfo.manufacturer;
        deviceType = DeviceType.terminal;
      } catch (e) {
        debugPrint('获取Android设备信息失败: $e');
        deviceId = _generateDeviceId();
        deviceName = 'Android Terminal';
        model = 'Android Device';
        manufacturer = 'Unknown';
      }
    } else if (!kIsWeb && io.Platform.isIOS) {
      try {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? '';
        deviceName = iosInfo.name;
        model = iosInfo.model;
        manufacturer = 'Apple';
        deviceType = DeviceType.terminal;
      } catch (e) {
        debugPrint('获取iOS设备信息失败: $e');
        deviceId = _generateDeviceId();
        deviceName = 'iOS Terminal';
        model = 'iPhone/iPad';
        manufacturer = 'Apple';
      }
    } else {
      // 未知平台，使用默认值
      final platformName = _getSafeOperatingSystem();
      debugPrint('未知平台: $platformName');
      deviceId = _generateDeviceId();
      deviceName = '$platformName Terminal';
      model = 'Unknown Device';
      manufacturer = 'Unknown';
    }

    // 获取网络信息
    String? ipAddress;
    String? macAddress;
    String? networkName;

    try {
      ipAddress = await networkInfo.getWifiIP();
      macAddress = await networkInfo.getWifiBSSID();
      networkName = await networkInfo.getWifiName();
      debugPrint('网络信息获取成功: IP=$ipAddress, MAC=$macAddress, Network=$networkName');
    } catch (e) {
      debugPrint('网络信息获取失败: $e');
      // 网络信息获取失败，使用默认值
      ipAddress = '127.0.0.1';
    }
    
    // 获取屏幕分辨率（需要在UI线程中调用）
    // 这里暂时使用默认值，实际使用时需要在Widget中获取

    final now = DateTime.now();

    // 确保设备ID和名称不为空
    final finalDeviceId = deviceId.isNotEmpty ? deviceId : _generateDeviceId();
    final platformName = _getSafeOperatingSystem();
    final finalDeviceName = deviceName.isNotEmpty ? deviceName : '$platformName Terminal';

    debugPrint('设备信息获取完成: ID=$finalDeviceId, Name=$finalDeviceName, Platform=$platformName');

    return DeviceInfo(
      id: finalDeviceId,
      name: finalDeviceName,
      type: deviceType,
      platform: platformName,
      platformVersion: _getSafeOperatingSystemVersion(),
      model: model.isNotEmpty ? model : platformName,
      manufacturer: manufacturer.isNotEmpty ? manufacturer : 'Unknown',
      ipAddress: ipAddress,
      macAddress: macAddress,
      networkName: networkName,
      screenResolution: screenResolution,
      capabilities: DeviceCapabilities.getDefault(),
      status: DeviceStatus.online,
      createdAt: now,
      updatedAt: now,
    );
    } catch (e) {
      debugPrint('获取设备信息时发生错误: $e');
      // 返回默认设备信息
      final now = DateTime.now();
      final defaultDeviceId = _generateDeviceId();

      final platformName = _getSafeOperatingSystem();
      return DeviceInfo(
        id: defaultDeviceId,
        name: '$platformName Terminal',
        type: DeviceType.terminal,
        platform: platformName,
        platformVersion: _getSafeOperatingSystemVersion(),
        model: platformName,
        manufacturer: 'Unknown',
        ipAddress: '127.0.0.1',
        macAddress: null,
        networkName: null,
        screenResolution: null,
        capabilities: DeviceCapabilities.getDefault(),
        status: DeviceStatus.online,
        createdAt: now,
        updatedAt: now,
      );
    }
  }
  
  /// 生成设备ID
  static String _generateDeviceId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'device_${_getSafeOperatingSystem()}_$random';
  }

  /// 安全获取主机名（Web兼容）
  static String _getSafeHostname() {
    if (kIsWeb) {
      // Web平台使用浏览器信息
      return 'Web-Terminal';
    }

    try {
      // 非Web平台使用Platform.localHostname
      final hostname = io.Platform.localHostname;
      return hostname.isNotEmpty ? hostname : 'Unknown-Device';
    } catch (e) {
      return 'Unknown-Device';
    }
  }

  /// 安全获取操作系统信息（Web兼容）
  static String _getSafeOperatingSystem() {
    if (kIsWeb) {
      return 'web';
    }

    try {
      return io.Platform.operatingSystem;
    } catch (e) {
      return 'unknown';
    }
  }

  /// 安全获取操作系统版本（Web兼容）
  static String _getSafeOperatingSystemVersion() {
    if (kIsWeb) {
      return 'Web Browser';
    }

    try {
      return io.Platform.operatingSystemVersion;
    } catch (e) {
      return 'Unknown';
    }
  }

  /// 复制并更新设备信息
  DeviceInfo copyWith({
    String? id,
    String? name,
    DeviceType? type,
    String? platform,
    String? platformVersion,
    String? model,
    String? manufacturer,
    String? ipAddress,
    String? macAddress,
    String? networkName,
    ScreenResolution? screenResolution,
    DeviceCapabilities? capabilities,
    DeviceStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return DeviceInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      platform: platform ?? this.platform,
      platformVersion: platformVersion ?? this.platformVersion,
      model: model ?? this.model,
      manufacturer: manufacturer ?? this.manufacturer,
      ipAddress: ipAddress ?? this.ipAddress,
      macAddress: macAddress ?? this.macAddress,
      networkName: networkName ?? this.networkName,
      screenResolution: screenResolution ?? this.screenResolution,
      capabilities: capabilities ?? this.capabilities,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  @override
  String toString() {
    return 'DeviceInfo(id: $id, name: $name, type: $type, platform: $platform)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}



/// 屏幕分辨率类
class ScreenResolution {
  final int width;
  final int height;
  final double pixelRatio;

  const ScreenResolution({
    required this.width,
    required this.height,
    required this.pixelRatio,
  });

  factory ScreenResolution.fromJson(Map<String, dynamic> json) {
    return ScreenResolution(
      width: json['width'] as int,
      height: json['height'] as int,
      pixelRatio: (json['pixelRatio'] as num).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'width': width,
      'height': height,
      'pixelRatio': pixelRatio,
    };
  }

  @override
  String toString() {
    return '${width}x$height@${pixelRatio}x';
  }
}