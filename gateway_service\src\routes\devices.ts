import express from 'express';
// DeviceService removed - using database directly
import { WebSocketService } from '../services/WebSocketService';
import { databaseService } from '../services/DatabaseService';

const router = express.Router();

// Get all devices
router.get('/', async (req, res) => {
  try {
    // Get all devices from database
    const devices = await databaseService.query(
      'SELECT * FROM devices ORDER BY created_at DESC'
    );
    res.json({
      success: true,
      data: devices,
      count: devices.length
    });
  } catch (error) {
    console.error('Error fetching devices:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch devices'
    });
  }
});

// Get device by ID
router.get('/:deviceId', async (req, res) => {
  try {
    const { deviceId } = req.params;
    // Get device from database
    const devices = await databaseService.query(
      'SELECT * FROM devices WHERE id = ?',
      [deviceId]
    );
    
    const device = devices.length > 0 ? devices[0] : null;
    
    if (!device) {
      return res.status(404).json({
        success: false,
        error: 'Device not found'
      });
    }
    
    res.json({
      success: true,
      data: device
    });
  } catch (error) {
    console.error('Error fetching device:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch device'
    });
  }
});

// Register a new device
router.post('/register', async (req, res) => {
  try {
    const deviceInfo = req.body;
    
    // Validate required fields
    if (!deviceInfo.id || !deviceInfo.name || !deviceInfo.ip_address) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: id, name, ip_address'
      });
    }
    
    // Register device in database
    const deviceId = `device_${Date.now()}`;
    await databaseService.query(
      'INSERT INTO devices (id, name, type, ip_address, port, status, config, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [deviceId, deviceInfo.name, deviceInfo.type || 'terminal', deviceInfo.ip_address, deviceInfo.port || 8080, 'online', JSON.stringify(deviceInfo.config || {}), new Date().toISOString(), new Date().toISOString()]
    );
    
    const device = { id: deviceId, ...deviceInfo };
    
    // Notify all connected clients about new device
    WebSocketService.getInstance().broadcastToClients('device_registered', device);
    
    res.status(201).json({
      success: true,
      data: device,
      message: 'Device registered successfully'
    });
  } catch (error) {
    console.error('Error registering device:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to register device'
    });
  }
});

// Update device configuration
router.put('/:deviceId/config', async (req, res) => {
  try {
    const { deviceId } = req.params;
    const config = req.body;
    
    // Update device config in database
    await databaseService.query(
      'UPDATE devices SET config = ?, updated_at = ? WHERE id = ?',
      [JSON.stringify(config), new Date().toISOString(), deviceId]
    );
    
    const updatedDevice = { id: deviceId, config };
    
    if (!updatedDevice) {
      return res.status(404).json({
        success: false,
        error: 'Device not found'
      });
    }
    
    // Notify the specific device about config update
    WebSocketService.getInstance().broadcastToClients('config_updated', { deviceId, config });
    
    res.json({
      success: true,
      data: updatedDevice,
      message: 'Device configuration updated successfully'
    });
  } catch (error) {
    console.error('Error updating device config:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update device configuration'
    });
  }
});

// Update device status
router.put('/:deviceId/status', async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { status } = req.body;
    
    if (!['online', 'offline', 'busy'].includes(status)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid status. Must be: online, offline, or busy'
      });
    }
    
    // Update device status in database
    await databaseService.query(
      'UPDATE devices SET status = ?, updated_at = ? WHERE id = ?',
      [status, new Date().toISOString(), deviceId]
    );
    
    const updatedDevice = { id: deviceId, status };
    
    if (!updatedDevice) {
      return res.status(404).json({
        success: false,
        error: 'Device not found'
      });
    }
    
    // Broadcast status update to all clients
    WebSocketService.getInstance().broadcastToClients('device_status_updated', {
      deviceId,
      status,
      timestamp: new Date().toISOString()
    });
    
    res.json({
      success: true,
      data: updatedDevice,
      message: 'Device status updated successfully'
    });
  } catch (error) {
    console.error('Error updating device status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to update device status'
    });
  }
});

// Delete device
router.delete('/:deviceId', async (req, res) => {
  try {
    const { deviceId } = req.params;
    
    // Delete device from database
    const result = await databaseService.query(
      'DELETE FROM devices WHERE id = ?',
      [deviceId]
    );
    
    const deleted = (result as any).changes > 0;
    
    if (!deleted) {
      return res.status(404).json({
        success: false,
        error: 'Device not found'
      });
    }
    
    // Notify all clients about device removal
    WebSocketService.getInstance().broadcastToClients('device_removed', { deviceId });
    
    res.json({
      success: true,
      message: 'Device deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting device:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete device'
    });
  }
});

// Trigger device discovery
router.post('/discover', async (req, res) => {
  try {
    const { DeviceDiscoveryService } = await import('../services/DeviceDiscoveryService.js');
    
    await DeviceDiscoveryService.getInstance().discoverDevices();
    
    res.json({
      success: true,
      message: 'Device discovery triggered successfully'
    });
  } catch (error) {
    console.error('Error triggering device discovery:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to trigger device discovery'
    });
  }
});

// Send command to device
router.post('/:deviceId/command', async (req, res) => {
  try {
    const { deviceId } = req.params;
    const { command, params } = req.body;
    
    if (!command) {
      return res.status(400).json({
        success: false,
        error: 'Command is required'
      });
    }
    
    // Check if device exists and is online
    // Get device from database
    const devices = await databaseService.query(
      'SELECT * FROM devices WHERE id = ?',
      [deviceId]
    );
    
    const device = devices.length > 0 ? devices[0] : null;
    if (!device) {
      return res.status(404).json({
        success: false,
        error: 'Device not found'
      });
    }
    
    if (device.status !== 'online') {
      return res.status(400).json({
        success: false,
        error: 'Device is not online'
      });
    }
    
    // Send command to device via WebSocket
    WebSocketService.getInstance().broadcastToClients('device_command', {
      deviceId,
      command,
      params,
      timestamp: new Date().toISOString()
    });
    
    // Always assume success for simplified implementation
    const success = true;
    
    if (!success) {
      return res.status(400).json({
        success: false,
        error: 'Failed to send command to device'
      });
    }
    
    res.json({
      success: true,
      message: 'Command sent successfully'
    });
  } catch (error) {
    console.error('Error sending command to device:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send command to device'
    });
  }
});

// Get device groups
router.get('/groups/list', async (req, res) => {
  try {
    const groups = await databaseService.query(
      'SELECT * FROM device_groups ORDER BY name'
    );
    
    res.json({
      success: true,
      data: groups
    });
  } catch (error) {
    console.error('Error fetching device groups:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch device groups'
    });
  }
});

// Create device group
router.post('/groups', async (req, res) => {
  try {
    const { name, description, default_config } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Group name is required'
      });
    }
    
    const groupId = `group_${Date.now()}`;
    
    await databaseService.query(
      'INSERT INTO device_groups (id, name, description, default_config) VALUES (?, ?, ?, ?)',
      [groupId, name, description, JSON.stringify(default_config || {})]
    );
    
    const group = await databaseService.query(
      'SELECT * FROM device_groups WHERE id = ?',
      [groupId]
    );
    
    res.status(201).json({
      success: true,
      data: group[0],
      message: 'Device group created successfully'
    });
  } catch (error) {
    console.error('Error creating device group:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to create device group'
    });
  }
});

export default router;