import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';

import '../../../../core/models/screenshot_folder_config.dart';
import '../../../../core/services/screenshot_config_service.dart';
import '../../../../core/services/logger_service.dart';

/// 截图文件夹管理组件
class ScreenshotFoldersWidget extends ConsumerStatefulWidget {
  const ScreenshotFoldersWidget({super.key});

  @override
  ConsumerState<ScreenshotFoldersWidget> createState() =>
      _ScreenshotFoldersWidgetState();
}

class _ScreenshotFoldersWidgetState
    extends ConsumerState<ScreenshotFoldersWidget> {
  final ScreenshotConfigService _configService =
      ScreenshotConfigService.instance;

  List<ScreenshotFolderItem> _folders = [];
  ScreenshotSaveStrategy _saveStrategy = ScreenshotSaveStrategy.defaultFolder;
  String? _selectedFolderId;
  int _rotationInterval = 60;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      await _configService.initialize();
      final settings = _configService.currentSettings;

      setState(() {
        _folders = settings.folderConfig.folders;
        _saveStrategy = settings.folderConfig.saveStrategy;
        _selectedFolderId = settings.folderConfig.selectedFolderId;
        _rotationInterval = settings.folderConfig.rotationIntervalMinutes;
      });
    } catch (error) {
      LoggerService.error('Failed to load folder settings', error);

      // 使用默认值作为回退
      setState(() {
        _folders = [];
        _saveStrategy = ScreenshotSaveStrategy.defaultFolder;
        _selectedFolderId = null;
        _rotationInterval = 60;
      });

      _showErrorSnackBar('加载文件夹设置失败，使用默认设置');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text('正在加载截图文件夹设置...'),
              ],
            ),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildSaveStrategySection(),
            const SizedBox(height: 16),
            _buildFoldersSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        const Icon(Icons.folder_outlined, color: Colors.blue),
        const SizedBox(width: 8),
        const Text(
          '截图文件夹管理',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: _addFolder,
          icon: const Icon(Icons.add),
          tooltip: '添加文件夹',
        ),
        IconButton(
          onPressed: _testScreenshotConfig,
          icon: const Icon(Icons.bug_report),
          tooltip: '测试截图配置',
        ),
      ],
    );
  }

  Widget _buildSaveStrategySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '保存策略',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<ScreenshotSaveStrategy>(
          value: _saveStrategy,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          items: [
            const DropdownMenuItem(
              value: ScreenshotSaveStrategy.defaultFolder,
              child: Text('保存到默认文件夹'),
            ),
            const DropdownMenuItem(
              value: ScreenshotSaveStrategy.singleFolder,
              child: Text('保存到指定文件夹'),
            ),
            const DropdownMenuItem(
              value: ScreenshotSaveStrategy.allFolders,
              child: Text('保存到所有启用的文件夹'),
            ),
            const DropdownMenuItem(
              value: ScreenshotSaveStrategy.roundRobin,
              child: Text('轮询保存到不同文件夹'),
            ),
            const DropdownMenuItem(
              value: ScreenshotSaveStrategy.timeRotation,
              child: Text('按时间轮换文件夹'),
            ),
          ],
          onChanged: (strategy) {
            if (strategy != null) {
              _updateSaveStrategy(strategy);
            }
          },
        ),
        if (_saveStrategy == ScreenshotSaveStrategy.singleFolder) ...[
          const SizedBox(height: 8),
          DropdownButtonFormField<String>(
            value: _selectedFolderId,
            decoration: const InputDecoration(
              labelText: '选择文件夹',
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
            items: _folders.where((f) => f.isEnabled).map((folder) {
              return DropdownMenuItem(
                value: folder.id,
                child: Text(folder.name),
              );
            }).toList(),
            onChanged: (folderId) {
              setState(() => _selectedFolderId = folderId);
              _updateSaveStrategy(_saveStrategy, selectedFolderId: folderId);
            },
          ),
        ],
        if (_saveStrategy == ScreenshotSaveStrategy.timeRotation) ...[
          const SizedBox(height: 8),
          Row(
            children: [
              const Text('轮换间隔: '),
              Expanded(
                child: Slider(
                  value: _rotationInterval.toDouble(),
                  min: 5,
                  max: 240,
                  divisions: 47,
                  label: '$_rotationInterval 分钟',
                  onChanged: (value) {
                    setState(() => _rotationInterval = value.round());
                  },
                  onChangeEnd: (value) {
                    _updateSaveStrategy(_saveStrategy,
                        rotationIntervalMinutes: value.round());
                  },
                ),
              ),
              Text('$_rotationInterval 分钟'),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildFoldersSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '文件夹列表',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        if (_folders.isEmpty)
          _buildEmptyState()
        else
          ..._folders.map((folder) => _buildFolderItem(folder)),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(32),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(Icons.folder_open, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            '还没有添加截图文件夹',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击右上角的"+"按钮添加第一个文件夹',
            style: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFolderItem(ScreenshotFolderItem folder) {
    final isDefault = folder.isDefault;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          folder.isEnabled ? Icons.folder : Icons.folder_off,
          color: folder.isEnabled ? Colors.blue : Colors.grey,
        ),
        title: Row(
          children: [
            Expanded(child: Text(folder.name)),
            if (isDefault) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '默认',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.blue.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              folder.path,
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 12,
              ),
            ),
            if (folder.lastUsedAt != null)
              Text(
                '最后使用: ${_formatDateTime(folder.lastUsedAt!)}',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 11,
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Switch(
              value: folder.isEnabled,
              onChanged: (enabled) => _updateFolder(folder, isEnabled: enabled),
            ),
            PopupMenuButton<String>(
              onSelected: (action) => _handleFolderAction(folder, action),
              itemBuilder: (context) => [
                if (!isDefault)
                  const PopupMenuItem(
                    value: 'setDefault',
                    child: Row(
                      children: [
                        Icon(Icons.star, size: 18),
                        SizedBox(width: 8),
                        Text('设为默认'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 18),
                      SizedBox(width: 8),
                      Text('编辑'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 18, color: Colors.red),
                      SizedBox(width: 8),
                      Text('删除', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 测试截图配置
  Future<void> _testScreenshotConfig() async {
    try {
      await _configService.initialize();
      final debugInfo = _configService.getConfigDebugInfo();
      final targetFolders = _configService.getTargetFolders();

      String message = '截图配置调试信息:\n\n';
      message += '初始化状态: ${debugInfo['isInitialized']}\n';
      message += '保存策略: ${debugInfo['saveStrategy']}\n';
      message += '总文件夹数: ${debugInfo['totalFolders']}\n';
      message += '启用文件夹数: ${debugInfo['enabledFolders']}\n';
      message += '默认文件夹: ${debugInfo['defaultFolder'] ?? '未设置'}\n';
      message += '目标文件夹数: ${targetFolders.length}\n\n';

      if (targetFolders.isNotEmpty) {
        message += '目标文件夹列表:\n';
        for (final folder in targetFolders) {
          message += '- ${folder.name}: ${folder.path}\n';
        }
      } else {
        message += '没有找到目标文件夹，将使用默认路径\n';
      }

      if (!mounted) return;

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('截图配置调试'),
          content: SingleChildScrollView(
            child: Text(message),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('关闭'),
            ),
          ],
        ),
      );
    } catch (error) {
      _showErrorSnackBar('获取调试信息失败: $error');
    }
  }

  Future<void> _addFolder() async {
    final result = await _showFolderDialog();
    if (result != null) {
      final success = await _configService.addScreenshotFolder(
        name: result['name']!,
        path: result['path']!,
        setAsDefault: result['setAsDefault'] == true,
      );

      if (success) {
        await _loadData();
        _showSuccessSnackBar('文件夹添加成功');
      } else {
        _showErrorSnackBar('文件夹添加失败');
      }
    }
  }

  Future<void> _updateFolder(
    ScreenshotFolderItem folder, {
    String? name,
    String? path,
    bool? isEnabled,
  }) async {
    final success = await _configService.updateScreenshotFolder(
      folderId: folder.id,
      name: name,
      path: path,
      isEnabled: isEnabled,
    );

    if (success) {
      await _loadData();
    } else {
      _showErrorSnackBar('文件夹更新失败');
    }
  }

  Future<void> _handleFolderAction(
      ScreenshotFolderItem folder, String action) async {
    switch (action) {
      case 'setDefault':
        final success =
            await _configService.setDefaultScreenshotFolder(folder.id);
        if (success) {
          await _loadData();
          _showSuccessSnackBar('已设置为默认文件夹');
        } else {
          _showErrorSnackBar('设置默认文件夹失败');
        }
        break;

      case 'edit':
        final result = await _showFolderDialog(folder: folder);
        if (result != null) {
          final success = await _configService.updateScreenshotFolder(
            folderId: folder.id,
            name: result['name'],
            path: result['path'],
          );

          if (success) {
            await _loadData();
            _showSuccessSnackBar('文件夹更新成功');
          } else {
            _showErrorSnackBar('文件夹更新失败');
          }
        }
        break;

      case 'delete':
        final confirmed = await _showDeleteConfirmDialog(folder.name);
        if (confirmed) {
          final success =
              await _configService.removeScreenshotFolder(folder.id);
          if (success) {
            await _loadData();
            _showSuccessSnackBar('文件夹删除成功');
          } else {
            _showErrorSnackBar('文件夹删除失败');
          }
        }
        break;
    }
  }

  Future<void> _updateSaveStrategy(
    ScreenshotSaveStrategy strategy, {
    String? selectedFolderId,
    int? rotationIntervalMinutes,
  }) async {
    setState(() => _saveStrategy = strategy);

    final success = await _configService.updateSaveStrategy(
      strategy: strategy,
      selectedFolderId: selectedFolderId ?? _selectedFolderId,
      rotationIntervalMinutes: rotationIntervalMinutes ?? _rotationInterval,
    );

    if (!success) {
      _showErrorSnackBar('保存策略更新失败');
    }
  }

  Future<Map<String, dynamic>?> _showFolderDialog(
      {ScreenshotFolderItem? folder}) async {
    final nameController = TextEditingController(text: folder?.name ?? '');
    final pathController = TextEditingController(text: folder?.path ?? '');
    bool setAsDefault = folder?.isDefault ?? false;

    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(folder == null ? '添加文件夹' : '编辑文件夹'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    decoration: const InputDecoration(
                      labelText: '文件夹名称',
                      border: OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: pathController,
                          decoration: const InputDecoration(
                            labelText: '文件夹路径',
                            border: OutlineInputBorder(),
                          ),
                          readOnly: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () async {
                          final result =
                              await FilePicker.platform.getDirectoryPath();
                          if (result != null) {
                            pathController.text = result;
                          }
                        },
                        icon: const Icon(Icons.folder_open),
                      ),
                    ],
                  ),
                  if (folder == null) ...[
                    const SizedBox(height: 16),
                    CheckboxListTile(
                      title: const Text('设为默认文件夹'),
                      value: setAsDefault,
                      onChanged: (value) {
                        setState(() => setAsDefault = value ?? false);
                      },
                      contentPadding: EdgeInsets.zero,
                    ),
                  ],
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    final name = nameController.text.trim();
                    final path = pathController.text.trim();

                    if (name.isEmpty || path.isEmpty) {
                      return;
                    }

                    if (!Directory(path).existsSync()) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('文件夹路径不存在')),
                      );
                      return;
                    }

                    Navigator.of(context).pop({
                      'name': name,
                      'path': path,
                      'setAsDefault': setAsDefault,
                    });
                  },
                  child: Text(folder == null ? '添加' : '保存'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<bool> _showDeleteConfirmDialog(String folderName) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除文件夹 "$folderName" 吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );

    return result ?? false;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-'
        '${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
