import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:getwidget/getwidget.dart';
import '../../../../core/models/image_info.dart';
import '../../../../core/services/image_management_service.dart';

class GalleryPage extends ConsumerStatefulWidget {
  const GalleryPage({super.key});

  @override
  ConsumerState<GalleryPage> createState() => _GalleryPageState();
}

class _GalleryPageState extends ConsumerState<GalleryPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _sortBy = 'name'; // name, date, size
  bool _sortAscending = true;
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        title: Text('图片库',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        iconTheme:
            IconThemeData(color: Theme.of(context).colorScheme.onSurface),
        actions: [
          IconButton(
            icon: Icon(_isGridView ? Icons.list : Icons.grid_view,
                color: Theme.of(context).colorScheme.onSurface),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert,
                color: Theme.of(context).colorScheme.onSurface),
            color: Theme.of(context).colorScheme.surface,
            onSelected: (value) {
              setState(() {
                if (value.startsWith('sort_')) {
                  _sortBy = value.substring(5);
                } else if (value == 'ascending') {
                  _sortAscending = true;
                } else if (value == 'descending') {
                  _sortAscending = false;
                }
              });
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'sort_name',
                child: Row(
                  children: [
                    Icon(
                        _sortBy == 'name'
                            ? Icons.radio_button_checked
                            : Icons.radio_button_unchecked,
                        color: Theme.of(context).colorScheme.onSurface),
                    const SizedBox(width: 8),
                    Text('按名称排序',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'sort_date',
                child: Row(
                  children: [
                    Icon(
                        _sortBy == 'date'
                            ? Icons.radio_button_checked
                            : Icons.radio_button_unchecked,
                        color: Theme.of(context).colorScheme.onSurface),
                    const SizedBox(width: 8),
                    Text('按日期排序',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'sort_size',
                child: Row(
                  children: [
                    Icon(
                        _sortBy == 'size'
                            ? Icons.radio_button_checked
                            : Icons.radio_button_unchecked,
                        color: Theme.of(context).colorScheme.onSurface),
                    const SizedBox(width: 8),
                    Text('按大小排序',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface)),
                  ],
                ),
              ),
              const PopupMenuDivider(),
              PopupMenuItem(
                value: 'ascending',
                child: Row(
                  children: [
                    Icon(
                        _sortAscending
                            ? Icons.radio_button_checked
                            : Icons.radio_button_unchecked,
                        color: Theme.of(context).colorScheme.onSurface),
                    const SizedBox(width: 8),
                    Text('升序',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface)),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'descending',
                child: Row(
                  children: [
                    Icon(
                        !_sortAscending
                            ? Icons.radio_button_checked
                            : Icons.radio_button_unchecked,
                        color: Theme.of(context).colorScheme.onSurface),
                    const SizedBox(width: 8),
                    Text('降序',
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurface)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Padding(
            padding: const EdgeInsets.all(8),
            child: GFCard(
              color: Theme.of(context).colorScheme.surface,
              elevation: 4,
              content: Padding(
                padding: const EdgeInsets.all(12),
                child: TextField(
                  controller: _searchController,
                  style:
                      TextStyle(color: Theme.of(context).colorScheme.onSurface),
                  decoration: InputDecoration(
                    hintText: '搜索图片...',
                    hintStyle: TextStyle(
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6)),
                    prefixIcon: Icon(Icons.search,
                        color: Theme.of(context)
                            .colorScheme
                            .onSurface
                            .withOpacity(0.6)),
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: Icon(Icons.clear,
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withOpacity(0.6)),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                    border: InputBorder.none,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ),
          ),
          // 图片列表
          Expanded(
            child: Builder(
              builder: (context) {
                final imageService = ref.watch(imageManagementServiceProvider);
                final filteredImages =
                    _filterAndSortImages(imageService.images);

                if (filteredImages.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.photo_library_outlined,
                          size: 64,
                          color: Theme.of(context)
                              .colorScheme
                              .onSurface
                              .withOpacity(0.4),
                        ),
                        const SizedBox(height: 12),
                        Text(
                          _searchQuery.isNotEmpty ? '未找到匹配的图片' : '暂无图片',
                          style: TextStyle(
                            fontSize: 18,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 6),
                        Text(
                          '点击添加按钮导入图片',
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.4),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return _isGridView
                    ? _buildGridView(filteredImages)
                    : _buildListView(filteredImages);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        onPressed: _addImages,
        child: const Icon(Icons.add),
      ),
    );
  }

  List<AppImageInfo> _filterAndSortImages(List<AppImageInfo> images) {
    // 过滤
    List<AppImageInfo> filtered = images;
    if (_searchQuery.isNotEmpty) {
      filtered = images.where((image) {
        return image.name.toLowerCase().contains(_searchQuery.toLowerCase());
      }).toList();
    }

    // 排序
    filtered.sort((a, b) {
      int comparison;
      switch (_sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'date':
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case 'size':
          comparison = a.size.compareTo(b.size);
          break;
        default:
          comparison = 0;
      }
      return _sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  Widget _buildGridView(List<AppImageInfo> images) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1,
      ),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final image = images[index];
        return _buildImageCard(image, index);
      },
    );
  }

  Widget _buildListView(List<AppImageInfo> images) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: images.length,
      itemBuilder: (context, index) {
        final image = images[index];
        return _buildImageListTile(image, index);
      },
    );
  }

  Widget _buildImageCard(AppImageInfo image, int index) {
    return GFCard(
      color: const Color(0xFF2A2A2A),
      elevation: 4,
      clipBehavior: Clip.antiAlias,
      content: InkWell(
        onTap: () => _showImageDetails(image, index),
        onLongPress: () => _showImageOptions(image),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Image.file(
              File(image.filePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                  ),
                );
              },
            ),
            Positioned(
              top: 4,
              right: 4,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: const BoxDecoration(
                  color: Color.fromRGBO(0, 0, 0, 0.7),
                  borderRadius: BorderRadius.all(Radius.circular(4)),
                ),
                child: Text(
                  '${index + 1}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                    colors: [
                      Color.fromRGBO(0, 0, 0, 0.8),
                      Colors.transparent,
                    ],
                  ),
                ),
                child: Text(
                  image.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageListTile(AppImageInfo image, int index) {
    return GFCard(
      color: const Color(0xFF2A2A2A),
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 8),
      content: ListTile(
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: SizedBox(
            width: 60,
            height: 60,
            child: Image.file(
              File(image.filePath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey[300],
                  child: const Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                  ),
                );
              },
            ),
          ),
        ),
        title: Text(
          image.name,
          style:
              const TextStyle(fontWeight: FontWeight.bold, color: Colors.white),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('大小: ${_formatFileSize(image.size)}',
                style: const TextStyle(color: Colors.grey)),
            Text('创建时间: ${_formatDateTime(image.createdAt)}',
                style: const TextStyle(color: Colors.grey)),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 查看按钮
            IconButton(
              icon: Icon(Icons.visibility,
                  color: Theme.of(context).colorScheme.onSurface),
              onPressed: () => _handleImageAction(image, 'view'),
              tooltip: '查看',
            ),
            // 重命名按钮
            IconButton(
              icon: Icon(Icons.edit,
                  color: Theme.of(context).colorScheme.onSurface),
              onPressed: () => _handleImageAction(image, 'rename'),
              tooltip: '重命名',
            ),
            // 删除按钮
            IconButton(
              icon: Icon(Icons.delete,
                  color: Theme.of(context).colorScheme.error),
              onPressed: () => _handleImageAction(image, 'delete'),
              tooltip: '删除',
            ),
          ],
        ),
        onTap: () => _showImageDetails(image, index),
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _addImages() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final imageService = ref.read(imageManagementServiceProvider);

        for (final file in result.files) {
          if (file.bytes != null) {
            await imageService.addImage(
              file.name,
              file.bytes!,
            );
          }
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('成功添加 ${result.files.length} 张图片',
                  style: TextStyle(
                      color: Theme.of(context).colorScheme.onPrimary)),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('添加图片失败: $e',
                style: TextStyle(color: Theme.of(context).colorScheme.onError)),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _showImageDetails(AppImageInfo image, int index) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _ImageDetailPage(image: image, index: index),
      ),
    );
  }

  void _showImageOptions(AppImageInfo image) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Theme.of(context).colorScheme.surface,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(Icons.visibility,
                color: Theme.of(context).colorScheme.onSurface),
            title: Text('查看',
                style:
                    TextStyle(color: Theme.of(context).colorScheme.onSurface)),
            onTap: () {
              Navigator.pop(context);
              _handleImageAction(image, 'view');
            },
          ),
          ListTile(
            leading: Icon(Icons.edit,
                color: Theme.of(context).colorScheme.onSurface),
            title: Text('重命名',
                style:
                    TextStyle(color: Theme.of(context).colorScheme.onSurface)),
            onTap: () {
              Navigator.pop(context);
              _handleImageAction(image, 'rename');
            },
          ),
          ListTile(
            leading:
                Icon(Icons.delete, color: Theme.of(context).colorScheme.error),
            title: Text('删除',
                style: TextStyle(color: Theme.of(context).colorScheme.error)),
            onTap: () {
              Navigator.pop(context);
              _handleImageAction(image, 'delete');
            },
          ),
        ],
      ),
    );
  }

  Future<void> _handleImageAction(AppImageInfo image, String action) async {
    try {
      switch (action) {
        case 'view':
          _showImageDetails(image, 0);
          break;
        case 'rename':
          await _renameImage(image);
          break;
        case 'delete':
          await _confirmDeleteImage(image);
          break;
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e',
                style: TextStyle(color: Theme.of(context).colorScheme.onError)),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _renameImage(AppImageInfo image) async {
    final newName = await showDialog<String>(
      context: context,
      builder: (context) => _RenameDialog(
        currentName: image.name,
        onRename: (newName) {
          // This callback is handled in the dialog itself
        },
      ),
    );

    if (newName != null && newName.isNotEmpty && newName != image.name) {
      final imageService = ref.read(imageManagementServiceProvider);
      await imageService.renameImage(image.id, newName);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('重命名成功',
                style:
                    TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    }
  }

  Future<void> _confirmDeleteImage(AppImageInfo image) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).colorScheme.surface,
        title: Text('确认删除',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        content: Text('确定要删除图片 "${image.name}" 吗？',
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('取消',
                style: TextStyle(
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.6))),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final imageService = ref.read(imageManagementServiceProvider);
      await imageService.removeImage(image.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除成功',
                style:
                    TextStyle(color: Theme.of(context).colorScheme.onPrimary)),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    }
  }
}

class _ImageDetailPage extends StatelessWidget {
  final AppImageInfo image;
  final int index;

  const _ImageDetailPage({
    required this.image,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        title: Text(image.name,
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
        actions: [
          IconButton(
            icon: Icon(Icons.share,
                color: Theme.of(context).colorScheme.onSurface),
            onPressed: () {
              // TODO: 实现分享功能
            },
          ),
        ],
      ),
      body: Center(
        child: InteractiveViewer(
          child: Image.file(
            File(image.filePath),
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('无法加载图片',
                      style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface)),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}

class _RenameDialog extends StatefulWidget {
  final String currentName;
  final Function(String) onRename;

  const _RenameDialog({
    required this.currentName,
    required this.onRename,
  });

  @override
  State<_RenameDialog> createState() => _RenameDialogState();
}

class _RenameDialogState extends State<_RenameDialog> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.currentName);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Theme.of(context).colorScheme.surface,
      title: Text('重命名图片',
          style: TextStyle(color: Theme.of(context).colorScheme.onSurface)),
      content: TextField(
        controller: _controller,
        style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
        decoration: InputDecoration(
          labelText: '图片名称',
          labelStyle: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
          hintText: '请输入新的图片名称',
          hintStyle: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6)),
          enabledBorder: UnderlineInputBorder(
            borderSide:
                BorderSide(color: Theme.of(context).colorScheme.outline),
          ),
          focusedBorder: UnderlineInputBorder(
            borderSide:
                BorderSide(color: Theme.of(context).colorScheme.primary),
          ),
        ),
        autofocus: true,
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text('取消',
              style: TextStyle(
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withOpacity(0.6))),
        ),
        TextButton(
          onPressed: () {
            final newName = _controller.text.trim();
            if (newName.isNotEmpty && newName != widget.currentName) {
              widget.onRename(newName);
            }
            Navigator.pop(context);
          },
          style: TextButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
          ),
          child: const Text('确定'),
        ),
      ],
    );
  }
}
