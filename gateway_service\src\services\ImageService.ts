import sharp from 'sharp';
import fs from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { databaseService } from './DatabaseService';
import { WebSocketService } from './WebSocketService';

interface ImageMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  density?: number;
  hasAlpha?: boolean;
  channels: number;
}

interface ProcessingOptions {
  generateThumbnail?: boolean;
  thumbnailSize?: { width: number; height: number };
  quality?: number;
  format?: 'jpeg' | 'png' | 'webp';
  resize?: { width?: number; height?: number; fit?: 'cover' | 'contain' | 'fill' };
}

interface ImageRecord {
  id: string;
  filename: string;
  originalName: string;
  deviceId: string;
  taskId?: string;
  fileSize: number;
  format: string;
  metadata: ImageMetadata;
  thumbnailPath?: string;
  createdAt: Date;
}

class ImageService {
  private static instance: ImageService;
  private uploadsDir: string;
  private thumbnailsDir: string;

  private constructor() {
    this.uploadsDir = path.join(process.cwd(), 'uploads', 'images');
    this.thumbnailsDir = path.join(process.cwd(), 'uploads', 'thumbnails');
  }

  public static getInstance(): ImageService {
    if (!ImageService.instance) {
      ImageService.instance = new ImageService();
    }
    return ImageService.instance;
  }

  public async initialize(): Promise<void> {
    try {
      // Ensure upload directories exist
      await fs.mkdir(this.uploadsDir, { recursive: true });
      await fs.mkdir(this.thumbnailsDir, { recursive: true });
      
      console.log('Image service initialized');
      console.log(`Images directory: ${this.uploadsDir}`);
      console.log(`Thumbnails directory: ${this.thumbnailsDir}`);
    } catch (error) {
      console.error('Failed to initialize image service:', error);
      throw error;
    }
  }

  public async processAndSaveImage(
    buffer: Buffer,
    originalName: string,
    deviceId: string,
    taskId?: string,
    options: ProcessingOptions = {}
  ): Promise<ImageRecord> {
    try {
      const imageId = uuidv4();
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const ext = path.extname(originalName).toLowerCase() || '.jpg';
      const filename = `${deviceId}_${timestamp}_${imageId}${ext}`;
      const filePath = path.join(this.uploadsDir, filename);

      // Get image metadata
      const sharpInstance = sharp(buffer);
      const metadata = await sharpInstance.metadata();
      
      const imageMetadata: ImageMetadata = {
        width: metadata.width || 0,
        height: metadata.height || 0,
        format: metadata.format || 'unknown',
        size: buffer.length,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha,
        channels: metadata.channels || 3
      };

      // Process image based on options
      let processedImage = sharpInstance;
      
      if (options.resize) {
        processedImage = processedImage.resize({
          width: options.resize.width,
          height: options.resize.height,
          fit: options.resize.fit || 'contain',
          withoutEnlargement: true
        });
      }

      if (options.format) {
        switch (options.format) {
          case 'jpeg':
            processedImage = processedImage.jpeg({ quality: options.quality || 90 });
            break;
          case 'png':
            processedImage = processedImage.png({ quality: options.quality || 90 });
            break;
          case 'webp':
            processedImage = processedImage.webp({ quality: options.quality || 90 });
            break;
        }
      }

      // Save processed image
      await processedImage.toFile(filePath);

      // Generate thumbnail if requested
      let thumbnailPath: string | undefined;
      if (options.generateThumbnail !== false) {
        thumbnailPath = await this.generateThumbnail(
          buffer,
          imageId,
          options.thumbnailSize || { width: 200, height: 200 }
        );
      }

      // Get final file size
      const stats = await fs.stat(filePath);
      const finalFileSize = stats.size;

      // Create image record
      const imageRecord: ImageRecord = {
        id: imageId,
        filename,
        originalName,
        deviceId,
        taskId,
        fileSize: finalFileSize,
        format: imageMetadata.format,
        metadata: imageMetadata,
        thumbnailPath,
        createdAt: new Date()
      };

      // Save to database
      await databaseService.query(
        `INSERT INTO images (id, filename, original_name, device_id, task_id, file_size, format, metadata, thumbnail_path) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          imageRecord.id,
          imageRecord.filename,
          imageRecord.originalName,
          imageRecord.deviceId,
          imageRecord.taskId,
          imageRecord.fileSize,
          imageRecord.format,
          JSON.stringify(imageRecord.metadata),
          imageRecord.thumbnailPath
        ]
      );

      // Notify clients about new image
      const wsService = WebSocketService.getInstance();
      wsService.broadcastToClients('image:uploaded', {
        image: {
          id: imageRecord.id,
          filename: imageRecord.filename,
          originalName: imageRecord.originalName,
          deviceId: imageRecord.deviceId,
          taskId: imageRecord.taskId,
          fileSize: imageRecord.fileSize,
          format: imageRecord.format,
          metadata: imageRecord.metadata,
          hasThumbnail: !!thumbnailPath,
          createdAt: imageRecord.createdAt
        }
      });

      console.log(`Image processed and saved: ${filename} (${finalFileSize} bytes)`);
      return imageRecord;
    } catch (error) {
      console.error('Error processing and saving image:', error);
      throw error;
    }
  }

  private async generateThumbnail(
    buffer: Buffer,
    imageId: string,
    size: { width: number; height: number }
  ): Promise<string> {
    try {
      const thumbnailFilename = `thumb_${imageId}.jpg`;
      const thumbnailPath = path.join(this.thumbnailsDir, thumbnailFilename);

      await sharp(buffer)
        .resize(size.width, size.height, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ quality: 80 })
        .toFile(thumbnailPath);

      return thumbnailFilename;
    } catch (error) {
      console.error('Error generating thumbnail:', error);
      throw error;
    }
  }

  public async getImageById(imageId: string): Promise<ImageRecord | null> {
    try {
      const result = await databaseService.query(
        'SELECT * FROM images WHERE id = ?',
        [imageId]
      );

      if (result.length === 0) {
        return null;
      }

      const row = result[0];
      return {
        id: row.id,
        filename: row.filename,
        originalName: row.original_name,
        deviceId: row.device_id,
        taskId: row.task_id,
        fileSize: row.file_size,
        format: row.format,
        metadata: JSON.parse(row.metadata),
        thumbnailPath: row.thumbnail_path,
        createdAt: new Date(row.created_at)
      };
    } catch (error) {
      console.error('Error getting image by ID:', error);
      throw error;
    }
  }

  public async getImageFile(imageId: string): Promise<Buffer | null> {
    try {
      const imageRecord = await this.getImageById(imageId);
      if (!imageRecord) {
        return null;
      }

      const filePath = path.join(this.uploadsDir, imageRecord.filename);
      return await fs.readFile(filePath);
    } catch (error) {
      console.error('Error getting image file:', error);
      return null;
    }
  }

  public async getThumbnailFile(imageId: string): Promise<Buffer | null> {
    try {
      const imageRecord = await this.getImageById(imageId);
      if (!imageRecord || !imageRecord.thumbnailPath) {
        return null;
      }

      const thumbnailPath = path.join(this.thumbnailsDir, imageRecord.thumbnailPath);
      return await fs.readFile(thumbnailPath);
    } catch (error) {
      console.error('Error getting thumbnail file:', error);
      return null;
    }
  }

  public async getImagesByDevice(
    deviceId: string,
    limit: number = 50,
    offset: number = 0,
    sortBy: 'created_at' | 'file_size' | 'original_name' = 'created_at',
    sortOrder: 'ASC' | 'DESC' = 'DESC'
  ): Promise<ImageRecord[]> {
    try {
      const result = await databaseService.query(
        `SELECT * FROM images WHERE device_id = ? 
         ORDER BY ${sortBy} ${sortOrder} 
         LIMIT ? OFFSET ?`,
        [deviceId, limit, offset]
      );

      return result.map(row => ({
        id: row.id,
        filename: row.filename,
        originalName: row.original_name,
        deviceId: row.device_id,
        taskId: row.task_id,
        fileSize: row.file_size,
        format: row.format,
        metadata: JSON.parse(row.metadata),
        thumbnailPath: row.thumbnail_path,
        createdAt: new Date(row.created_at)
      }));
    } catch (error) {
      console.error('Error getting images by device:', error);
      throw error;
    }
  }

  public async searchImages(
    query: {
      deviceId?: string;
      format?: string;
      dateFrom?: Date;
      dateTo?: Date;
      minSize?: number;
      maxSize?: number;
      keyword?: string;
    },
    limit: number = 50,
    offset: number = 0
  ): Promise<ImageRecord[]> {
    try {
      let sql = 'SELECT * FROM images WHERE 1=1';
      const params: any[] = [];

      if (query.deviceId) {
        sql += ' AND device_id = ?';
        params.push(query.deviceId);
      }

      if (query.format) {
        sql += ' AND format = ?';
        params.push(query.format);
      }

      if (query.dateFrom) {
        sql += ' AND created_at >= ?';
        params.push(query.dateFrom.toISOString());
      }

      if (query.dateTo) {
        sql += ' AND created_at <= ?';
        params.push(query.dateTo.toISOString());
      }

      if (query.minSize) {
        sql += ' AND file_size >= ?';
        params.push(query.minSize);
      }

      if (query.maxSize) {
        sql += ' AND file_size <= ?';
        params.push(query.maxSize);
      }

      if (query.keyword) {
        sql += ' AND (original_name LIKE ? OR filename LIKE ?)';
        const keywordPattern = `%${query.keyword}%`;
        params.push(keywordPattern, keywordPattern);
      }

      sql += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
      params.push(limit, offset);

      const result = await databaseService.query(sql, params);

      return result.map(row => ({
        id: row.id,
        filename: row.filename,
        originalName: row.original_name,
        deviceId: row.device_id,
        taskId: row.task_id,
        fileSize: row.file_size,
        format: row.format,
        metadata: JSON.parse(row.metadata),
        thumbnailPath: row.thumbnail_path,
        createdAt: new Date(row.created_at)
      }));
    } catch (error) {
      console.error('Error searching images:', error);
      throw error;
    }
  }

  public async deleteImage(imageId: string): Promise<boolean> {
    try {
      const imageRecord = await this.getImageById(imageId);
      if (!imageRecord) {
        return false;
      }

      // Delete files
      const imagePath = path.join(this.uploadsDir, imageRecord.filename);
      try {
        await fs.unlink(imagePath);
      } catch (error) {
        console.warn(`Failed to delete image file: ${imagePath}`, error);
      }

      if (imageRecord.thumbnailPath) {
        const thumbnailPath = path.join(this.thumbnailsDir, imageRecord.thumbnailPath);
        try {
          await fs.unlink(thumbnailPath);
        } catch (error) {
          console.warn(`Failed to delete thumbnail file: ${thumbnailPath}`, error);
        }
      }

      // Delete from database
      await databaseService.query(
        'DELETE FROM images WHERE id = ?',
        [imageId]
      );

      // Notify clients
      const wsService = WebSocketService.getInstance();
      wsService.broadcastToClients('image:deleted', {
        imageId,
        deviceId: imageRecord.deviceId
      });

      console.log(`Image deleted: ${imageRecord.filename}`);
      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
  }

  public async deleteImagesByDevice(deviceId: string): Promise<number> {
    try {
      const images = await this.getImagesByDevice(deviceId, 1000); // Get all images
      let deletedCount = 0;

      for (const image of images) {
        const success = await this.deleteImage(image.id);
        if (success) {
          deletedCount++;
        }
      }

      console.log(`Deleted ${deletedCount} images for device: ${deviceId}`);
      return deletedCount;
    } catch (error) {
      console.error('Error deleting images by device:', error);
      throw error;
    }
  }

  public async getImageStats(): Promise<{
    totalImages: number;
    totalSize: number;
    averageSize: number;
    formatDistribution: Record<string, number>;
    deviceDistribution: Record<string, number>;
  }> {
    try {
      const [totalResult, formatResult, deviceResult] = await Promise.all([
        databaseService.query('SELECT COUNT(*) as count, SUM(file_size) as total_size, AVG(file_size) as avg_size FROM images'),
        databaseService.query('SELECT format, COUNT(*) as count FROM images GROUP BY format'),
        databaseService.query('SELECT device_id, COUNT(*) as count FROM images GROUP BY device_id')
      ]);

      const formatDistribution: Record<string, number> = {};
      formatResult.forEach(row => {
        formatDistribution[row.format] = row.count;
      });

      const deviceDistribution: Record<string, number> = {};
      deviceResult.forEach(row => {
        deviceDistribution[row.device_id] = row.count;
      });

      return {
        totalImages: totalResult[0].count || 0,
        totalSize: totalResult[0].total_size || 0,
        averageSize: totalResult[0].avg_size || 0,
        formatDistribution,
        deviceDistribution
      };
    } catch (error) {
      console.error('Error getting image stats:', error);
      throw error;
    }
  }

  public async cleanupOrphanedFiles(): Promise<{ deletedImages: number; deletedThumbnails: number }> {
    try {
      let deletedImages = 0;
      let deletedThumbnails = 0;

      // Get all files in uploads directory
      const imageFiles = await fs.readdir(this.uploadsDir);
      const thumbnailFiles = await fs.readdir(this.thumbnailsDir);

      // Get all filenames from database
      const dbImages = await databaseService.query('SELECT filename, thumbnail_path FROM images');
      const dbFilenames = new Set(dbImages.map(img => img.filename));
      const dbThumbnails = new Set(dbImages.map(img => img.thumbnail_path).filter(Boolean));

      // Delete orphaned image files
      for (const file of imageFiles) {
        if (!dbFilenames.has(file)) {
          try {
            await fs.unlink(path.join(this.uploadsDir, file));
            deletedImages++;
            console.log(`Deleted orphaned image file: ${file}`);
          } catch (error) {
            console.warn(`Failed to delete orphaned image file: ${file}`, error);
          }
        }
      }

      // Delete orphaned thumbnail files
      for (const file of thumbnailFiles) {
        if (!dbThumbnails.has(file)) {
          try {
            await fs.unlink(path.join(this.thumbnailsDir, file));
            deletedThumbnails++;
            console.log(`Deleted orphaned thumbnail file: ${file}`);
          } catch (error) {
            console.warn(`Failed to delete orphaned thumbnail file: ${file}`, error);
          }
        }
      }

      console.log(`Cleanup completed: ${deletedImages} images, ${deletedThumbnails} thumbnails deleted`);
      return { deletedImages, deletedThumbnails };
    } catch (error) {
      console.error('Error during cleanup:', error);
      throw error;
    }
  }

  public getUploadsPath(): string {
    return this.uploadsDir;
  }

  public getThumbnailsPath(): string {
    return this.thumbnailsDir;
  }
}

// Export singleton instance
export const imageService = ImageService.getInstance();
export default imageService;
export { ImageRecord, ImageMetadata };