import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/splash_page.dart';
import '../../features/dashboard/presentation/pages/dashboard_page.dart';
import '../../features/devices/presentation/pages/devices_page.dart';
import '../../features/gallery/presentation/pages/gallery_page.dart';
import '../../features/settings/presentation/pages/settings_page.dart';

/// 应用路由配置类
class AppRoutes {
  // 路由路径常量
  static const String splash = '/';
  static const String login = '/login';
  static const String dashboard = '/dashboard';
  static const String devices = '/devices';
  static const String gallery = '/gallery';
  static const String settings = '/settings';

  /// 路由配置
  static final GoRouter router = GoRouter(
    initialLocation: splash,
    debugLogDiagnostics: true,
    routes: [
      // 启动页
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // 登录页
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),

      // 主要功能页面
      ShellRoute(
        builder: (context, state, child) {
          return MainShell(child: child);
        },
        routes: [
          // 仪表板
          GoRoute(
            path: dashboard,
            name: 'dashboard',
            builder: (context, state) => const DashboardPage(),
          ),

          // 设备管理
          GoRoute(
            path: devices,
            name: 'devices',
            builder: (context, state) => const DevicesPage(),
          ),

          // 图片库
          GoRoute(
            path: gallery,
            name: 'gallery',
            builder: (context, state) => const GalleryPage(),
          ),

          // 设置
          GoRoute(
            path: settings,
            name: 'settings',
            builder: (context, state) => const SettingsPage(),
          ),
        ],
      ),
    ],

    // 错误页面
    errorBuilder: (context, state) => ErrorPage(
      error: state.error.toString(),
    ),

    // 路由重定向逻辑
    redirect: (context, state) {
      // 这里可以添加认证检查逻辑
      // 例如：检查用户是否已登录
      return null; // 暂时不重定向
    },
  );
}

/// 主应用外壳 - 包含导航栏等公共UI
class MainShell extends StatelessWidget {
  final Widget child;

  const MainShell({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // GetWidget 侧边导航栏
          Container(
            width: 280,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.blue,
                  Colors.blue.withOpacity(0.8),
                ],
              ),
            ),
            child: Column(
              children: [
                // 头部区域
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const CircleAvatar(
                        backgroundColor: Colors.white,
                        radius: 30,
                        child: Icon(
                          Icons.control_camera,
                          color: Colors.blue,
                          size: 30,
                        ),
                      ),
                      const SizedBox(height: 12),
                      const Text(
                        'Control App',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '图片控制中心',
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),

                // 导航项
                Expanded(
                  child: Column(
                    children: [
                      _buildNavItem(
                        context,
                        icon: Icons.dashboard,
                        title: '仪表板',
                        subtitle: '系统概览',
                        index: 0,
                      ),
                      _buildNavItem(
                        context,
                        icon: Icons.devices,
                        title: '设备管理',
                        subtitle: '终端设备控制',
                        index: 1,
                      ),
                      _buildNavItem(
                        context,
                        icon: Icons.photo_library,
                        title: '图片库',
                        subtitle: '图片资源管理',
                        index: 2,
                      ),
                      _buildNavItem(
                        context,
                        icon: Icons.settings,
                        title: '系统设置',
                        subtitle: '应用配置管理',
                        index: 3,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const VerticalDivider(thickness: 1, width: 1),

          // 主内容区域
          Expanded(child: child),
        ],
      ),
    );
  }

  /// 获取当前选中的导航项索引
  int _getSelectedIndex(BuildContext context) {
    final location =
        GoRouter.of(context).routerDelegate.currentConfiguration.uri.toString();
    switch (location) {
      case AppRoutes.dashboard:
        return 0;
      case AppRoutes.devices:
        return 1;
      case AppRoutes.gallery:
        return 2;
      case AppRoutes.settings:
        return 3;
      default:
        return 0;
    }
  }

  /// 处理导航项选择
  void _onDestinationSelected(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go(AppRoutes.dashboard);
        break;
      case 1:
        context.go(AppRoutes.devices);
        break;
      case 2:
        context.go(AppRoutes.gallery);
        break;
      case 3:
        context.go(AppRoutes.settings);
        break;
    }
  }

  Widget _buildNavItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required int index,
  }) {
    final isSelected = _getSelectedIndex(context) == index;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor:
              isSelected ? Colors.white.withOpacity(0.2) : Colors.transparent,
          radius: 20,
          child: Icon(
            icon,
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.7),
            size: 24,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.9),
            fontSize: 16,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(
            color: Colors.white.withOpacity(0.6),
            fontSize: 12,
          ),
        ),
        tileColor:
            isSelected ? Colors.white.withOpacity(0.1) : Colors.transparent,
        onTap: () => _onDestinationSelected(context, index),
        contentPadding:
            const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }
}

/// 错误页面
class ErrorPage extends StatelessWidget {
  final String error;

  const ErrorPage({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('页面错误'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '页面加载出错',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => context.go(AppRoutes.dashboard),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}

/// 路由扩展方法
extension AppRouterExtension on BuildContext {
  /// 导航到仪表板
  void goToDashboard() => go(AppRoutes.dashboard);

  /// 导航到设备页面
  void goToDevices() => go(AppRoutes.devices);

  /// 导航到图片库
  void goToGallery() => go(AppRoutes.gallery);

  /// 导航到设置页面
  void goToSettings() => go(AppRoutes.settings);

  /// 导航到登录页面
  void goToLogin() => go(AppRoutes.login);
}
