import path from 'path';
import { GatewayConfig } from '../models';

// Environment variables with defaults
const getEnvVar = (key: string, defaultValue: string): string => {
  return process.env[key] || defaultValue;
};

const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = process.env[key];
  return value ? parseInt(value, 10) : defaultValue;
};

const getEnvBoolean = (key: string, defaultValue: boolean): boolean => {
  const value = process.env[key];
  return value ? value.toLowerCase() === 'true' : defaultValue;
};

// Main configuration object
export const config: GatewayConfig = {
  server: {
    port: getEnvNumber('PORT', 8080),
    host: getEnvVar('HOST', '0.0.0.0'),
    cors: {
      origin: getEnvVar('CORS_ORIGIN', '*'),
      credentials: getEnvBoolean('CORS_CREDENTIALS', true)
    }
  },
  
  database: {
    path: getEnvVar('DATABASE_PATH', path.join(process.cwd(), 'data', 'imgct.db')),
    backupInterval: getEnvNumber('DB_BACKUP_INTERVAL', 24 * 60 * 60 * 1000) // 24 hours
  },
  
  websocket: {
    transports: ['websocket', 'polling'],
    pingTimeout: getEnvNumber('WS_PING_TIMEOUT', 60000),
    pingInterval: getEnvNumber('WS_PING_INTERVAL', 25000)
  },
  
  discovery: {
    port: getEnvNumber('DISCOVERY_PORT', 8889),
    broadcastInterval: getEnvNumber('DISCOVERY_BROADCAST_INTERVAL', 30000),
    deviceTimeout: getEnvNumber('DISCOVERY_DEVICE_TIMEOUT', 300000) // 5 minutes
  },
  
  upload: {
    maxFileSize: getEnvNumber('UPLOAD_MAX_FILE_SIZE', 50 * 1024 * 1024), // 50MB
    allowedFormats: getEnvVar('UPLOAD_ALLOWED_FORMATS', 'jpeg,jpg,png,webp,gif,bmp,tiff').split(','),
    thumbnailSize: {
      width: getEnvNumber('THUMBNAIL_WIDTH', 200),
      height: getEnvNumber('THUMBNAIL_HEIGHT', 200)
    }
  },
  
  security: {
    jwtSecret: getEnvVar('JWT_SECRET', 'imgct_default_secret_change_in_production'),
    jwtExpiration: getEnvVar('JWT_EXPIRATION', '24h'),
    rateLimiting: {
      windowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 15 * 60 * 1000), // 15 minutes
      max: getEnvNumber('RATE_LIMIT_MAX', 100) // limit each IP to 100 requests per windowMs
    }
  }
};

// Validation function
export const validateConfig = (): void => {
  const errors: string[] = [];
  
  // Validate server configuration
  if (config.server.port < 1 || config.server.port > 65535) {
    errors.push('Server port must be between 1 and 65535');
  }
  
  // Validate discovery port
  if (config.discovery.port < 1 || config.discovery.port > 65535) {
    errors.push('Discovery port must be between 1 and 65535');
  }
  
  // Validate upload settings
  if (config.upload.maxFileSize < 1024) {
    errors.push('Max file size must be at least 1KB');
  }
  
  if (config.upload.allowedFormats.length === 0) {
    errors.push('At least one file format must be allowed');
  }
  
  // Validate thumbnail size
  if (config.upload.thumbnailSize.width < 50 || config.upload.thumbnailSize.height < 50) {
    errors.push('Thumbnail size must be at least 50x50 pixels');
  }
  
  // Validate JWT secret in production
  if (process.env.NODE_ENV === 'production' && config.security.jwtSecret === 'imgct_default_secret_change_in_production') {
    errors.push('JWT secret must be changed in production environment');
  }
  
  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }
};

// Environment-specific configurations
export const isDevelopment = process.env.NODE_ENV === 'development';
export const isProduction = process.env.NODE_ENV === 'production';
export const isTest = process.env.NODE_ENV === 'test';

// Logging configuration
export const logConfig = {
  level: getEnvVar('LOG_LEVEL', isDevelopment ? 'debug' : 'info'),
  format: getEnvVar('LOG_FORMAT', isDevelopment ? 'dev' : 'combined'),
  file: {
    enabled: getEnvBoolean('LOG_FILE_ENABLED', isProduction),
    path: getEnvVar('LOG_FILE_PATH', path.join(process.cwd(), 'logs')),
    maxSize: getEnvVar('LOG_FILE_MAX_SIZE', '10m'),
    maxFiles: getEnvVar('LOG_FILE_MAX_FILES', '5')
  }
};

// Database configuration helpers
export const getDatabasePath = (): string => {
  if (isTest) {
    return ':memory:'; // Use in-memory database for tests
  }
  return config.database.path;
};

// CORS configuration helper
export const getCorsOrigins = (): string | string[] => {
  const origins = config.server.cors.origin;
  if (typeof origins === 'string' && origins.includes(',')) {
    return origins.split(',').map(origin => origin.trim());
  }
  return origins;
};

// Upload directory paths
export const getUploadPaths = () => {
  const baseUploadDir = getEnvVar('UPLOAD_DIR', path.join(process.cwd(), 'uploads'));
  
  return {
    base: baseUploadDir,
    images: path.join(baseUploadDir, 'images'),
    thumbnails: path.join(baseUploadDir, 'thumbnails'),
    temp: path.join(baseUploadDir, 'temp')
  };
};

// Security headers configuration
export const securityHeaders = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "blob:"],
      connectSrc: ["'self'", "ws:", "wss:"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  },
  crossOriginEmbedderPolicy: false, // Disable for file uploads
  crossOriginOpenerPolicy: false,
  crossOriginResourcePolicy: { policy: "cross-origin" as const },
  dnsPrefetchControl: { allow: false },
  frameguard: { action: 'deny' as const },
  hidePoweredBy: true,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },
  ieNoOpen: true,
  noSniff: true,
  originAgentCluster: true,
  permittedCrossDomainPolicies: false,
  referrerPolicy: { policy: "no-referrer" as const },
  xssFilter: true
};

// Rate limiting configuration
export const rateLimitConfig = {
  // General API rate limiting
  general: {
    windowMs: config.security.rateLimiting.windowMs,
    max: config.security.rateLimiting.max,
    message: 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  },
  
  // Stricter rate limiting for authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 requests per windowMs
    message: 'Too many authentication attempts, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  },
  
  // Rate limiting for file uploads
  upload: {
    windowMs: 60 * 1000, // 1 minute
    max: 10, // limit each IP to 10 uploads per minute
    message: 'Too many file uploads, please try again later.',
    standardHeaders: true,
    legacyHeaders: false
  }
};

// WebSocket configuration
export const websocketConfig = {
  cors: {
    origin: getCorsOrigins(),
    methods: ["GET", "POST"],
    credentials: config.server.cors.credentials
  },
  transports: config.websocket.transports,
  pingTimeout: config.websocket.pingTimeout,
  pingInterval: config.websocket.pingInterval,
  maxHttpBufferSize: 1e6, // 1MB
  allowEIO3: true
};

// Export default configuration
export default config;

// Configuration summary for logging
export const getConfigSummary = () => {
  return {
    environment: process.env.NODE_ENV || 'development',
    server: {
      host: config.server.host,
      port: config.server.port
    },
    database: {
      path: getDatabasePath()
    },
    discovery: {
      port: config.discovery.port
    },
    upload: {
      maxFileSize: `${Math.round(config.upload.maxFileSize / 1024 / 1024)}MB`,
      allowedFormats: config.upload.allowedFormats.join(', ')
    },
    security: {
      jwtExpiration: config.security.jwtExpiration,
      rateLimitMax: config.security.rateLimiting.max
    }
  };
};