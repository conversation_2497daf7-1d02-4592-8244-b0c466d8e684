import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/app_config_service.dart';
import '../services/device_service.dart';
import '../services/screenshot_service.dart';
import '../services/websocket_service.dart';
import '../services/logger_service.dart';
import '../services/device_registration_service.dart';
import '../services/screenshot_log_service.dart';

/// 应用配置服务提供者
final appConfigServiceProvider = Provider<AppConfigService>((ref) {
  return AppConfigService.instance;
});

/// 设备服务提供者
final deviceServiceProvider = Provider<DeviceService>((ref) {
  return DeviceService.instance;
});

/// 截图服务提供者
final screenshotServiceProvider = Provider<ScreenshotService>((ref) {
  return ScreenshotService.instance;
});

/// WebSocket服务提供者
final webSocketServiceProvider = Provider<WebSocketService>((ref) {
  return WebSocketService.instance;
});

/// 日志服务提供者
final loggerServiceProvider = Provider<LoggerService>((ref) {
  return LoggerService.instance;
});

/// 设备注册服务提供者
final deviceRegistrationServiceProvider =
    Provider<DeviceRegistrationService>((ref) {
  return DeviceRegistrationService();
});

/// 截图日志服务提供者
final screenshotLogServiceProvider = Provider<ScreenshotLogService>((ref) {
  return ScreenshotLogService.instance;
});
