# Flutter 网关服务 - 实时日志功能使用指南

## 🎯 功能概述

增强版网关服务现在支持完善的实时日志功能，帮助开发者和运维人员更好地排查问题和监控系统状态。

## ✨ 日志功能特性

### 📊 分级日志输出
- **DEBUG**: 详细的调试信息，包括消息内容、连接详情等
- **INFO**: 一般信息，如连接建立、请求处理等  
- **WARN**: 警告信息，如未知消息类型、连接断开等
- **ERROR**: 错误信息，包含完整错误堆栈和上下文

### 🌈 实时控制台日志
- **彩色输出**: 不同日志级别使用不同颜色显示
- **时间戳**: 精确到秒的时间记录
- **运行时间**: 显示服务启动后的运行时间
- **分类标识**: 清晰标识日志来源（SYSTEM、HTTP、WebSocket等）

### 💾 文件日志持久化
- **按日期分割**: 每天自动创建新的日志文件
- **分类存储**: 运行日志和错误日志分别存储
- **自动轮转**: 防止单个日志文件过大

### 📈 性能监控
- **连接统计**: 实时显示连接数、总连接数等
- **请求监控**: HTTP请求响应时间记录
- **内存使用**: 定期报告内存使用情况
- **系统状态**: 每分钟自动生成状态报告

## 🚀 快速开始

### 1. 启动增强版网关服务

```bash
# 使用启动脚本（推荐）
start_enhanced_gateway.bat

# 或直接运行
node final_unified_gateway_with_logs.js
```

### 2. 查看实时日志

启动后，控制台将显示彩色的实时日志：

```
[2025-08-24 15:30:21] [0.5s] [INFO] [SYSTEM] 🚀 启动统一网关服务 v1.1.0
[2025-08-24 15:30:21] [0.6s] [INFO] [SYSTEM] 📁 日志目录: E:\AI_codeE\imgCT\gateway_service\logs
[2025-08-24 15:30:21] [0.7s] [SUCCESS] [SYSTEM] 🌐 服务器运行在: http://localhost:7777
```

### 3. 监控日志文件

```bash
# 启动日志监控工具
log_monitor.bat
```

## 📁 日志文件说明

### 文件命名规则
- `gateway-YYYY-MM-DD.log` - 每日运行日志
- `error-YYYY-MM-DD.log` - 每日错误日志

### 文件位置
```
gateway_service/
├── logs/
│   ├── gateway-2025-08-24.log    # 今日运行日志
│   ├── error-2025-08-24.log      # 今日错误日志
│   ├── gateway-2025-08-23.log    # 昨日运行日志
│   └── ...
```

## 🛠️ 日志配置

### 自定义日志级别
编辑 `gateway-config.json` 文件：

```json
{
  "logging": {
    "level": "DEBUG",           // 全局日志级别
    "console": {
      "enabled": true,         // 启用控制台输出
      "colorful": true,        // 彩色输出
      "timestamp": true        // 显示时间戳
    },
    "file": {
      "enabled": true,         // 启用文件日志
      "directory": "./logs",   // 日志目录
      "rotation": "daily"      // 按日轮转
    }
  }
}
```

### 分类日志控制
```json
{
  "categories": {
    "SYSTEM": { "enabled": true, "level": "INFO" },
    "HTTP": { "enabled": true, "level": "INFO" },
    "WebSocket": { "enabled": true, "level": "DEBUG" },
    "Terminal": { "enabled": true, "level": "DEBUG" },
    "Controller": { "enabled": true, "level": "DEBUG" }
  }
}
```

## 📋 日志监控工具

运行 `log_monitor.bat` 进入日志监控界面：

```
═══════════════════════════════════════════════════════════════
                        日志监控菜单                           
═══════════════════════════════════════════════════════════════

[1] 查看今日运行日志      [5] 显示日志统计信息
[2] 查看今日错误日志      [6] 清理历史日志  
[3] 实时监控运行日志      [7] 搜索日志内容
[4] 实时监控错误日志      [0] 退出
```

### 主要功能说明

1. **查看今日日志**: 显示当天的完整日志内容
2. **实时监控**: 类似`tail -f`的实时日志跟踪
3. **统计信息**: 显示日志文件数量、大小、条数等统计
4. **搜索功能**: 在所有日志文件中搜索关键词
5. **清理功能**: 删除7天前的历史日志文件

## 🔍 问题排查指南

### 1. 连接问题
查看WebSocket连接日志：
```
[INFO] [WebSocket] 连接建立 - terminal:terminal_1724486421_abc123
[DEBUG] [Terminal] 收到消息 | Data: {"type":"device_register","data":{...}}
[SUCCESS] [Terminal] 设备注册 | Data: {"clientId":"terminal_1724486421_abc123","deviceId":"WIN-PC_terminal"}
```

### 2. 消息传输问题
查看消息处理日志：
```
[DEBUG] [Controller] 发送命令到终端 | Data: {"from":"controller_123","to":"terminal_456","command":{...}}
[SUCCESS] [Controller] 命令发送成功 | Data: {"from":"controller_123","to":"terminal_456"}
```

### 3. 错误追踪
查看错误日志文件：
```
[ERROR] [Terminal] 消息解析错误 | Data: {"clientId":"terminal_123","error":"Unexpected token","stack":"..."}
```

## 📊 性能监控

### 实时统计报告
每分钟自动生成：
```
[INFO] [STATISTICS] 系统状态报告 | Data: {
  "connectedDevices": 2,
  "connectedControllers": 1,
  "totalConnections": 15,
  "totalRequests": 89,
  "uptime": "3600.5",
  "memoryUsage": {"rss": 45678912, "heapUsed": 23456789}
}
```

### HTTP请求监控
每个请求都会记录响应时间：
```
[INFO] [HTTP] GET /health (23ms)
[INFO] [HTTP] GET /terminal/status (15ms)
```

## 🎛️ 高级配置

### 环境变量配置
```bash
# 设置端口
set PORT=7777

# 设置日志级别
set LOG_LEVEL=DEBUG

# 启用开发模式
set NODE_ENV=development
```

### 日志文件轮转
- **自动轮转**: 每天午夜自动创建新日志文件
- **文件大小**: 单个文件最大10MB（可配置）
- **保留期限**: 默认保留30天（可配置）

## 🔧 故障排除

### 常见问题

1. **日志文件权限问题**
   ```bash
   # 确保日志目录有写权限
   icacls logs /grant %USERNAME%:F
   ```

2. **日志文件过大**
   ```bash
   # 使用清理工具
   log_monitor.bat -> [6] 清理历史日志
   ```

3. **控制台乱码**
   ```bash
   # 确保控制台支持UTF-8
   chcp 65001
   ```

### 性能优化

1. **减少日志输出**: 将DEBUG级别改为INFO
2. **禁用文件日志**: 仅使用控制台输出
3. **增加轮转频率**: 改为按小时轮转

## 📞 技术支持

如果遇到日志相关问题：

1. 检查日志配置文件语法
2. 确认文件权限设置正确
3. 查看错误日志文件获取详细信息
4. 使用日志监控工具的搜索功能定位问题

---

**版本**: v1.1.0  
**更新日期**: 2025-08-24  
**维护者**: Flutter ImgCT Team