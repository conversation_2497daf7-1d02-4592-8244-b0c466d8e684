import dgram from 'dgram';
import { EventEmitter } from 'events';
import { NetworkInterfaceInfo, networkInterfaces } from 'os';
import { WebSocketService } from './WebSocketService';
import { databaseService } from './DatabaseService';

/// 发现的设备信息接口
interface DiscoveredDevice {
  id: string;
  name: string;
  type: string;
  platform: string;
  version: string;
  ipAddress: string;
  port: number;
  capabilities: Record<string, any>;
  metadata: Record<string, any>;
  discoveredAt: Date;
  lastSeen: Date;
}

/// 设备发现配置接口
interface DiscoveryConfig {
  broadcastPort: number;
  listenPort: number;
  broadcastInterval: number;
  deviceTimeout: number;
  maxRetries: number;
  enableAutoDiscovery: boolean;
}

/// 设备发现服务类
export class DeviceDiscoveryService extends EventEmitter {
  private static instance: DeviceDiscoveryService;
  private udpServer: dgram.Socket | null = null;
  private broadcastClient: dgram.Socket | null = null;
  private discoveredDevices = new Map<string, DiscoveredDevice>();
  private discoveryInterval: NodeJS.Timeout | null = null;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private isRunning = false;
  
  private config: DiscoveryConfig = {
    broadcastPort: parseInt(process.env.DISCOVERY_BROADCAST_PORT || '8889'),
    listenPort: parseInt(process.env.DISCOVERY_LISTEN_PORT || '8888'),
    broadcastInterval: parseInt(process.env.DISCOVERY_INTERVAL || '30000'), // 30秒
    deviceTimeout: parseInt(process.env.DEVICE_TIMEOUT || '120000'), // 2分钟
    maxRetries: parseInt(process.env.DISCOVERY_MAX_RETRIES || '3'),
    enableAutoDiscovery: process.env.ENABLE_AUTO_DISCOVERY !== 'false'
  };
  
  private constructor() {
    super();
  }
  
  /// 获取单例实例
  public static getInstance(): DeviceDiscoveryService {
    if (!DeviceDiscoveryService.instance) {
      DeviceDiscoveryService.instance = new DeviceDiscoveryService();
    }
    return DeviceDiscoveryService.instance;
  }
  
  /// 启动设备发现服务
  public async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Device discovery service is already running');
      return;
    }
    
    console.log('Starting device discovery service...');
    
    try {
      // 启动UDP监听服务器
      await this.startUdpServer();
      
      // 启动UDP广播客户端
      await this.startBroadcastClient();
      
      // 启动定期广播
      if (this.config.enableAutoDiscovery) {
        this.startPeriodicBroadcast();
      }
      
      // 启动清理任务
      this.startCleanupTask();
      
      this.isRunning = true;
      console.log('✅ Device discovery service started successfully');
      console.log(`📡 Listening on port ${this.config.listenPort}`);
      console.log(`📢 Broadcasting on port ${this.config.broadcastPort}`);
      
      this.emit('started');
    } catch (error) {
      console.error('Failed to start device discovery service:', error);
      await this.stop();
      throw error;
    }
  }
  
  /// 停止设备发现服务
  public async stop(): Promise<void> {
    console.log('Stopping device discovery service...');
    
    this.isRunning = false;
    
    // 停止定期任务
    if (this.discoveryInterval) {
      clearInterval(this.discoveryInterval);
      this.discoveryInterval = null;
    }
    
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    
    // 关闭UDP服务器
    if (this.udpServer) {
      this.udpServer.close();
      this.udpServer = null;
    }
    
    // 关闭广播客户端
    if (this.broadcastClient) {
      this.broadcastClient.close();
      this.broadcastClient = null;
    }
    
    // 清空发现的设备
    this.discoveredDevices.clear();
    
    console.log('✅ Device discovery service stopped');
    this.emit('stopped');
  }
  
  /// 启动UDP监听服务器
  private async startUdpServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.udpServer = dgram.createSocket('udp4');
      
      this.udpServer.on('error', (error) => {
        console.error('UDP server error:', error);
        reject(error);
      });
      
      this.udpServer.on('message', (message, remoteInfo) => {
        this.handleDiscoveryMessage(message, remoteInfo);
      });
      
      this.udpServer.on('listening', () => {
        const address = this.udpServer!.address();
        console.log(`UDP discovery server listening on ${address.address}:${address.port}`);
        resolve();
      });
      
      this.udpServer.bind(this.config.listenPort);
    });
  }
  
  /// 启动UDP广播客户端
  private async startBroadcastClient(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.broadcastClient = dgram.createSocket('udp4');
      
      this.broadcastClient.on('error', (error) => {
        console.error('UDP broadcast client error:', error);
        reject(error);
      });
      
      this.broadcastClient.on('listening', () => {
        this.broadcastClient!.setBroadcast(true);
        console.log('UDP broadcast client ready');
        resolve();
      });
      
      this.broadcastClient.bind();
    });
  }
  
  /// 处理发现消息
  private async handleDiscoveryMessage(message: Buffer, remoteInfo: dgram.RemoteInfo): Promise<void> {
    try {
      const data = JSON.parse(message.toString());
      
      // 验证消息格式
      if (!this.isValidDiscoveryMessage(data)) {
        console.warn('Invalid discovery message received:', data);
        return;
      }
      
      const deviceInfo = data.device;
      const messageType = data.type;
      
      switch (messageType) {
        case 'device_announce':
          await this.handleDeviceAnnounce(deviceInfo, remoteInfo);
          break;
        case 'device_response':
          await this.handleDeviceResponse(deviceInfo, remoteInfo);
          break;
        case 'gateway_discovery':
          await this.handleGatewayDiscovery(remoteInfo);
          break;
        default:
          console.warn(`Unknown discovery message type: ${messageType}`);
      }
    } catch (error) {
      console.error('Error handling discovery message:', error);
    }
  }
  
  /// 验证发现消息格式
  private isValidDiscoveryMessage(data: any): boolean {
    return (
      data &&
      typeof data.type === 'string' &&
      data.device &&
      typeof data.device.id === 'string' &&
      typeof data.device.name === 'string'
    );
  }
  
  /// 处理设备公告
  private async handleDeviceAnnounce(deviceInfo: any, remoteInfo: dgram.RemoteInfo): Promise<void> {
    const deviceId = deviceInfo.id;
    const now = new Date();
    
    // 创建或更新发现的设备信息
    const discoveredDevice: DiscoveredDevice = {
      id: deviceId,
      name: deviceInfo.name || `Device ${deviceId}`,
      type: deviceInfo.type || 'terminal',
      platform: deviceInfo.platform || 'unknown',
      version: deviceInfo.version || '1.0.0',
      ipAddress: remoteInfo.address,
      port: deviceInfo.port || 0,
      capabilities: deviceInfo.capabilities || {},
      metadata: deviceInfo.metadata || {},
      discoveredAt: this.discoveredDevices.get(deviceId)?.discoveredAt || now,
      lastSeen: now
    };
    
    const isNewDevice = !this.discoveredDevices.has(deviceId);
    this.discoveredDevices.set(deviceId, discoveredDevice);
    
    // 保存到数据库
    await this.saveDiscoveredDevice(discoveredDevice);
    
    // 发送网关响应
    await this.sendGatewayResponse(remoteInfo);
    
    // 触发事件
    if (isNewDevice) {
      console.log(`🔍 New device discovered: ${deviceId} (${discoveredDevice.name}) at ${remoteInfo.address}`);
      this.emit('device_discovered', discoveredDevice);
    } else {
      console.log(`🔄 Device updated: ${deviceId} at ${remoteInfo.address}`);
      this.emit('device_updated', discoveredDevice);
    }
  }
  
  /// 处理设备响应
  private async handleDeviceResponse(deviceInfo: any, remoteInfo: dgram.RemoteInfo): Promise<void> {
    // 与设备公告处理类似
    await this.handleDeviceAnnounce(deviceInfo, remoteInfo);
  }
  
  /// 处理网关发现请求
  private async handleGatewayDiscovery(remoteInfo: dgram.RemoteInfo): Promise<void> {
    // 响应网关信息
    await this.sendGatewayResponse(remoteInfo);
  }
  
  /// 发送网关响应
  private async sendGatewayResponse(remoteInfo: dgram.RemoteInfo): Promise<void> {
    if (!this.broadcastClient) return;
    
    const response = {
      type: 'gateway_response',
      gateway: {
        id: 'gateway_' + require('os').hostname(),
        name: 'Image Control Gateway',
        version: '1.0.0',
        ipAddress: this.getLocalIpAddress(),
        port: parseInt(process.env.PORT || '3000'),
        websocketPort: parseInt(process.env.WEBSOCKET_PORT || '3000'),
        capabilities: {
          websocket: true,
          deviceManagement: true,
          imageProcessing: true,
          multiDevice: true
        },
        timestamp: new Date().toISOString()
      }
    };
    
    const message = Buffer.from(JSON.stringify(response));
    
    this.broadcastClient.send(
      message,
      remoteInfo.port,
      remoteInfo.address,
      (error) => {
        if (error) {
          console.error('Error sending gateway response:', error);
        } else {
          console.log(`📤 Gateway response sent to ${remoteInfo.address}:${remoteInfo.port}`);
        }
      }
    );
  }
  
  /// 启动定期广播
  private startPeriodicBroadcast(): void {
    this.discoveryInterval = setInterval(() => {
      this.broadcastDiscovery();
    }, this.config.broadcastInterval);
    
    // 立即执行一次广播
    setTimeout(() => this.broadcastDiscovery(), 1000);
  }
  
  /// 广播发现消息
  public async broadcastDiscovery(): Promise<void> {
    if (!this.broadcastClient || !this.isRunning) {
      return;
    }
    
    const discoveryMessage = {
      type: 'gateway_discovery',
      gateway: {
        id: 'gateway_' + require('os').hostname(),
        name: 'Image Control Gateway',
        version: '1.0.0',
        ipAddress: this.getLocalIpAddress(),
        port: parseInt(process.env.PORT || '3000'),
        websocketPort: parseInt(process.env.WEBSOCKET_PORT || '3000'),
        timestamp: new Date().toISOString()
      }
    };
    
    const message = Buffer.from(JSON.stringify(discoveryMessage));
    const broadcastAddresses = this.getBroadcastAddresses();
    
    for (const address of broadcastAddresses) {
      this.broadcastClient.send(
        message,
        this.config.broadcastPort,
        address,
        (error) => {
          if (error) {
            console.error(`Error broadcasting to ${address}:`, error);
          } else {
            console.log(`📡 Discovery broadcast sent to ${address}:${this.config.broadcastPort}`);
          }
        }
      );
    }
  }
  
  /// 获取广播地址列表
  private getBroadcastAddresses(): string[] {
    const addresses: string[] = [];
    const interfaces = networkInterfaces();
    
    for (const name of Object.keys(interfaces)) {
      const networkInterface = interfaces[name];
      if (!networkInterface) continue;
      
      for (const net of networkInterface) {
        // 跳过内部地址和IPv6
        if (net.family === 'IPv4' && !net.internal) {
          // 计算广播地址
          const ip = net.address.split('.').map(Number);
          const netmask = net.netmask.split('.').map(Number);
          const broadcast = ip.map((octet, i) => octet | (255 - (netmask?.[i] || 0)));
          addresses.push(broadcast.join('.'));
        }
      }
    }
    
    // 添加通用广播地址
    if (!addresses.includes('***************')) {
      addresses.push('***************');
    }
    
    return addresses;
  }
  
  /// 获取本地IP地址
  private getLocalIpAddress(): string {
    const interfaces = networkInterfaces();
    
    for (const name of Object.keys(interfaces)) {
      const networkInterface = interfaces[name];
      if (!networkInterface) continue;
      
      for (const net of networkInterface) {
        if (net.family === 'IPv4' && !net.internal) {
          return net.address;
        }
      }
    }
    
    return '127.0.0.1';
  }
  
  /// 启动清理任务
  private startCleanupTask(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupStaleDevices();
    }, 60000); // 每分钟清理一次
  }
  
  /// 清理过期设备
  private cleanupStaleDevices(): void {
    const now = new Date();
    const staleDevices: string[] = [];
    
    for (const [deviceId, device] of this.discoveredDevices) {
      const timeSinceLastSeen = now.getTime() - device.lastSeen.getTime();
      
      if (timeSinceLastSeen > this.config.deviceTimeout) {
        staleDevices.push(deviceId);
      }
    }
    
    for (const deviceId of staleDevices) {
      const device = this.discoveredDevices.get(deviceId);
      if (device) {
        console.log(`🗑️ Removing stale device: ${deviceId} (last seen: ${device.lastSeen.toISOString()})`);
        this.discoveredDevices.delete(deviceId);
        this.emit('device_lost', device);
      }
    }
    
    if (staleDevices.length > 0) {
      console.log(`Cleaned up ${staleDevices.length} stale devices`);
    }
  }
  
  /// 保存发现的设备到数据库
  private async saveDiscoveredDevice(device: DiscoveredDevice): Promise<void> {
    try {
      await databaseService.query(
        `INSERT OR REPLACE INTO discovered_devices 
         (id, name, type, platform, version, ip_address, port, capabilities, metadata, discovered_at, last_seen) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          device.id,
          device.name,
          device.type,
          device.platform,
          device.version,
          device.ipAddress,
          device.port,
          JSON.stringify(device.capabilities),
          JSON.stringify(device.metadata),
          device.discoveredAt.toISOString(),
          device.lastSeen.toISOString()
        ]
      );
    } catch (error) {
      console.error('Failed to save discovered device to database:', error);
    }
  }
  
  /// 手动发现设备
  public async discoverDevices(): Promise<DiscoveredDevice[]> {
    console.log('🔍 Starting manual device discovery...');
    
    // 广播发现消息
    await this.broadcastDiscovery();
    
    // 等待一段时间收集响应
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const devices = Array.from(this.discoveredDevices.values());
    console.log(`Found ${devices.length} devices`);
    
    return devices;
  }
  
  /// 获取发现的设备列表
  public getDiscoveredDevices(): DiscoveredDevice[] {
    return Array.from(this.discoveredDevices.values());
  }
  
  /// 获取特定设备信息
  public getDiscoveredDevice(deviceId: string): DiscoveredDevice | undefined {
    return this.discoveredDevices.get(deviceId);
  }
  
  /// 检查设备是否在线
  public isDeviceOnline(deviceId: string): boolean {
    const device = this.discoveredDevices.get(deviceId);
    if (!device) return false;
    
    const now = new Date();
    const timeSinceLastSeen = now.getTime() - device.lastSeen.getTime();
    return timeSinceLastSeen < this.config.deviceTimeout;
  }
  
  /// 获取服务状态
  public getStatus(): Record<string, any> {
    return {
      isRunning: this.isRunning,
      config: this.config,
      discoveredDevicesCount: this.discoveredDevices.size,
      localIpAddress: this.getLocalIpAddress(),
      broadcastAddresses: this.getBroadcastAddresses(),
      devices: this.getDiscoveredDevices().map(device => ({
        id: device.id,
        name: device.name,
        ipAddress: device.ipAddress,
        status: this.isDeviceOnline(device.id) ? 'online' : 'offline',
        lastSeen: device.lastSeen.toISOString()
      }))
    };
  }
  
  /// 更新配置
  public updateConfig(newConfig: Partial<DiscoveryConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('Device discovery configuration updated:', this.config);
  }

  /// 获取发现的设备列表（静态方法）
  public static getDiscoveredDevices(): DiscoveredDevice[] {
    return Array.from(DeviceDiscoveryService.getInstance().discoveredDevices.values());
  }

  /// 检查设备是否在线（静态方法）
  public static isDeviceOnline(deviceId: string): boolean {
    const device = DeviceDiscoveryService.getInstance().discoveredDevices.get(deviceId);
    if (!device) return false;
    
    const now = new Date();
    const timeSinceLastSeen = now.getTime() - device.lastSeen.getTime();
    return timeSinceLastSeen < DeviceDiscoveryService.getInstance().config.deviceTimeout;
  }

  /// 获取服务状态（静态方法）
  public static getStatus(): any {
    const instance = DeviceDiscoveryService.getInstance();
    return {
      isRunning: instance.isRunning,
      config: instance.config,
      discoveredDevicesCount: instance.discoveredDevices.size,
      localIpAddress: '127.0.0.1', // 简化实现
      broadcastAddresses: ['***************'] // 简化实现
    };
  }

  /// 关闭服务
  public shutdown(): void {
    console.log('✅ Device discovery service stopped');
    this.emit('stopped');
  }
}

/// 导出单例实例
export default DeviceDiscoveryService.getInstance();