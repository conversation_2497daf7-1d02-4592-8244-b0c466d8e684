import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/app.dart';
import 'core/config/app_config.dart';
import 'core/services/storage_service.dart';
import 'core/services/logger_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize logger
  await LoggerService.instance.initialize();
  
  try {
    // Initialize Hive for local storage
    await Hive.initFlutter();
    
    // Initialize storage service
    await StorageService.instance.initialize();
    
    // Load app configuration
    await AppConfig.instance.initialize();
    
    LoggerService.info('App initialization completed successfully');
    
    // Run the app
    runApp(
      const ProviderScope(
        child: FlutterImgctControlApp(),
      ),
    );
  } catch (error, stackTrace) {
    LoggerService.error('Failed to initialize app', error, stackTrace);
    
    // Show error dialog or fallback UI
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Failed to initialize app',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  error.toString(),
                  textAlign: TextAlign.center,
                  style: const TextStyle(color: Colors.grey),
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}