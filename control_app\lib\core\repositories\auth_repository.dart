import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:http/http.dart' as http;
import '../config/app_constants.dart';

/// 认证仓库类
/// 负责处理用户认证相关的网络请求
class AuthRepository {
  final String _baseUrl;

  AuthRepository({String? baseUrl})
      : _baseUrl = baseUrl ?? AppConstants.defaultGatewayUrl;

  /// 用户登录
  Future<Map<String, dynamic>> login(String username, String password) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/api/auth/login'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'username': username,
              'password': password,
            }),
          )
          .timeout(AppConstants.connectionTimeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('登录失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Login error: $e');
      rethrow;
    }
  }

  /// 用户注册
  Future<Map<String, dynamic>> register(
      String username, String email, String password) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/api/auth/register'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'username': username,
              'email': email,
              'password': password,
            }),
          )
          .timeout(AppConstants.connectionTimeout);

      if (response.statusCode == 201) {
        return jsonDecode(response.body);
      } else {
        throw Exception('注册失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Register error: $e');
      rethrow;
    }
  }

  /// 验证Token有效性
  Future<bool> validateToken(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/auth/validate'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      ).timeout(AppConstants.connectionTimeout);

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Token validation error: $e');
      return false;
    }
  }

  /// 刷新Token
  Future<Map<String, dynamic>> refreshToken(String refreshToken) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/api/auth/refresh'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'refreshToken': refreshToken,
            }),
          )
          .timeout(AppConstants.connectionTimeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('Token刷新失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Token refresh error: $e');
      rethrow;
    }
  }

  /// 获取当前用户信息
  Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/api/auth/me'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(AppConstants.connectionTimeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('获取用户信息失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Get current user error: $e');
      rethrow;
    }
  }

  /// 修改密码
  Future<bool> changePassword(String oldPassword, String newPassword) async {
    try {
      final response = await http
          .post(
            Uri.parse('$_baseUrl/api/auth/change-password'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode({
              'oldPassword': oldPassword,
              'newPassword': newPassword,
            }),
          )
          .timeout(AppConstants.connectionTimeout);

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Change password error: $e');
      return false;
    }
  }

  /// 用户登出
  Future<bool> logout() async {
    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/auth/logout'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(AppConstants.connectionTimeout);

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Logout error: $e');
      return false;
    }
  }

  /// 更新用户信息
  Future<Map<String, dynamic>> updateUserInfo(
      String userId, Map<String, dynamic> updates) async {
    try {
      final response = await http
          .put(
            Uri.parse('$_baseUrl/api/auth/users/$userId'),
            headers: {
              'Content-Type': 'application/json',
            },
            body: jsonEncode(updates),
          )
          .timeout(AppConstants.connectionTimeout);

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      } else {
        throw Exception('更新用户信息失败: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Update user info error: $e');
      rethrow;
    }
  }
}

/// AuthRepository 提供者
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  return AuthRepository();
});
