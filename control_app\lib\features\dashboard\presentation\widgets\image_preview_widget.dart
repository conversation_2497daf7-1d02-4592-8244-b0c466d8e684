import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../core/models/image_info.dart';
import '../../../../core/services/image_management_service.dart';
import '../../../../core/services/logger_service.dart';

/// 图片预览组件
class ImagePreviewWidget extends ConsumerStatefulWidget {
  final AppImageInfo? image;
  final Function(AppImageInfo)? onImageUpdated;
  
  const ImagePreviewWidget({
    super.key,
    this.image,
    this.onImageUpdated,
  });
  
  @override
  ConsumerState<ImagePreviewWidget> createState() => _ImagePreviewWidgetState();
}

class _ImagePreviewWidgetState extends ConsumerState<ImagePreviewWidget> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _tagController = TextEditingController();
  bool _isEditing = false;
  
  @override
  void initState() {
    super.initState();
    _updateControllers();
  }
  
  @override
  void didUpdateWidget(ImagePreviewWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.image?.id != widget.image?.id) {
      _updateControllers();
      _isEditing = false;
    }
  }
  
  @override
  void dispose() {
    _nameController.dispose();
    _tagController.dispose();
    super.dispose();
  }
  
  void _updateControllers() {
    _nameController.text = widget.image?.name ?? '';
    _tagController.text = widget.image?.tags.join(', ') ?? '';
  }
  
  @override
  Widget build(BuildContext context) {
    if (widget.image == null) {
      return _buildEmptyState();
    }
    
    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildImagePreview(),
                const SizedBox(height: 16),
                _buildImageInfo(),
                const SizedBox(height: 16),
                _buildImageActions(),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_outlined,
            size: 64,
            color: Colors.white54,
          ),
          SizedBox(height: 16),
          Text(
            '选择图片查看详情',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white70,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '点击左侧图片网格中的任意图片',
            style: TextStyle(
              fontSize: 14,
              color: Colors.white54,
            ),
          ),
        ],
      ),
    );
  }
  
  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Color(0xFF2A2A2A),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          const Text(
            '图片详情',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const Spacer(),
          if (_isEditing) ..[
            TextButton(
              onPressed: _cancelEdit,
              child: const Text('取消', style: TextStyle(color: Colors.grey)),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: _saveEdit,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
              child: const Text('保存'),
            ),
          ] else ..[
            IconButton(
              onPressed: _toggleFavorite,
              icon: Icon(
                widget.image!.isFavorite ? Icons.favorite : Icons.favorite_border,
                color: widget.image!.isFavorite ? Colors.red : null,
              ),
              tooltip: widget.image!.isFavorite ? '取消收藏' : '添加收藏',
            ),
            IconButton(
              onPressed: _startEdit,
              icon: const Icon(Icons.edit),
              tooltip: '编辑',
            ),
            IconButton(
              onPressed: _deleteImage,
              icon: const Icon(Icons.delete, color: Colors.red),
              tooltip: '删除',
            ),
          ],
        ],
      ),
    );
  }
  
  /// 构建图片预览
  Widget _buildImagePreview() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.white24),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(7),
        child: _buildImageWidget(),
      ),
    );
  }
  
  /// 构建图片组件
  Widget _buildImageWidget() {
    final image = widget.image!;
    
    if (image.path.startsWith('http')) {
      // 网络图片
      return CachedNetworkImage(
        imageUrl: image.path,
        fit: BoxFit.contain,
        placeholder: (context, url) => Container(
          color: const Color(0xFF3A3A3A),
          child: const Center(
            child: CircularProgressIndicator(color: Colors.white),
          ),
        ),
        errorWidget: (context, url, error) {
          LoggerService.warning('Failed to load network image: ${image.path}');
          return _buildErrorWidget();
        },
      );
    } else {
      // 本地图片
      final file = File(image.path);
      if (file.existsSync()) {
        return Image.file(
          file,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            LoggerService.warning('Failed to load local image: ${image.path}');
            return _buildErrorWidget();
          },
        );
      } else {
        LoggerService.warning('Image file not found: ${image.path}');
        return _buildErrorWidget();
      }
    }
  }
  
  /// 构建错误组件
  Widget _buildErrorWidget() {
    return Container(
      color: const Color(0xFF3A3A3A),
      child: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image,
              color: Colors.white54,
              size: 48,
            ),
            SizedBox(height: 8),
            Text(
              '图片加载失败',
              style: TextStyle(color: Colors.white54),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 构建图片信息
  Widget _buildImageInfo() {
    final image = widget.image!;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 名称
        _buildInfoField(
          label: '名称',
          child: _isEditing
              ? TextField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                )
              : Text(
                  image.name,
                  style: const TextStyle(fontSize: 16),
                ),
        ),
        
        const SizedBox(height: 12),
        
        // 标签
        _buildInfoField(
          label: '标签',
          child: _isEditing
              ? TextField(
                  controller: _tagController,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '用逗号分隔多个标签',
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                  ),
                )
              : image.tags.isEmpty
                  ? const Text(
                      '无标签',
                      style: TextStyle(
                        color: Colors.white54,
                        fontStyle: FontStyle.italic,
                      ),
                    )
                  : Wrap(
                      spacing: 4,
                      runSpacing: 4,
                      children: image.tags.map((tag) {
                        return Chip(
                          label: Text(
                            tag,
                            style: const TextStyle(fontSize: 12, color: Colors.white),
                          ),
                          backgroundColor: const Color(0xFF404040),
                          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        );
                      }).toList(),
                    ),
        ),
        
        const SizedBox(height: 12),
        
        // 基本信息
        _buildInfoField(
          label: '大小',
          child: Text(image.getFormattedSize()),
        ),
        
        const SizedBox(height: 8),
        
        _buildInfoField(
          label: '分辨率',
          child: Text(image.getResolutionString()),
        ),
        
        const SizedBox(height: 8),
        
        _buildInfoField(
          label: '格式',
          child: Text(image.getFileExtension().toUpperCase()),
        ),
        
        const SizedBox(height: 8),
        
        _buildInfoField(
          label: '创建时间',
          child: Text(image.getFormattedCreatedAt()),
        ),
        
        const SizedBox(height: 8),
        
        _buildInfoField(
          label: '路径',
          child: SelectableText(
            image.path,
            style: const TextStyle(
              fontSize: 12,
              fontFamily: 'monospace',
            ),
          ),
        ),
        
        // 元数据
        if (image.metadata.isNotEmpty) ..[
          const SizedBox(height: 12),
          _buildInfoField(
            label: '元数据',
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: image.metadata.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: 80,
                        child: Text(
                          '${entry.key}:',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Text(
                          entry.value.toString(),
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ],
    );
  }
  
  /// 构建信息字段
  Widget _buildInfoField({
    required String label,
    required Widget child,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60,
          child: Text(
            '$label:',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white70,
            ),
          ),
        ),
        Expanded(child: child),
      ],
    );
  }
  
  /// 构建图片操作
  Widget _buildImageActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '操作',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white70,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            ElevatedButton.icon(
              onPressed: _showFullScreen,
              icon: const Icon(Icons.fullscreen),
              label: const Text('全屏预览'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF404040),
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _copyPath,
              icon: const Icon(Icons.copy),
              label: const Text('复制路径'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF404040),
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _openInExplorer,
              icon: const Icon(Icons.folder_open),
              label: const Text('打开位置'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF404040),
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: _generateThumbnail,
              icon: const Icon(Icons.photo_size_select_small),
              label: const Text('生成缩略图'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF404040),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }
  
  /// 开始编辑
  void _startEdit() {
    setState(() {
      _isEditing = true;
    });
  }
  
  /// 取消编辑
  void _cancelEdit() {
    setState(() {
      _isEditing = false;
    });
    _updateControllers();
  }
  
  /// 保存编辑
  Future<void> _saveEdit() async {
    try {
      final imageService = ref.read(imageManagementServiceProvider);
      final name = _nameController.text.trim();
      final tagsText = _tagController.text.trim();
      final tags = tagsText.isEmpty
          ? <String>[]
          : tagsText.split(',').map((tag) => tag.trim()).where((tag) => tag.isNotEmpty).toList();
      
      final updatedImage = await imageService.updateImage(
        widget.image!.id,
        name: name.isEmpty ? null : name,
        tags: tags,
      );
      
      setState(() {
        _isEditing = false;
      });
      
      widget.onImageUpdated?.call(updatedImage);
      _showSuccessSnackBar('图片信息更新成功');
    } catch (error) {
      LoggerService.error('Failed to update image', error);
      _showErrorSnackBar('更新失败');
    }
  }
  
  /// 切换收藏状态
  Future<void> _toggleFavorite() async {
    try {
      final imageService = ref.read(imageManagementServiceProvider);
      final updatedImage = await imageService.updateImage(
        widget.image!.id,
        isFavorite: !widget.image!.isFavorite,
      );
      
      widget.onImageUpdated?.call(updatedImage);
      _showSuccessSnackBar(
        updatedImage.isFavorite ? '已添加到收藏' : '已取消收藏',
      );
    } catch (error) {
      LoggerService.error('Failed to toggle favorite', error);
      _showErrorSnackBar('操作失败');
    }
  }
  
  /// 删除图片
  Future<void> _deleteImage() async {
    final confirmed = await _showDeleteConfirmDialog();
    if (!confirmed) return;
    
    try {
      final imageService = ref.read(imageManagementServiceProvider);
      await imageService.removeImage(widget.image!.id);
      
      _showSuccessSnackBar('图片删除成功');
    } catch (error) {
      LoggerService.error('Failed to delete image', error);
      _showErrorSnackBar('删除失败');
    }
  }
  
  /// 全屏预览
  void _showFullScreen() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _FullScreenImageViewer(image: widget.image!),
      ),
    );
  }
  
  /// 复制路径
  void _copyPath() {
    // TODO: 实现复制到剪贴板
    _showInfoSnackBar('路径已复制到剪贴板');
  }
  
  /// 在资源管理器中打开
  void _openInExplorer() {
    // TODO: 实现在资源管理器中打开
    _showInfoSnackBar('功能即将推出');
  }
  
  /// 生成缩略图
  Future<void> _generateThumbnail() async {
    try {
      final imageService = ref.read(imageManagementServiceProvider);
      await imageService.generateThumbnail(widget.image!.id);
      
      _showSuccessSnackBar('缩略图生成成功');
    } catch (error) {
      LoggerService.error('Failed to generate thumbnail', error);
      _showErrorSnackBar('缩略图生成失败');
    }
  }
  
  /// 显示删除确认对话框
  Future<bool> _showDeleteConfirmDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF2A2A2A),
          title: const Text('删除图片', style: TextStyle(color: Colors.white)),
          content: Text(
            '确定要删除图片 "${widget.image!.name}" 吗？此操作无法撤销。',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消', style: TextStyle(color: Colors.grey)),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
    
    return result ?? false;
  }
  
  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  /// 显示信息提示
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.blue,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// 全屏图片查看器
class _FullScreenImageViewer extends StatelessWidget {
  final AppImageInfo image;
  
  const _FullScreenImageViewer({required this.image});
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          image.name,
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: Center(
        child: InteractiveViewer(
          child: _buildImageWidget(),
        ),
      ),
    );
  }
  
  Widget _buildImageWidget() {
    if (image.path.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: image.path,
        fit: BoxFit.contain,
        placeholder: (context, url) => const CircularProgressIndicator(
          color: Colors.white,
        ),
        errorWidget: (context, url, error) => const Icon(
          Icons.broken_image,
          color: Colors.white,
          size: 64,
        ),
      );
    } else {
      final file = File(image.path);
      if (file.existsSync()) {
        return Image.file(
          file,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) => const Icon(
            Icons.broken_image,
            color: Colors.white,
            size: 64,
          ),
        );
      } else {
        return const Icon(
          Icons.broken_image,
          color: Colors.white,
          size: 64,
        );
      }
    }
  }
}