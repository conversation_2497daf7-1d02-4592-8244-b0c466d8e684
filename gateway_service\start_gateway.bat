@echo off
chcp 65001 >nul
title 网关服务 - 端口7777

echo.
echo ================================================================
echo.
echo    Flutter图片切换系统 - 网关服务 v2.0.0
echo.
echo ================================================================
echo.

:: 清理端口
echo [INFO] 清理端口7777...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :7777 2^>nul') do (
    echo [INFO] 终止进程 %%a...
    taskkill /PID %%a /F >nul 2>&1
)

:: 等待端口释放
timeout /t 1 /nobreak >nul

echo [SUCCESS] 端口清理完成
echo.
echo [INFO] 启动网关服务...
echo.

:: 启动服务
node final_unified_gateway.js

:: 如果服务退出
echo.
echo [WARN] 网关服务已停止
pause
