import 'dart:io';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:photo_view/photo_view.dart';
import 'package:path/path.dart' as path;
import 'package:intl/intl.dart';

class ImageViewerPage extends StatefulWidget {
  final String imagePath;
  final String? title;
  final List<String>? imageList; // 图片列表，支持左右滑动
  final int? initialIndex; // 初始索引

  const ImageViewerPage({
    super.key,
    required this.imagePath,
    this.title,
    this.imageList,
    this.initialIndex,
  });

  @override
  State<ImageViewerPage> createState() => _ImageViewerPageState();
}

class _ImageViewerPageState extends State<ImageViewerPage>
    with TickerProviderStateMixin {
  bool _imageExists = false;
  String? _error;
  bool _showControls = true;
  late PageController _pageController;
  late List<String> _imageList;
  late int _currentIndex;

  // 图片属性
  File? _currentImageFile;
  int? _imageWidth;
  int? _imageHeight;
  DateTime? _imageCreated;
  DateTime? _imageModified;
  int? _imageSize;

  // 控制器
  late PhotoViewController _photoViewController;
  late AnimationController _controlsAnimationController;
  late Animation<double> _controlsOpacity;

  // 旋转角度
  double _rotationAngle = 0.0;

  @override
  void initState() {
    super.initState();
    _initializeImageList();
    _initializeControllers();
    _checkImageExists();
  }

  /// 初始化图片列表
  void _initializeImageList() {
    if (widget.imageList != null && widget.imageList!.isNotEmpty) {
      _imageList = widget.imageList!;
      _currentIndex = widget.initialIndex ?? 0;
      if (_currentIndex >= _imageList.length) {
        _currentIndex = 0;
      }
    } else {
      _imageList = [widget.imagePath];
      _currentIndex = 0;
    }

    _pageController = PageController(initialPage: _currentIndex);
  }

  /// 初始化控制器
  void _initializeControllers() {
    _photoViewController = PhotoViewController();
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _controlsOpacity = Tween<double>(begin: 0.0, end: 1.0)
        .animate(_controlsAnimationController);

    _controlsAnimationController.forward();
    _startHideControlsTimer();
  }

  void _startHideControlsTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        _hideControls();
      }
    });
  }

  void _showControlsMethod() {
    if (!_showControls) {
      setState(() {
        _showControls = true;
      });
      _controlsAnimationController.forward();
      _startHideControlsTimer();
    }
  }

  void _hideControls() {
    if (_showControls) {
      setState(() {
        _showControls = false;
      });
      _controlsAnimationController.reverse();
    }
  }

  void _toggleControls() {
    if (_showControls) {
      _hideControls();
    } else {
      _showControlsMethod();
    }
  }

  Future<void> _checkImageExists() async {
    try {
      final currentPath = _imageList[_currentIndex];
      final file = File(currentPath);
      final exists = await file.exists();

      setState(() {
        _imageExists = exists;
        _currentImageFile = file;
        if (!exists) {
          _error = '图片文件不存在';
        } else {
          _loadImageMetadata(file);
        }
      });
    } catch (e) {
      setState(() {
        _imageExists = false;
        _error = '检查图片文件时出错: $e';
      });
    }
  }

  /// 加载图片元数据
  Future<void> _loadImageMetadata(File file) async {
    try {
      final stat = await file.stat();
      setState(() {
        _imageSize = stat.size;
        _imageCreated = stat.changed;
        _imageModified = stat.modified;
      });

      await _getImageDimensions(file);
    } catch (e) {
      // 忽略元数据加载错误
    }
  }

  /// 获取图片尺寸
  Future<void> _getImageDimensions(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final image = await decodeImageFromList(bytes);
      setState(() {
        _imageWidth = image.width;
        _imageHeight = image.height;
      });
      image.dispose();
    } catch (e) {
      // 忽略尺寸获取错误
    }
  }

  String _getCurrentFileName() {
    return path.basename(_imageList[_currentIndex]);
  }

  String _getFileSize() {
    if (_imageSize == null) return '未知大小';

    final bytes = _imageSize!;
    if (bytes < 1024) {
      return '${bytes}B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  String _getImageDimensionsString() {
    if (_imageWidth != null && _imageHeight != null) {
      return '${_imageWidth} × ${_imageHeight}';
    }
    return '未知尺寸';
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) return '未知';
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  @override
  void dispose() {
    _pageController.dispose();
    _photoViewController.dispose();
    _controlsAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // 图片显示区域
            _buildImageContent(),

            // 顶部控制栏
            _buildTopControls(),

            // 底部控制栏
            _buildBottomControls(),

            // 左右切换指示器
            if (_imageList.length > 1) _buildNavigationIndicators(),
          ],
        ),
      ),
    );
  }

  /// 构建图片内容区域
  Widget _buildImageContent() {
    if (_error != null) {
      return _buildErrorView();
    }

    if (!_imageExists) {
      return const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
        ),
      );
    }

    if (_imageList.length == 1) {
      return _buildSingleImageView();
    } else {
      return _buildMultipleImageView();
    }
  }

  /// 构建单张图片视图
  Widget _buildSingleImageView() {
    return Transform.rotate(
      angle: _rotationAngle,
      child: PhotoView(
        imageProvider: FileImage(File(_imageList[_currentIndex])),
        controller: _photoViewController,
        minScale: PhotoViewComputedScale.contained * 0.3,
        maxScale: PhotoViewComputedScale.covered * 3.0,
        initialScale: PhotoViewComputedScale.contained,
        backgroundDecoration: const BoxDecoration(
          color: Colors.black,
        ),
        loadingBuilder: (context, event) {
          return const Center(
            child: CircularProgressIndicator(
              color: Colors.white,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildImageErrorView();
        },
        onTapUp: (context, details, controllerValue) {
          _toggleControls();
        },
      ),
    );
  }

  /// 构建多张图片视图
  Widget _buildMultipleImageView() {
    return PageView.builder(
      controller: _pageController,
      onPageChanged: (index) {
        setState(() {
          _currentIndex = index;
          _rotationAngle = 0.0;
        });
        _checkImageExists();
      },
      itemCount: _imageList.length,
      itemBuilder: (context, index) {
        return Transform.rotate(
          angle: index == _currentIndex ? _rotationAngle : 0.0,
          child: PhotoView(
            imageProvider: FileImage(File(_imageList[index])),
            minScale: PhotoViewComputedScale.contained * 0.3,
            maxScale: PhotoViewComputedScale.covered * 3.0,
            initialScale: PhotoViewComputedScale.contained,
            backgroundDecoration: const BoxDecoration(
              color: Colors.black,
            ),
            loadingBuilder: (context, event) {
              return const Center(
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              return _buildImageErrorView();
            },
            onTapUp: (context, details, controllerValue) {
              _toggleControls();
            },
          ),
        );
      },
    );
  }

  /// 构建图片加载错误视图
  Widget _buildImageErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.broken_image,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            '图片加载失败',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                ),
          ),
          const SizedBox(height: 8),
          Text(
            '无法显示该图片',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white70,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              '无法显示图片',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.white70,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('返回'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageView() {
    return PhotoView(
      imageProvider: FileImage(File(widget.imagePath)),
      minScale: PhotoViewComputedScale.contained * 0.8,
      maxScale: PhotoViewComputedScale.covered * 2.0,
      initialScale: PhotoViewComputedScale.contained,
      backgroundDecoration: const BoxDecoration(
        color: Colors.black,
      ),
      loadingBuilder: (context, event) {
        return const Center(
          child: CircularProgressIndicator(
            color: Colors.white,
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.broken_image,
                size: 64,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                '图片加载失败',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white,
                    ),
              ),
              const SizedBox(height: 8),
              Text(
                '无法显示该图片',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white70,
                    ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showImageInfo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('图片信息'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('文件名', _getCurrentFileName()),
            const SizedBox(height: 8),
            _buildInfoRow('文件大小', _getFileSize()),
            const SizedBox(height: 8),
            _buildInfoRow('图片尺寸', _getImageDimensionsString()),
            const SizedBox(height: 8),
            _buildInfoRow('创建时间', _formatDateTime(_imageCreated)),
            const SizedBox(height: 8),
            _buildInfoRow('修改时间', _formatDateTime(_imageModified)),
            const SizedBox(height: 8),
            _buildInfoRow('文件路径', _imageList[_currentIndex], isPath: true),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isPath = false}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: isPath ? 12 : 14,
              color: Colors.grey[700],
            ),
            softWrap: true,
          ),
        ),
      ],
    );
  }

  /// 构建顶部控制栏
  Widget _buildTopControls() {
    return AnimatedBuilder(
      animation: _controlsOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsOpacity.value,
          child: Container(
            padding: EdgeInsets.only(
              top: MediaQuery.of(context).padding.top + 8,
              left: 16,
              right: 16,
              bottom: 8,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black.withOpacity(0.7),
                  Colors.transparent,
                ],
              ),
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        widget.title ?? '图片查看器',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (_imageList.length > 1)
                        Text(
                          '${_currentIndex + 1} / ${_imageList.length}',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: _showImageInfo,
                  icon: const Icon(Icons.info_outline, color: Colors.white),
                  tooltip: '图片信息',
                ),
                IconButton(
                  onPressed: _shareImage,
                  icon: const Icon(Icons.share, color: Colors.white),
                  tooltip: '分享图片',
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onSelected: _handleMenuAction,
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'open_folder',
                      child: Row(
                        children: [
                          Icon(Icons.folder_open),
                          SizedBox(width: 8),
                          Text('打开文件位置'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'copy_path',
                      child: Row(
                        children: [
                          Icon(Icons.copy),
                          SizedBox(width: 8),
                          Text('复制路径'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建底部控制栏
  Widget _buildBottomControls() {
    return AnimatedBuilder(
      animation: _controlsOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsOpacity.value,
          child: Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.only(
                left: 16,
                right: 16,
                bottom: MediaQuery.of(context).padding.bottom + 8,
                top: 8,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withOpacity(0.7),
                    Colors.transparent,
                  ],
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildControlButton(
                    icon: Icons.zoom_out_map,
                    onPressed: _resetZoom,
                    tooltip: '重置缩放',
                  ),
                  _buildControlButton(
                    icon: Icons.rotate_left,
                    onPressed: _rotateLeft,
                    tooltip: '向左旋转',
                  ),
                  _buildControlButton(
                    icon: Icons.rotate_right,
                    onPressed: _rotateRight,
                    tooltip: '向右旋转',
                  ),
                  _buildControlButton(
                    icon: Icons.fit_screen,
                    onPressed: _fitToScreen,
                    tooltip: '适应屏幕',
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建控制按钮
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.5),
        borderRadius: BorderRadius.circular(24),
      ),
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(icon, color: Colors.white),
        tooltip: tooltip,
      ),
    );
  }

  /// 构建导航指示器
  Widget _buildNavigationIndicators() {
    return AnimatedBuilder(
      animation: _controlsOpacity,
      builder: (context, child) {
        return Opacity(
          opacity: _controlsOpacity.value * 0.7,
          child: Row(
            children: [
              if (_currentIndex > 0)
                Positioned(
                  left: 16,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: GestureDetector(
                      onTap: _previousImage,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: const Icon(
                          Icons.chevron_left,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    ),
                  ),
                ),
              const Spacer(),
              if (_currentIndex < _imageList.length - 1)
                Positioned(
                  right: 16,
                  top: 0,
                  bottom: 0,
                  child: Center(
                    child: GestureDetector(
                      onTap: _nextImage,
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(24),
                        ),
                        child: const Icon(
                          Icons.chevron_right,
                          color: Colors.white,
                          size: 32,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  /// 处理菜单动作
  void _handleMenuAction(String action) {
    switch (action) {
      case 'open_folder':
        _openFileLocation();
        break;
      case 'copy_path':
        _copyImagePath();
        break;
    }
  }

  /// 重置缩放
  void _resetZoom() {
    _photoViewController.reset();
  }

  /// 向左旋转
  void _rotateLeft() {
    setState(() {
      _rotationAngle -= math.pi / 2;
    });
  }

  /// 向右旋转
  void _rotateRight() {
    setState(() {
      _rotationAngle += math.pi / 2;
    });
  }

  /// 适应屏幕
  void _fitToScreen() {
    _photoViewController.reset();
  }

  /// 上一张图片
  void _previousImage() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 下一张图片
  void _nextImage() {
    if (_currentIndex < _imageList.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 分享图片
  Future<void> _shareImage() async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('分享功能待实现'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('分享失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 打开文件位置
  Future<void> _openFileLocation() async {
    try {
      final currentPath = _imageList[_currentIndex];
      if (Platform.isWindows) {
        await Process.run('explorer', ['/select,', currentPath]);
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('打开文件位置失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 复制图片路径
  Future<void> _copyImagePath() async {
    try {
      final currentPath = _imageList[_currentIndex];
      await Clipboard.setData(ClipboardData(text: currentPath));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('路径已复制到剪贴板'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('复制失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
