import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';
import '../services/logger_service.dart';

/// 安全的窗口管理器包装器
/// 提供错误处理和回退机制
class SafeWindowManager {
  static const String _tag = 'SafeWindowManager';

  /// 安全地设置全屏状态
  static Future<bool> setFullScreen(bool fullscreen) async {
    try {
      await windowManager.setFullScreen(fullscreen);
      LoggerService.info('$_tag: Successfully set fullscreen to $fullscreen');
      return true;
    } catch (error) {
      LoggerService.error(
          '$_tag: Failed to set fullscreen to $fullscreen', error);
      return false;
    }
  }

  /// 安全地检查全屏状态
  static Future<bool> isFullScreen() async {
    try {
      final result = await windowManager.isFullScreen();
      return result;
    } catch (error) {
      LoggerService.error('$_tag: Failed to check fullscreen status', error);
      return false; // 默认返回false
    }
  }

  /// 安全地切换全屏状态
  static Future<bool> toggleFullScreen() async {
    try {
      final currentState = await isFullScreen();
      final newState = !currentState;
      final success = await setFullScreen(newState);
      if (success) {
        LoggerService.info(
            '$_tag: Successfully toggled fullscreen from $currentState to $newState');
      }
      return success;
    } catch (error) {
      LoggerService.error('$_tag: Failed to toggle fullscreen', error);
      // 回退：尝试设置为全屏
      return await setFullScreen(true);
    }
  }

  /// 安全地最小化窗口
  static Future<bool> minimize() async {
    try {
      await windowManager.minimize();
      LoggerService.info('$_tag: Successfully minimized window');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to minimize window', error);
      return false;
    }
  }

  /// 安全地隐藏窗口
  static Future<bool> hide() async {
    try {
      await windowManager.hide();
      LoggerService.info('$_tag: Successfully hid window');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to hide window', error);
      return false;
    }
  }

  /// 安全地关闭窗口
  static Future<bool> close() async {
    try {
      await windowManager.close();
      LoggerService.info('$_tag: Successfully closed window');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to close window', error);
      return false;
    }
  }

  /// 安全地显示窗口
  static Future<bool> show() async {
    try {
      await windowManager.show();
      LoggerService.info('$_tag: Successfully showed window');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to show window', error);
      return false;
    }
  }

  /// 安全地获取窗口大小
  static Future<Size?> getSize() async {
    try {
      final size = await windowManager.getSize();
      return size;
    } catch (error) {
      LoggerService.error('$_tag: Failed to get window size', error);
      return null;
    }
  }

  /// 安全地设置窗口大小
  static Future<bool> setSize(Size size) async {
    try {
      await windowManager.setSize(size);
      LoggerService.info(
          '$_tag: Successfully set window size to ${size.width}x${size.height}');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to set window size', error);
      return false;
    }
  }

  /// 安全地获取窗口位置
  static Future<Offset?> getPosition() async {
    try {
      final position = await windowManager.getPosition();
      return position;
    } catch (error) {
      LoggerService.error('$_tag: Failed to get window position', error);
      return null;
    }
  }

  /// 安全地设置窗口位置
  static Future<bool> setPosition(Offset position) async {
    try {
      await windowManager.setPosition(position);
      LoggerService.info(
          '$_tag: Successfully set window position to (${position.dx}, ${position.dy})');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to set window position', error);
      return false;
    }
  }

  /// 安全地居中窗口
  static Future<bool> center() async {
    try {
      await windowManager.center();
      LoggerService.info('$_tag: Successfully centered window');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to center window', error);
      return false;
    }
  }

  /// 安全地设置窗口标题
  static Future<bool> setTitle(String title) async {
    try {
      await windowManager.setTitle(title);
      LoggerService.info('$_tag: Successfully set window title to "$title"');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to set window title', error);
      return false;
    }
  }

  /// 安全地检查窗口是否可见
  static Future<bool> isVisible() async {
    try {
      final result = await windowManager.isVisible();
      return result;
    } catch (error) {
      LoggerService.error('$_tag: Failed to check window visibility', error);
      return true; // 默认返回true
    }
  }

  /// 安全地检查窗口是否最小化
  static Future<bool> isMinimized() async {
    try {
      final result = await windowManager.isMinimized();
      return result;
    } catch (error) {
      LoggerService.error(
          '$_tag: Failed to check window minimized status', error);
      return false; // 默认返回false
    }
  }

  /// 安全地检查窗口是否最大化
  static Future<bool> isMaximized() async {
    try {
      final result = await windowManager.isMaximized();
      return result;
    } catch (error) {
      LoggerService.error(
          '$_tag: Failed to check window maximized status', error);
      return false; // 默认返回false
    }
  }

  /// 安全地最大化窗口
  static Future<bool> maximize() async {
    try {
      await windowManager.maximize();
      LoggerService.info('$_tag: Successfully maximized window');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to maximize window', error);
      return false;
    }
  }

  /// 安全地取消最大化窗口
  static Future<bool> unmaximize() async {
    try {
      await windowManager.unmaximize();
      LoggerService.info('$_tag: Successfully unmaximized window');
      return true;
    } catch (error) {
      LoggerService.error('$_tag: Failed to unmaximize window', error);
      return false;
    }
  }
}
