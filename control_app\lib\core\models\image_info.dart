/// 图片信息模型
class AppImageInfo {
  final String id;
  final String name;
  final String filePath;
  final String thumbnailPath;
  final int size;
  final String hash;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final Map<String, dynamic> metadata;
  final List<String> tags;
  final bool isFavorite;

  const AppImageInfo({
    required this.id,
    required this.name,
    required this.filePath,
    this.thumbnailPath = '',
    required this.size,
    required this.hash,
    required this.createdAt,
    this.updatedAt,
    this.metadata = const {},
    this.tags = const [],
    this.isFavorite = false,
  });

  /// 创建空的图片信息
  factory AppImageInfo.empty() {
    return AppImageInfo(
      id: '',
      name: '',
      filePath: '',
      size: 0,
      hash: '',
      createdAt: DateTime.now(),
    );
  }

  /// 从JSON创建图片信息
  factory AppImageInfo.fromJson(Map<String, dynamic> json) {
    return AppImageInfo(
      id: json['id'] as String,
      name: json['name'] as String,
      filePath: json['filePath'] as String,
      thumbnailPath: json['thumbnailPath'] as String? ?? '',
      size: json['size'] as int,
      hash: json['hash'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      metadata: Map<String, dynamic>.from(json['metadata'] as Map? ?? {}),
      tags: List<String>.from(json['tags'] as List? ?? []),
      isFavorite: json['isFavorite'] as bool? ?? false,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'filePath': filePath,
      'thumbnailPath': thumbnailPath,
      'size': size,
      'hash': hash,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'metadata': metadata,
      'tags': tags,
      'isFavorite': isFavorite,
    };
  }

  /// 复制并修改属性
  AppImageInfo copyWith({
    String? name,
    String? filePath,
    String? thumbnailPath,
    int? size,
    String? hash,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
    List<String>? tags,
    bool? isFavorite,
  }) {
    return AppImageInfo(
      id: id,
      name: name ?? this.name,
      filePath: filePath ?? this.filePath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      size: size ?? this.size,
      hash: hash ?? this.hash,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      metadata: metadata ?? this.metadata,
      tags: tags ?? this.tags,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  /// 获取文件扩展名
  String get fileExtension {
    return filePath.split('.').last.toLowerCase();
  }

  /// 获取格式化的文件大小
  String get formattedSize {
    if (size < 1024) {
      return '${size}B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    } else if (size < 1024 * 1024 * 1024) {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    } else {
      return '${(size / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
    }
  }

  /// 获取创建时间的格式化字符串
  String get formattedCreatedAt {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 检查是否为图片文件
  bool get isImageFile {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    return imageExtensions.contains(fileExtension);
  }

  /// 获取来源设备ID
  String? get sourceDeviceId {
    return metadata['sourceDevice'] as String?;
  }

  /// 获取截图ID
  String? get screenshotId {
    return metadata['screenshotId'] as String?;
  }

  /// 获取图片分辨率
  String? get resolution {
    final width = metadata['width'] as int?;
    final height = metadata['height'] as int?;

    if (width != null && height != null) {
      return '${width}x$height';
    }

    return null;
  }

  /// 检查是否有缩略图
  bool get hasThumbnail {
    return thumbnailPath.isNotEmpty;
  }

  /// 获取图片路径（兼容性属性）
  String get path => filePath;

  /// 获取缩略图路径
  String getThumbnailPath() {
    return thumbnailPath.isNotEmpty ? thumbnailPath : filePath;
  }

  /// 获取格式化的创建时间
  String getFormattedCreatedAt() {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 获取格式化的文件大小
  String getFormattedFileSize() {
    if (size < 1024) {
      return '${size}B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(1)}KB';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(1)}MB';
    }
  }

  /// 获取格式化的大小（别名方法）
  String getFormattedSize() {
    return getFormattedFileSize();
  }

  /// 获取文件扩展名
  String getFileExtension() {
    return fileExtension;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is AppImageInfo && other.id == id && other.hash == hash;
  }

  @override
  int get hashCode {
    return id.hashCode ^ hash.hashCode;
  }

  @override
  String toString() {
    return 'AppImageInfo(id: $id, name: $name, size: $formattedSize, createdAt: $formattedCreatedAt)';
  }
}

/// 图片搜索过滤器
class ImageFilter {
  final String? nameFilter;
  final List<String>? tagFilter;
  final DateTime? startDate;
  final DateTime? endDate;
  final int? minSize;
  final int? maxSize;
  final bool? favoritesOnly;
  final String? deviceFilter;

  const ImageFilter({
    this.nameFilter,
    this.tagFilter,
    this.startDate,
    this.endDate,
    this.minSize,
    this.maxSize,
    this.favoritesOnly,
    this.deviceFilter,
  });

  /// 检查图片是否匹配过滤条件
  bool matches(AppImageInfo image) {
    // 名称过滤
    if (nameFilter != null && nameFilter!.isNotEmpty) {
      if (!image.name.toLowerCase().contains(nameFilter!.toLowerCase())) {
        return false;
      }
    }

    // 标签过滤
    if (tagFilter != null && tagFilter!.isNotEmpty) {
      final hasMatchingTag = tagFilter!.any((tag) => image.tags.contains(tag));
      if (!hasMatchingTag) {
        return false;
      }
    }

    // 日期过滤
    if (startDate != null && image.createdAt.isBefore(startDate!)) {
      return false;
    }

    if (endDate != null && image.createdAt.isAfter(endDate!)) {
      return false;
    }

    // 大小过滤
    if (minSize != null && image.size < minSize!) {
      return false;
    }

    if (maxSize != null && image.size > maxSize!) {
      return false;
    }

    // 收藏过滤
    if (favoritesOnly == true && !image.isFavorite) {
      return false;
    }

    // 设备过滤
    if (deviceFilter != null && deviceFilter!.isNotEmpty) {
      if (image.sourceDeviceId != deviceFilter) {
        return false;
      }
    }

    return true;
  }
}

/// 图片排序方式
enum ImageSortBy {
  name,
  createdAt,
  size,
  favorite,
}

/// 排序方向
enum SortDirection {
  ascending,
  descending,
}

/// 图片排序器
class ImageSorter {
  final ImageSortBy sortBy;
  final SortDirection direction;

  const ImageSorter({
    this.sortBy = ImageSortBy.createdAt,
    this.direction = SortDirection.descending,
  });

  /// 对图片列表进行排序
  List<AppImageInfo> sort(List<AppImageInfo> images) {
    final sortedImages = List<AppImageInfo>.from(images);

    sortedImages.sort((a, b) {
      int comparison;

      switch (sortBy) {
        case ImageSortBy.name:
          comparison = a.name.compareTo(b.name);
          break;
        case ImageSortBy.createdAt:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case ImageSortBy.size:
          comparison = a.size.compareTo(b.size);
          break;
        case ImageSortBy.favorite:
          comparison =
              a.isFavorite == b.isFavorite ? 0 : (a.isFavorite ? -1 : 1);
          break;
      }

      return direction == SortDirection.ascending ? comparison : -comparison;
    });

    return sortedImages;
  }
}
