# 🚀 启动脚本使用指南

## 📋 脚本概述

Flutter图片切换系统提供了多个启动脚本，让用户可以轻松启动整个系统，无需手动逐个启动各个服务。

## 📁 可用脚本

### Windows脚本

| 脚本名称 | 用途 | 推荐场景 |
|---------|------|----------|
| `start_system.bat` | 完整启动脚本，包含多种模式选择 | 首次使用、完整测试 |
| `quick_start.bat` | 快速启动基础模式 | 日常使用、快速演示 |
| `stop_system.bat` | 停止所有相关服务 | 系统清理、重启前 |

### Linux/macOS脚本

| 脚本名称 | 用途 | 推荐场景 |
|---------|------|----------|
| `start_system.sh` | 完整启动脚本，包含多种模式选择 | 所有使用场景 |

## 🔧 详细使用说明

### 1. start_system.bat / start_system.sh (完整启动脚本)

**功能特性:**
- ✅ 自动检查系统环境 (Node.js, Flutter)
- ✅ 自动安装缺失的依赖
- ✅ 检查端口占用并自动处理
- ✅ 提供多种启动模式选择
- ✅ 在独立窗口中启动各个服务
- ✅ 提供详细的状态反馈

**启动模式:**

#### 模式1: 完整模式
- 启动网关服务 (端口9999)
- 启动Flutter终端应用
- 启动控制端应用
- **适用场景**: 完整功能测试、演示

#### 模式2: 基础模式 (推荐)
- 启动网关服务 (端口9999)
- 启动Flutter终端应用
- **适用场景**: 日常使用、终端设备运行

#### 模式3: 仅网关服务
- 只启动网关服务 (端口9999)
- **适用场景**: 服务器部署、API测试

#### 模式4: 测试模式
- 启动网关服务 (端口9999)
- 启动模拟终端设备
- **适用场景**: 开发测试、功能验证

**使用方法:**

Windows:
```cmd
# 双击运行或命令行执行
start_system.bat

# 或者在命令行中
.\start_system.bat
```

Linux/macOS:
```bash
# 给予执行权限
chmod +x start_system.sh

# 运行脚本
./start_system.sh
```

### 2. quick_start.bat (快速启动脚本)

**功能特性:**
- ⚡ 快速启动，无需选择模式
- 🎯 直接启动基础模式 (网关服务 + Flutter终端应用)
- 🔄 自动处理端口冲突
- 📱 适合日常快速使用

**使用方法:**
```cmd
# 双击运行或命令行执行
quick_start.bat
```

### 3. stop_system.bat (停止服务脚本)

**功能特性:**
- 🛑 停止所有相关服务进程
- 🔍 智能识别相关进程
- 🧹 清理端口占用
- ✅ 验证清理结果

**停止的服务:**
- 网关服务 (Node.js进程)
- Flutter终端应用 (Flutter/Dart进程)
- 控制端应用 (Node.js进程)
- 模拟终端设备 (Node.js进程)
- 相关命令行窗口

**使用方法:**
```cmd
# 双击运行或命令行执行
stop_system.bat
```

## 🔍 故障排除

### 问题1: 脚本无法运行
**症状**: 双击脚本无反应或闪退

**解决方案:**
1. 右键脚本 → "以管理员身份运行"
2. 在命令行中运行脚本查看错误信息
3. 检查系统是否安装了Node.js和Flutter

### 问题2: 依赖安装失败
**症状**: 脚本显示依赖安装失败

**解决方案:**
1. 检查网络连接
2. 手动安装依赖:
   ```cmd
   cd gateway_service
   npm install
   
   cd ../controller_app
   npm install
   
   cd ../terminal_app
   flutter pub get
   ```

### 问题3: 端口被占用
**症状**: 脚本提示端口9999被占用

**解决方案:**
1. 脚本会自动处理，选择"y"继续
2. 手动处理:
   ```cmd
   netstat -ano | findstr :9999
   taskkill /PID [进程ID] /F
   ```

### 问题4: Flutter应用启动失败
**症状**: Flutter应用无法启动或编译失败

**解决方案:**
1. 检查Flutter环境:
   ```cmd
   flutter doctor
   ```
2. 清理并重新获取依赖:
   ```cmd
   cd terminal_app
   flutter clean
   flutter pub get
   ```

### 问题5: 服务无法停止
**症状**: stop_system.bat无法完全停止服务

**解决方案:**
1. 手动关闭相关窗口
2. 使用任务管理器结束进程
3. 重启计算机 (最后手段)

## 💡 使用技巧

### 1. 开发环境设置
```cmd
# 首次使用，建议运行完整启动脚本
start_system.bat
# 选择模式1 (完整模式) 进行全面测试
```

### 2. 日常使用
```cmd
# 日常使用，推荐快速启动
quick_start.bat
```

### 3. 服务器部署
```cmd
# 服务器环境，只启动网关服务
start_system.bat
# 选择模式3 (仅网关服务)
```

### 4. 开发测试
```cmd
# 开发测试，使用测试模式
start_system.bat
# 选择模式4 (测试模式)
```

### 5. 系统清理
```cmd
# 重启前或出现问题时，清理所有服务
stop_system.bat
```

## 📊 脚本执行流程

### start_system.bat 执行流程
```
1. 显示欢迎界面
2. 检查系统环境 (Node.js, Flutter)
3. 检查并安装依赖
4. 检查端口占用
5. 显示模式选择菜单
6. 根据选择启动相应服务
7. 验证服务启动状态
8. 显示成功信息和服务地址
```

### quick_start.bat 执行流程
```
1. 显示欢迎界面
2. 检查并处理端口占用
3. 启动网关服务
4. 等待网关服务就绪
5. 启动Flutter终端应用
6. 显示成功信息
```

### stop_system.bat 执行流程
```
1. 显示欢迎界面
2. 停止网关服务 (端口9999)
3. 停止Node.js相关进程
4. 停止Flutter相关进程
5. 停止Dart相关进程
6. 关闭相关命令行窗口
7. 验证端口释放状态
8. 显示清理结果
```

## 🎯 最佳实践

### 1. 首次使用
1. 运行 `start_system.bat`
2. 选择"完整模式"进行全面测试
3. 验证所有功能正常工作
4. 使用 `stop_system.bat` 清理环境

### 2. 日常开发
1. 使用 `quick_start.bat` 快速启动
2. 开发完成后使用 `stop_system.bat` 清理
3. 遇到问题时重新运行完整启动脚本

### 3. 生产部署
1. 使用 `start_system.bat` 选择"仅网关服务"
2. 配置为系统服务自动启动
3. 定期检查服务状态

### 4. 故障排除
1. 使用 `stop_system.bat` 完全清理环境
2. 重新运行 `start_system.bat` 进行诊断
3. 查看各服务窗口的错误信息

## 📞 技术支持

如果启动脚本遇到问题：

1. **查看错误信息**: 在命令行中运行脚本查看详细错误
2. **检查系统环境**: 确认Node.js和Flutter正确安装
3. **手动验证**: 尝试手动启动各个服务
4. **查看文档**: 参考 `START_GUIDE.md` 获取详细说明
5. **清理重试**: 使用 `stop_system.bat` 清理后重试

---

**启动脚本** - 让系统启动变得简单快捷 🚀
