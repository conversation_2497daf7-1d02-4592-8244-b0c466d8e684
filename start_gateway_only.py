#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flutter 系统 - 仅启动网关服务
版本: v1.0.0
"""

import os
import sys
import time
import subprocess
from pathlib import Path

def main():
    """仅启动网关服务"""
    project_root = Path(__file__).parent
    gateway_path = project_root / "gateway_service"
    
    print("\n" + "="*60)
    print("   Flutter 系统 - 网关服务启动器")
    print("="*60)
    
    # 检查项目结构
    print("\n[1/4] 检查项目结构...")
    if not gateway_path.exists():
        print("❌ gateway_service 目录不存在")
        return False
    
    gateway_js = gateway_path / "final_unified_gateway.js"
    if not gateway_js.exists():
        print("❌ final_unified_gateway.js 文件不存在")
        return False
    
    print("✓ 项目结构检查通过")
    
    # 检查Node.js
    print("\n[2/4] 检查 Node.js...")
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ Node.js {result.stdout.strip()}")
        else:
            print("❌ Node.js 不可用")
            return False
    except:
        print("❌ Node.js 未安装或不在PATH中")
        return False
    
    # 检查依赖
    print("\n[3/4] 检查网关服务依赖...")
    node_modules = gateway_path / "node_modules"
    if not node_modules.exists():
        print("📦 安装依赖...")
        try:
            subprocess.run(['npm', 'install'], cwd=gateway_path, 
                         check=True, timeout=120)
            print("✓ 依赖安装完成")
        except:
            print("❌ 依赖安装失败")
            return False
    else:
        print("✓ 依赖已存在")
    
    # 启动网关服务
    print("\n[4/4] 启动网关服务...")
    try:
        print("🚀 正在启动网关服务 (端口7777)...")
        print("📡 服务地址: http://localhost:7777")
        print("❤️ 健康检查: http://localhost:7777/health")
        print("🔗 WebSocket: ws://localhost:7777/terminal/ws")
        print("\n💡 提示: 按 Ctrl+C 停止服务")
        print("-" * 60)
        
        # 启动服务 (前台运行)
        subprocess.run(['node', 'final_unified_gateway.js'], 
                      cwd=gateway_path)
        
    except KeyboardInterrupt:
        print("\n\n⚠ 用户停止服务")
    except Exception as e:
        print(f"\n❌ 网关服务错误: {e}")
        return False
    
    print("\n✓ 网关服务已停止")
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 网关服务脚本执行完成！")
        else:
            print("\n💥 网关服务启动失败")
    except KeyboardInterrupt:
        print("\n\n⚠ 用户中断")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
    
    input("\n按 Enter 退出...")